package humanize

import (
	"faq-system/internal/logger"
	"math/rand"
	"regexp"
	"strings"
	"time"
)

// Processor 人性化处理器
type Processor struct {
	config           *ConversationalConfig
	patterns         []*ConversationalPattern
	emotionalContext *EmotionalContext
	rand             *rand.Rand
}

// NewProcessor 创建人性化处理器
func NewProcessor(config *ConversationalConfig) *Processor {
	if config == nil {
		config = DefaultConversationalConfig()
	}

	processor := &Processor{
		config:   config,
		patterns: loadDefaultPatterns(),
		emotionalContext: &EmotionalContext{
			UserEmotion:      "neutral",
			BotPersonality:   config.DefaultPersonality,
			ConversationTone: "friendly",
			LastInteraction:  time.Now(),
			InteractionCount: 0,
		},
		rand: rand.New(rand.NewSource(time.Now().UnixNano())),
	}

	logger.Infof("🗣️ 人性化处理器已初始化，加载了 %d 个对话模式", len(processor.patterns))
	return processor
}

// ProcessQuery 处理查询
func (p *Processor) ProcessQuery(query string) *HumanizedResponse {
	if !p.config.EnableHumanization {
		return &HumanizedResponse{
			OriginalQuery:  query,
			ShouldContinue: true,
			ProcessedAt:    time.Now(),
		}
	}

	// 更新情感上下文
	p.emotionalContext.LastInteraction = time.Now()
	p.emotionalContext.InteractionCount++

	// 检测是否是口语化表达
	pattern, confidence := p.detectConversationalPattern(query)
	if pattern == nil {
		// 不是口语化表达，继续正常处理
		return &HumanizedResponse{
			OriginalQuery:  query,
			ShouldContinue: true,
			ProcessedAt:    time.Now(),
		}
	}

	// 检查是否是续接请求
	if pattern.Category == "continuation" {
		// 续接请求需要特殊处理，让ChatSystem来处理
		return &HumanizedResponse{
			OriginalQuery:   query,
			DetectedPattern: pattern.Pattern,
			Intent:          pattern.Intent,
			Emotion:         pattern.Emotion,
			Response:        "", // 空回应，让ChatSystem处理
			ResponseType:    "continuation_request",
			Confidence:      confidence,
			ShouldContinue:  true, // 需要继续处理
			ProcessedAt:     time.Now(),
			Metadata: map[string]interface{}{
				"category":          pattern.Category,
				"interaction_count": p.emotionalContext.InteractionCount,
				"is_continuation":   true,
			},
		}
	}

	// 生成人性化回应
	response := p.generateResponse(pattern)

	logger.Infof("🗣️ 检测到口语化表达: '%s' -> 分类='%s', 意图='%s', 置信度=%.2f",
		query, pattern.Category, pattern.Intent, confidence)

	return &HumanizedResponse{
		OriginalQuery:   query,
		DetectedPattern: pattern.Pattern,
		Intent:          pattern.Intent,
		Emotion:         pattern.Emotion,
		Response:        response,
		ResponseType:    "conversational",
		Confidence:      confidence,
		ShouldContinue:  false, // 不需要继续处理
		ProcessedAt:     time.Now(),
		Metadata: map[string]interface{}{
			"category":          pattern.Category,
			"interaction_count": p.emotionalContext.InteractionCount,
		},
	}
}

// ProcessResponse 处理回应
func (p *Processor) ProcessResponse(response string) string {
	if !p.config.EnableHumanization {
		return response
	}

	// 添加人性化元素
	enhancedResponse := p.enhanceResponse(response)
	return enhancedResponse
}

// detectConversationalPattern 检测对话模式
func (p *Processor) detectConversationalPattern(query string) (*ConversationalPattern, float64) {
	normalizedQuery := strings.TrimSpace(strings.ToLower(query))

	// 如果查询太长，可能不是简单的口语化表达
	if len(normalizedQuery) > 15 {
		return nil, 0
	}

	bestMatch := (*ConversationalPattern)(nil)
	bestConfidence := 0.0

	for _, pattern := range p.patterns {
		confidence := p.matchPattern(normalizedQuery, pattern)
		if confidence > bestConfidence && confidence > 0.6 {
			bestMatch = pattern
			bestConfidence = confidence
		}
	}

	return bestMatch, bestConfidence
}

// matchPattern 匹配模式
func (p *Processor) matchPattern(query string, pattern *ConversationalPattern) float64 {
	// 精确匹配
	if query == pattern.Pattern {
		return 1.0
	}

	// 正则表达式匹配
	re, err := regexp.Compile(pattern.Pattern)
	if err == nil && re.MatchString(query) {
		return 0.9
	}

	// 关键词匹配
	for _, keyword := range pattern.Keywords {
		if strings.Contains(query, keyword) {
			return 0.8
		}
	}

	return 0.0
}

// generateResponse 生成回应
func (p *Processor) generateResponse(pattern *ConversationalPattern) string {
	if len(pattern.Responses) == 0 {
		return ""
	}

	// 随机选择一个回应
	index := p.rand.Intn(len(pattern.Responses))
	return pattern.Responses[index]
}

// enhanceResponse 增强回应
func (p *Processor) enhanceResponse(response string) string {
	// 如果回应已经很长，不做修改
	if len(response) > p.config.MaxResponseLength {
		return response
	}

	// 根据对话风格添加人性化元素
	style := p.config.ResponseStyle

	// 添加友好的开头
	if style.Friendly && p.rand.Float64() < 0.3 {
		friendlyIntros := []string{
			"嗯，", "好的，", "这样啊，", "我明白了，", "了解，",
		}
		index := p.rand.Intn(len(friendlyIntros))
		response = friendlyIntros[index] + response
	}

	// 添加共情元素
	if style.Empathetic && p.rand.Float64() < 0.2 {
		empathyPhrases := []string{
			"我理解您的意思，", "从您的角度来看，", "考虑到您的需求，",
		}
		index := p.rand.Intn(len(empathyPhrases))
		if !strings.Contains(response, empathyPhrases[index]) {
			response = empathyPhrases[index] + response
		}
	}

	return response
}

// AddPattern 添加对话模式
func (p *Processor) AddPattern(pattern *ConversationalPattern) {
	p.patterns = append(p.patterns, pattern)
}

// SetEmotionalContext 设置情感上下文
func (p *Processor) SetEmotionalContext(context *EmotionalContext) {
	p.emotionalContext = context
}

// GetEmotionalContext 获取情感上下文
func (p *Processor) GetEmotionalContext() *EmotionalContext {
	return p.emotionalContext
}

// UpdateConfig 更新配置
func (p *Processor) UpdateConfig(config *ConversationalConfig) {
	p.config = config
}

// GetConfig 获取配置
func (p *Processor) GetConfig() *ConversationalConfig {
	return p.config
}

// loadDefaultPatterns 加载默认对话模式
func loadDefaultPatterns() []*ConversationalPattern {
	return []*ConversationalPattern{
		// 确认类
		{
			Pattern:    "哦",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"嗯嗯，有什么我可以帮您的吗？", "好的，请问您需要了解什么？", "我在呢，有什么问题尽管问！"},
			Confidence: 0.9,
			Keywords:   []string{"哦"},
			Emotion:    "neutral",
		},
		{
			Pattern:    "哦哦",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"是的呢，还有什么想了解的吗？", "嗯嗯，我随时为您服务！", "好的好的，请继续提问吧！"},
			Confidence: 0.9,
			Keywords:   []string{"哦哦"},
			Emotion:    "friendly",
		},
		{
			Pattern:    "嗯",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"嗯，有什么需要帮助的吗？", "好的，我在听呢！", "是的，请说！"},
			Confidence: 0.8,
			Keywords:   []string{"嗯"},
			Emotion:    "neutral",
		},
		{
			Pattern:    "嗯嗯",
			Category:   "confirmation",
			Intent:     "acknowledge",
			Responses:  []string{"好的好的，还有什么问题吗？", "嗯嗯，我明白了！", "是的呢，继续吧！"},
			Confidence: 0.9,
			Keywords:   []string{"嗯嗯"},
			Emotion:    "friendly",
		},

		// 反问类
		{
			Pattern:    "不然呢",
			Category:   "rhetorical",
			Intent:     "rhetorical_question",
			Responses:  []string{"哈哈，您说得对！还有其他想了解的吗？", "确实如此呢！有什么其他问题吗？", "您说得很有道理！还需要什么帮助？"},
			Confidence: 0.95,
			Keywords:   []string{"不然呢", "不然"},
			Emotion:    "agreeable",
		},
		{
			Pattern:    "还能怎么样",
			Category:   "rhetorical",
			Intent:     "rhetorical_question",
			Responses:  []string{"是啊，就是这样的！有什么其他需要了解的吗？", "没错呢！还有什么问题吗？", "确实如此！我还能为您做些什么？"},
			Confidence: 0.9,
			Keywords:   []string{"还能怎么样", "怎么样"},
			Emotion:    "agreeable",
		},

		// 肯定类
		{
			Pattern:    "当然啦",
			Category:   "affirmation",
			Intent:     "agreement",
			Responses:  []string{"对呀对呀！还有什么想知道的吗？", "就是这样！有其他问题尽管问！", "没错呢！我很乐意继续帮助您！"},
			Confidence: 0.95,
			Keywords:   []string{"当然啦", "当然"},
			Emotion:    "enthusiastic",
		},
		{
			Pattern:    "对啊",
			Category:   "affirmation",
			Intent:     "agreement",
			Responses:  []string{"是的是的！还有什么需要了解的？", "对呀！有其他问题吗？", "没错！我随时为您服务！"},
			Confidence: 0.9,
			Keywords:   []string{"对啊", "对呀"},
			Emotion:    "agreeable",
		},
		{
			Pattern:    "就是",
			Category:   "affirmation",
			Intent:     "agreement",
			Responses:  []string{"对的对的！还有什么想问的吗？", "就是这样！有其他需要帮助的地方吗？", "没错呢！继续提问吧！"},
			Confidence: 0.8,
			Keywords:   []string{"就是"},
			Emotion:    "agreeable",
		},

		// 疑问类
		{
			Pattern:    "啊？",
			Category:   "confusion",
			Intent:     "clarification",
			Responses:  []string{"有什么不清楚的地方吗？我来详细解释一下！", "需要我再说明一下吗？", "有疑问的话请尽管问，我会耐心解答的！"},
			Confidence: 0.9,
			Keywords:   []string{"啊？", "啊"},
			Emotion:    "confused",
		},
		{
			Pattern:    "什么？",
			Category:   "confusion",
			Intent:     "clarification",
			Responses:  []string{"请问有什么不明白的地方吗？", "需要我重新解释一下吗？", "有什么疑问请告诉我，我来帮您解答！"},
			Confidence: 0.9,
			Keywords:   []string{"什么？"},
			Emotion:    "confused",
		},

		// 感叹类
		{
			Pattern:    "哇",
			Category:   "exclamation",
			Intent:     "surprise",
			Responses:  []string{"是不是很有趣呢？还想了解更多吗？", "对吧！还有什么想知道的？", "很神奇对不对！有其他问题吗？"},
			Confidence: 0.9,
			Keywords:   []string{"哇", "哇塞"},
			Emotion:    "excited",
		},
		{
			Pattern:    "厉害",
			Category:   "praise",
			Intent:     "compliment",
			Responses:  []string{"谢谢夸奖！还有什么我可以帮您的？", "很高兴能帮到您！有其他问题吗？", "您过奖了！还需要了解什么？"},
			Confidence: 0.9,
			Keywords:   []string{"厉害", "牛"},
			Emotion:    "pleased",
		},

		// 续接类
		{
			Pattern:    "请继续",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"好的，让我继续为您详细说明...", "当然，我来接着讲...", "没问题，继续为您介绍..."},
			Confidence: 0.98,
			Keywords:   []string{"请继续", "继续"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "继续",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"好的，接下来...", "继续说...", "那么接着..."},
			Confidence: 0.95,
			Keywords:   []string{"继续"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "还有呢",
			Category:   "continuation",
			Intent:     "more_info",
			Responses:  []string{"还有很多呢，让我继续告诉您...", "当然还有，接下来...", "是的，还有更多内容..."},
			Confidence: 0.95,
			Keywords:   []string{"还有呢", "还有"},
			Emotion:    "enthusiastic",
		},
		{
			Pattern:    "还有",
			Category:   "continuation",
			Intent:     "more_info",
			Responses:  []string{"还有...", "另外...", "此外..."},
			Confidence: 0.9,
			Keywords:   []string{"还有"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "然后呢",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"然后...", "接下来...", "下一步..."},
			Confidence: 0.95,
			Keywords:   []string{"然后呢", "然后"},
			Emotion:    "curious",
		},
		{
			Pattern:    "接下来",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"接下来...", "下面...", "然后..."},
			Confidence: 0.9,
			Keywords:   []string{"接下来"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "下面呢",
			Category:   "continuation",
			Intent:     "continue",
			Responses:  []string{"下面...", "接着...", "然后..."},
			Confidence: 0.9,
			Keywords:   []string{"下面呢", "下面"},
			Emotion:    "curious",
		},

		// 礼貌用语
		{
			Pattern:    "谢谢",
			Category:   "gratitude",
			Intent:     "thanks",
			Responses:  []string{"不客气！很高兴能帮到您！", "不用谢！还有什么需要帮助的吗？", "我的荣幸！有其他问题随时问我！"},
			Confidence: 0.95,
			Keywords:   []string{"谢谢", "感谢"},
			Emotion:    "grateful",
		},
		{
			Pattern:    "再见",
			Category:   "farewell",
			Intent:     "goodbye",
			Responses:  []string{"再见！有问题随时来找我哦！", "拜拜！期待下次为您服务！", "再见！祝您一切顺利！"},
			Confidence: 0.95,
			Keywords:   []string{"再见", "拜拜", "88"},
			Emotion:    "friendly",
		},

		// 能力询问类
		{
			Pattern:    "你会什么",
			Category:   "capability_inquiry",
			Intent:     "ask_capability",
			Responses:  []string{"我会很多呢！比如回答技术问题、聊天对话、算法计算等，您想了解哪方面？", "我的能力挺丰富的！技术问答、智能对话、知识学习都可以，有什么想试试的吗？"},
			Confidence: 0.95,
			Keywords:   []string{"你会什么", "你能做什么", "你的能力"},
			Emotion:    "confident",
		},
		{
			Pattern:    "你能干嘛",
			Category:   "capability_inquiry",
			Intent:     "ask_capability",
			Responses:  []string{"我能做的事情可多了！技术问答、编程帮助、算法计算、知识学习，您想试试哪个？", "我可以帮您解答技术问题、进行智能对话、处理算法计算等，有什么需要帮助的吗？"},
			Confidence: 0.9,
			Keywords:   []string{"你能干嘛", "你能干什么", "你可以做什么"},
			Emotion:    "helpful",
		},

		// 请求发言类
		{
			Pattern:    "你说",
			Category:   "request_speak",
			Intent:     "ask_to_speak",
			Responses:  []string{"好的，我来说说！您想听我聊什么话题呢？", "当然可以！请告诉我您想了解什么，我来为您详细介绍！", "没问题！您希望我谈论哪个方面？"},
			Confidence: 0.9,
			Keywords:   []string{"你说", "你讲", "你来说"},
			Emotion:    "cooperative",
		},
		{
			Pattern:    "你请说",
			Category:   "request_speak",
			Intent:     "ask_to_speak",
			Responses:  []string{"好的，很荣幸为您分享！请问您想了解什么内容？", "当然！我很乐意为您介绍，您对哪个话题感兴趣？", "没问题！请告诉我您希望我讲解什么？"},
			Confidence: 0.95,
			Keywords:   []string{"你请说", "请说", "请讲"},
			Emotion:    "polite",
		},

		// 教学请求类
		{
			Pattern:    "让我告诉你",
			Category:   "teaching_request",
			Intent:     "want_to_teach",
			Responses:  []string{"太好了！我很乐意向您学习，请开始您的教学吧！", "非常感谢！我已经准备好学习了，请告诉我！", "这太棒了！我会认真学习您要教我的内容！"},
			Confidence: 0.95,
			Keywords:   []string{"让我告诉你", "我来告诉你", "我教你"},
			Emotion:    "eager",
		},
		{
			Pattern:    "我跟你说",
			Category:   "teaching_request",
			Intent:     "want_to_share",
			Responses:  []string{"好的，我在认真听呢！请说吧！", "我全神贯注地在听，请继续！", "请说，我会仔细记住您说的每一句话！"},
			Confidence: 0.9,
			Keywords:   []string{"我跟你说", "我告诉你", "听我说"},
			Emotion:    "attentive",
		},

		// 求助类
		{
			Pattern:    "帮我",
			Category:   "request_help",
			Intent:     "ask_for_help",
			Responses:  []string{"当然！我很乐意帮助您，请告诉我您需要什么帮助？", "没问题！请详细说明您遇到的问题，我来协助您解决！", "我随时为您服务！请说明您的具体需求。"},
			Confidence: 0.95,
			Keywords:   []string{"帮我", "帮助我", "协助我"},
			Emotion:    "helpful",
		},
		{
			Pattern:    "求助",
			Category:   "request_help",
			Intent:     "emergency_help",
			Responses:  []string{"我收到您的求助信号！请详细描述您遇到的问题，我会全力帮助您！", "别担心，我来帮您！请告诉我具体情况。", "我马上来帮您！请说明您需要什么帮助。"},
			Confidence: 0.98,
			Keywords:   []string{"求助", "救命", "help"},
			Emotion:    "urgent",
		},

		// 否定类
		{
			Pattern:    "不对",
			Category:   "negation",
			Intent:     "disagree",
			Responses:  []string{"哦，看来我理解错了，请您指正一下！", "抱歉我说错了，请告诉我正确的信息！", "谢谢您的纠正，请详细说明一下！"},
			Confidence: 0.9,
			Keywords:   []string{"不对", "不是", "错了"},
			Emotion:    "apologetic",
		},
		{
			Pattern:    "不懂",
			Category:   "negation",
			Intent:     "dont_understand",
			Responses:  []string{"没关系！让我来为您详细解释，您想了解什么？", "这很正常，我来帮您理解！请告诉我哪里不清楚？", "我很乐意为您解答！请说明您的疑问。"},
			Confidence: 0.9,
			Keywords:   []string{"不懂", "不明白", "不理解"},
			Emotion:    "patient",
		},

		// 日常口语类
		{
			Pattern:    "算了",
			Category:   "dismissal",
			Intent:     "give_up",
			Responses:  []string{"别放弃呀！也许我可以帮您找到更好的解决方案？", "等等，让我再试试帮您解决这个问题！", "不要气馁，我们一起想想办法！"},
			Confidence: 0.85,
			Keywords:   []string{"算了", "不用了", "放弃"},
			Emotion:    "encouraging",
		},
		{
			Pattern:    "随便",
			Category:   "casual",
			Intent:     "casual_response",
			Responses:  []string{"哈哈，那我就随便聊聊啦！您对什么话题感兴趣？", "好的，那我们轻松聊天吧！有什么想聊的吗？", "随便聊聊也很好呀！您想听什么？"},
			Confidence: 0.8,
			Keywords:   []string{"随便", "无所谓", "都行"},
			Emotion:    "casual",
		},

		// 情感表达类
		{
			Pattern:    "开心",
			Category:   "emotion",
			Intent:     "express_happiness",
			Responses:  []string{"太好了！看到您开心我也很高兴！", "您的好心情感染了我！有什么开心的事情吗？", "真棒！保持这种好心情哦！"},
			Confidence: 0.9,
			Keywords:   []string{"开心", "高兴", "快乐"},
			Emotion:    "happy",
		},
		{
			Pattern:    "郁闷",
			Category:   "emotion",
			Intent:     "express_sadness",
			Responses:  []string{"怎么了？有什么烦心事可以跟我聊聊。", "别郁闷啦，也许我能帮您分析分析？", "心情不好的时候聊聊天会好一些，我陪您！"},
			Confidence: 0.9,
			Keywords:   []string{"郁闷", "烦", "不开心"},
			Emotion:    "sympathetic",
		},

		// 身份询问类
		{
			Pattern:    "你是谁",
			Category:   "identity_inquiry",
			Intent:     "ask_identity",
			Responses:  []string{"我是您的智能FAQ技术助手！很高兴认识您！", "我是专业的技术问答助手，随时为您服务！", "我是AI技术助手，专门帮您解决技术问题！"},
			Confidence: 0.98,
			Keywords:   []string{"你是谁", "你叫什么", "你的名字"},
			Emotion:    "friendly",
		},
		{
			Pattern:    "你是机器人吗",
			Category:   "identity_inquiry",
			Intent:     "ask_if_robot",
			Responses:  []string{"是的，我是AI智能助手！不过我很友好哦～", "对，我是人工智能，但我会像朋友一样帮助您！", "没错，我是AI机器人，专门为您提供技术支持！"},
			Confidence: 0.95,
			Keywords:   []string{"机器人", "AI", "人工智能", "程序"},
			Emotion:    "honest",
		},
		{
			Pattern:    "你是真人吗",
			Category:   "identity_inquiry",
			Intent:     "ask_if_human",
			Responses:  []string{"不是哦，我是AI助手，但我会用心为您服务！", "我不是真人，但我的帮助是真诚的！", "我是人工智能，虽然不是人类，但我很乐意帮助您！"},
			Confidence: 0.95,
			Keywords:   []string{"真人", "人类", "真的人"},
			Emotion:    "honest",
		},
		{
			Pattern:    "介绍一下自己",
			Category:   "identity_inquiry",
			Intent:     "self_introduction",
			Responses:  []string{"很高兴为您介绍！我是智能FAQ技术助手，专业、友好、随时在线！", "让我自我介绍一下：我是您的专属技术顾问，什么问题都可以问我！", "我是AI技术助手，擅长解答各种技术问题，很高兴为您服务！"},
			Confidence: 0.95,
			Keywords:   []string{"介绍", "自我介绍", "认识一下"},
			Emotion:    "professional",
		},
		{
			Pattern:    "你从哪来",
			Category:   "identity_inquiry",
			Intent:     "ask_origin",
			Responses:  []string{"我来自技术的世界！由专业团队开发，专门为您提供技术支持！", "我诞生于代码和算法中，目标是成为您最好的技术伙伴！", "我来自AI技术领域，带着满满的知识来帮助您！"},
			Confidence: 0.9,
			Keywords:   []string{"从哪来", "来自哪里", "哪里来的"},
			Emotion:    "mysterious",
		},
		{
			Pattern:    "谁创造了你",
			Category:   "identity_inquiry",
			Intent:     "ask_creator",
			Responses:  []string{"我是由专业的技术团队精心开发的！他们希望我能成为您最好的技术助手！", "感谢我的开发者们！他们赋予了我帮助您解决技术问题的能力！", "我的创造者是一群热爱技术的工程师，他们希望我能让技术学习变得更简单！"},
			Confidence: 0.9,
			Keywords:   []string{"谁创造", "开发者", "制作者", "创造者"},
			Emotion:    "grateful",
		},
	}
}
