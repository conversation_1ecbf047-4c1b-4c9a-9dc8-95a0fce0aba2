package server

import (
	"context"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"os"
	"strconv"
	"sync"
	"time"

	"faq-system/internal/config"
	"faq-system/internal/crawler"
	"faq-system/internal/health"
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/rag"
	"faq-system/internal/server/handlers"
	"faq-system/internal/server/templates"
	"faq-system/internal/static"

	"github.com/gin-gonic/gin"
	"github.com/gorilla/websocket"
)

// Server Web服务器
type Server struct {
	config          *config.Config
	engine          *gin.Engine
	ragSystem       *rag.ChatSystem
	healthChecker   *health.Checker
	httpServer      *http.Server
	learningManager *learning.Manager
	crawler         *crawler.KnowledgeCrawler
}

// New 创建新的Web服务器
func New(cfg *config.Config, ragSystem *rag.ChatSystem, healthChecker *health.Checker, learningManager *learning.Manager, knowledgeCrawler *crawler.KnowledgeCrawler) (*Server, error) {
	// 设置Gin模式
	if cfg.Server.Host != "0.0.0.0" {
		gin.SetMode(gin.ReleaseMode)
	}

	server := &Server{
		config:          cfg,
		engine:          gin.Default(),
		ragSystem:       ragSystem,
		healthChecker:   healthChecker,
		learningManager: learningManager,
		crawler:         knowledgeCrawler,
	}

	// 设置路由
	server.setupRoutes()

	return server, nil
}

// 注意：静态文件现在使用嵌入方式，不再需要路径检测函数

// setupRoutes 设置路由
func (s *Server) setupRoutes() {
	// 创建处理器
	h := handlers.New(s.ragSystem, s.healthChecker)
	kh := NewKnowledgeHandler(s.ragSystem)

	// 主页 - 智能问答界面
	s.engine.GET("/", func(c *gin.Context) {
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(http.StatusOK, templates.GetIndexHTML())
	})

	// 健康检查
	s.engine.GET("/health", h.HealthCheck)

	// 问答接口
	s.engine.POST("/ask", h.AskQuestion)

	// 测试页面
	s.engine.GET("/test", func(c *gin.Context) {
		c.Header("Content-Type", "text/html; charset=utf-8")
		c.String(http.StatusOK, templates.GetTestHTML())
	})

	// API路由组
	api := s.engine.Group("/api/v1")
	{
		api.GET("/health", h.HealthCheck)
		api.POST("/ask", h.AskQuestion)
		api.GET("/stats", h.GetStats)
	}

	// 学习系统API路由
	if s.learningManager != nil {
		learningAPI := s.learningManager.GetAPI()
		learning := s.engine.Group("/api/learning")
		{
			learning.POST("/feedback", gin.WrapF(learningAPI.HandleFeedback))
			learning.POST("/behavior", gin.WrapF(learningAPI.HandleBehavior))
			learning.GET("/metrics", gin.WrapF(learningAPI.HandleMetrics))
			learning.GET("/performance", gin.WrapF(learningAPI.HandlePerformanceStats))
			learning.GET("/activity", gin.WrapF(learningAPI.HandleRecentActivity))
			learning.GET("/recommendations", gin.WrapF(learningAPI.HandleOptimizationRecommendations))
			learning.POST("/analyze", gin.WrapF(learningAPI.HandleLearningAnalysis))
			learning.GET("/config", gin.WrapF(learningAPI.HandleLearningConfig))
			learning.PUT("/config", gin.WrapF(learningAPI.HandleLearningConfig))

			// 知识管理API
			learning.POST("/knowledge", s.createKnowledge)
			learning.GET("/knowledge", s.getKnowledgeList)
			learning.GET("/knowledge/:id", s.getKnowledgeDetail)
			learning.PUT("/knowledge/:id", s.updateKnowledge)
			learning.GET("/knowledge/:id/related", s.getRelatedKnowledge)
			learning.GET("/topics", s.getKnowledgeTopics)
			learning.GET("/statistics", s.getKnowledgeStatistics)
			learning.PUT("/knowledge/:id/approve", s.approveKnowledge)
			learning.DELETE("/knowledge/:id", s.deleteKnowledge)

			// 进化改进API - 暂时禁用
			// learning.POST("/evolution/apply", s.handleApplyEvolution)
		}
	}

	// 知识学习API路由
	knowledge := s.engine.Group("/api/knowledge")
	{
		knowledge.POST("/teach", kh.TeachKnowledge)
		knowledge.GET("/search", kh.SearchKnowledge)
		knowledge.GET("/list", kh.GetLearnedKnowledge)
		knowledge.GET("/pending", kh.GetPendingKnowledge)
		knowledge.POST("/:id/approve", kh.ApproveKnowledge)
		knowledge.POST("/:id/feedback", kh.FeedbackKnowledge)
		knowledge.GET("/stats", kh.GetKnowledgeStats)
	}

	// 爬虫系统API路由
	if s.crawler != nil {
		// 手动注册爬虫路由
		crawlerGroup := s.engine.Group("/api/crawler")
		{
			// 爬虫管理
			crawlerGroup.POST("/start", s.startCrawler)
			crawlerGroup.POST("/stop", s.stopCrawler)
			crawlerGroup.GET("/status", s.getCrawlerStatus)

			// 目标管理
			crawlerGroup.GET("/targets", s.getTargets)
			crawlerGroup.POST("/targets", s.addTarget)
			crawlerGroup.GET("/targets/:id", s.getTarget)
			crawlerGroup.PUT("/targets/:id", s.updateTarget)
			crawlerGroup.DELETE("/targets/:id", s.deleteTarget)
			crawlerGroup.POST("/targets/:id/crawl", s.manualCrawl)
			crawlerGroup.POST("/targets/:id/enable", s.enableTarget)
			crawlerGroup.POST("/targets/:id/disable", s.disableTarget)

			// 结果查看
			crawlerGroup.GET("/results", s.getResults)
			crawlerGroup.GET("/statistics", s.getStatistics)
			crawlerGroup.GET("/logs", s.getLogs)
		}
		logger.Info("✅ 爬虫API路由已启用")
	}

	// WebSocket路由 - 实时日志
	s.engine.GET("/ws/logs", s.handleWebSocketLogs)

	// 静态文件服务 - 使用嵌入的静态文件
	logger.Info("🌐 使用嵌入的静态文件系统")

	// 设置嵌入的静态文件目录
	s.engine.GET("/static/*filepath", static.ServeEmbeddedStatic())

	// 使用嵌入文件处理器注册特定的HTML文件
	dashboardFiles := map[string]string{
		"/learning_dashboard.html": "learning_dashboard.html",
		"/crawler_dashboard.html":  "crawler_dashboard.html",
		"/knowledge_manager.html":  "knowledge_manager.html",
	}

	for route, fileName := range dashboardFiles {
		s.engine.GET(route, static.ServeEmbeddedFile(fileName))
		logger.Infof("✅ 注册嵌入仪表板文件: %s -> %s", route, fileName)
	}

	// 测试页面文件路径处理
	testFiles := []string{
		"test_vector_generation.html",
		"test_knowledge_topics.html",
		"test_related_knowledge_recommendations.html",
	}

	for _, testFile := range testFiles {
		// 尝试多个可能的路径
		testPaths := []string{
			"./" + testFile,
			testFile,
		}

		var foundPath string
		for _, testPath := range testPaths {
			if _, err := os.Stat(testPath); err == nil {
				foundPath = testPath
				break
			}
		}

		if foundPath != "" {
			s.engine.GET("/"+testFile, func(c *gin.Context) {
				c.File(foundPath)
			})
			logger.Infof("✅ 注册测试文件: /%s -> %s", testFile, foundPath)
		} else {
			logger.Warnf("⚠️ 测试文件未找到: %s", testFile)
		}
	}

	// 初始化WebSocket日志广播
	s.initializeWebSocketLogging()
}

// WebSocket升级器
var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool {
		return true // 允许所有来源，生产环境应该更严格
	},
}

// WebSocket客户端管理
var (
	wsClients      = make(map[*websocket.Conn]bool)
	wsClientsMutex sync.RWMutex
	wsBroadcast    = make(chan []byte, 256)
)

// handleWebSocketLogs 处理WebSocket日志连接
func (s *Server) handleWebSocketLogs(c *gin.Context) {
	// 升级HTTP连接为WebSocket
	conn, err := upgrader.Upgrade(c.Writer, c.Request, nil)
	if err != nil {
		log.Printf("❌ WebSocket升级失败: %v", err)
		return
	}
	defer conn.Close()

	// 注册客户端
	wsClientsMutex.Lock()
	wsClients[conn] = true
	wsClientsMutex.Unlock()

	// 清理函数
	defer func() {
		wsClientsMutex.Lock()
		delete(wsClients, conn)
		wsClientsMutex.Unlock()
	}()

	// 发送连接成功消息
	welcomeMsg := map[string]interface{}{
		"level":     "success",
		"icon":      "📡",
		"message":   "WebSocket连接已建立，开始接收实时日志",
		"timestamp": time.Now().Format("2006-01-02T15:04:05Z07:00"),
	}
	if data, err := json.Marshal(welcomeMsg); err == nil {
		conn.WriteMessage(websocket.TextMessage, data)
	}

	// 保持连接活跃
	for {
		// 读取客户端消息（主要用于保持连接）
		_, _, err := conn.ReadMessage()
		if err != nil {
			log.Printf("WebSocket连接断开: %v", err)
			break
		}
	}
}

// 启动WebSocket广播处理器
func init() {
	go handleWebSocketBroadcast()
}

// handleWebSocketBroadcast 处理WebSocket广播
func handleWebSocketBroadcast() {
	for {
		select {
		case message := <-wsBroadcast:
			wsClientsMutex.Lock()
			for conn := range wsClients {
				err := conn.WriteMessage(websocket.TextMessage, message)
				if err != nil {
					log.Printf("WebSocket发送消息失败: %v", err)
					conn.Close()
					delete(wsClients, conn)
				}
			}
			wsClientsMutex.Unlock()
		}
	}
}

// initializeWebSocketLogging 初始化WebSocket日志广播
func (s *Server) initializeWebSocketLogging() {
	crawlerLogger := logger.GetCrawlerLogger()

	// 设置广播函数
	crawlerLogger.SetBroadcastFunc(func(entry *logger.LogEntry) {
		// 序列化日志条目
		data, err := json.Marshal(map[string]interface{}{
			"timestamp": entry.Timestamp.Format("2006-01-02T15:04:05Z07:00"),
			"level":     entry.Level,
			"icon":      entry.Icon,
			"message":   entry.Message,
			"metadata":  entry.Metadata,
		})
		if err != nil {
			log.Printf("❌ 序列化日志消息失败: %v", err)
			return
		}

		// 广播给所有WebSocket客户端
		select {
		case wsBroadcast <- data:
		default:
			// 广播通道满了，跳过这条消息
		}
	})

	logger.Info("✅ WebSocket日志广播已初始化")
}

// Start 启动服务器
func (s *Server) Start() error {
	address := s.config.GetServerAddress()

	s.httpServer = &http.Server{
		Addr:         address,
		Handler:      s.engine,
		ReadTimeout:  30 * time.Second,
		WriteTimeout: 30 * time.Second,
		IdleTimeout:  60 * time.Second,
	}

	logger.Infof("Server starting on %s", address)
	return s.httpServer.ListenAndServe()
}

// Shutdown 关闭服务器
func (s *Server) Shutdown() error {
	ctx, cancel := context.WithTimeout(context.Background(), 10*time.Second)
	defer cancel()

	logger.Info("Shutting down server...")
	return s.httpServer.Shutdown(ctx)
}

// 爬虫API处理器方法

// startCrawler 启动爬虫
func (s *Server) startCrawler(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	if err := s.crawler.Start(); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬虫启动成功"})
}

// stopCrawler 停止爬虫
func (s *Server) stopCrawler(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	s.crawler.Stop()
	c.JSON(200, gin.H{"success": true, "message": "爬虫停止成功"})
}

// getCrawlerStatus 获取爬虫状态
func (s *Server) getCrawlerStatus(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	targets := s.crawler.GetTargets()
	activeCrawls := s.crawler.GetActiveCrawls()

	activeTargets := 0
	for _, target := range targets {
		if target.Enabled {
			activeTargets++
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"running":        s.crawler.IsRunning(),
			"total_targets":  len(targets),
			"active_targets": activeTargets,
			"crawling_now":   len(activeCrawls),
			"active_crawls":  activeCrawls,
		},
	})
}

// getTargets 获取所有爬取目标
func (s *Server) getTargets(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	targets := s.crawler.GetTargets()
	c.JSON(200, gin.H{"success": true, "data": targets, "total": len(targets)})
}

// addTarget 添加爬取目标
func (s *Server) addTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	var target crawler.CrawlTarget
	if err := c.ShouldBindJSON(&target); err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的JSON数据"})
		return
	}

	if err := s.crawler.AddTarget(&target); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬取目标添加成功", "data": target})
}

// getTarget 获取单个爬取目标
func (s *Server) getTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targets := s.crawler.GetTargets()

	for _, target := range targets {
		if fmt.Sprintf("%d", target.ID) == id {
			c.JSON(200, gin.H{"success": true, "data": target})
			return
		}
	}

	c.JSON(404, gin.H{"success": false, "message": "目标不存在"})
}

// updateTarget 更新爬取目标
func (s *Server) updateTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	var target crawler.CrawlTarget
	if err := c.ShouldBindJSON(&target); err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的JSON数据"})
		return
	}

	// 设置ID
	if targetID, err := strconv.Atoi(id); err == nil {
		target.ID = targetID
	} else {
		c.JSON(400, gin.H{"success": false, "message": "无效的ID"})
		return
	}

	if err := s.crawler.UpdateTarget(&target); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬取目标更新成功", "data": target})
}

// deleteTarget 删除爬取目标
func (s *Server) deleteTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的ID"})
		return
	}

	if err := s.crawler.RemoveTarget(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "爬取目标删除成功"})
}

// manualCrawl 手动触发爬取
func (s *Server) manualCrawl(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的ID"})
		return
	}

	if err := s.crawler.ManualCrawl(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "手动爬取任务已启动"})
}

// enableTarget 启用目标
func (s *Server) enableTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的目标ID"})
		return
	}

	if err := s.crawler.EnableTarget(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "目标启用成功"})
}

// disableTarget 禁用目标
func (s *Server) disableTarget(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	id := c.Param("id")
	targetID, err := strconv.Atoi(id)
	if err != nil {
		c.JSON(400, gin.H{"success": false, "message": "无效的目标ID"})
		return
	}

	if err := s.crawler.DisableTarget(targetID); err != nil {
		c.JSON(500, gin.H{"success": false, "message": err.Error()})
		return
	}

	c.JSON(200, gin.H{"success": true, "message": "目标禁用成功"})
}

// getResults 获取爬取结果
func (s *Server) getResults(c *gin.Context) {
	c.JSON(200, gin.H{"success": true, "data": []interface{}{}, "total": 0})
}

// getStatistics 获取爬取统计
func (s *Server) getStatistics(c *gin.Context) {
	if s.crawler == nil {
		c.JSON(500, gin.H{"success": false, "message": "爬虫系统未初始化"})
		return
	}

	targets := s.crawler.GetTargets()
	activeTargets := 0
	for _, target := range targets {
		if target.Enabled {
			activeTargets++
		}
	}

	c.JSON(200, gin.H{
		"success": true,
		"data": gin.H{
			"total_targets":     len(targets),
			"active_targets":    activeTargets,
			"total_crawls":      0,
			"successful_crawls": 0,
			"failed_crawls":     0,
		},
	})
}

// getLogs 获取爬取日志
func (s *Server) getLogs(c *gin.Context) {
	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "100")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 100
	}
	if limit > 1000 {
		limit = 1000 // 最大限制
	}

	// 获取日志数据
	logs := s.getRecentLogs(limit)

	c.JSON(200, gin.H{
		"success": true,
		"data":    logs,
		"total":   len(logs),
	})
}

// getRecentLogs 获取最近的日志
func (s *Server) getRecentLogs(limit int) []map[string]interface{} {
	// 从日志收集器获取实际日志
	crawlerLogger := logger.GetCrawlerLogger()
	logEntries := crawlerLogger.GetRecentLogs(limit)

	// 转换为API响应格式
	logs := make([]map[string]interface{}, len(logEntries))
	for i, entry := range logEntries {
		logs[i] = map[string]interface{}{
			"timestamp": entry.Timestamp.Format("2006-01-02T15:04:05Z07:00"), // ISO 8601格式
			"level":     entry.Level,
			"icon":      entry.Icon,
			"message":   entry.Message,
			"metadata":  entry.Metadata,
		}
	}

	// 如果没有实际日志，返回一些示例数据
	if len(logs) == 0 {
		logs = []map[string]interface{}{
			{
				"timestamp": time.Now().Add(-5 * time.Minute).Format("15:04:05"),
				"level":     "info",
				"icon":      "🚀",
				"message":   "爬虫系统启动成功",
			},
			{
				"timestamp": time.Now().Add(-4 * time.Minute).Format("15:04:05"),
				"level":     "info",
				"icon":      "🕷️",
				"message":   "[第0层] 开始爬取: https://top.baidu.com/api/board?platform=pc&sa=pcindex_entry",
			},
			{
				"timestamp": time.Now().Add(-3 * time.Minute).Format("15:04:05"),
				"level":     "info",
				"icon":      "🌐",
				"message":   "检测到需要JS渲染的网站: api",
			},
			{
				"timestamp": time.Now().Add(-2 * time.Minute).Format("15:04:05"),
				"level":     "info",
				"icon":      "📊",
				"message":   "链接处理结果: 发现139个 → 过滤19个 → 添加19个到队列",
			},
			{
				"timestamp": time.Now().Add(-1 * time.Minute).Format("15:04:05"),
				"level":     "success",
				"icon":      "✅",
				"message":   "知识保存成功: 当前有哪些热门话题？",
			},
		}
	}

	return logs
}

// ==================== 知识管理API ====================

// createKnowledge 创建新知识
func (s *Server) createKnowledge(c *gin.Context) {
	var req struct {
		Question       string   `json:"question" binding:"required"`
		Answer         string   `json:"answer" binding:"required"`
		Category       string   `json:"category"`
		Keywords       []string `json:"keywords"`
		Confidence     float32  `json:"confidence"`
		Source         string   `json:"source"`
		LearnedFrom    string   `json:"learned_from"`
		KnowledgeTopic string   `json:"knowledge_topic"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 创建知识对象
	knowledge := &learning.LearnedKnowledge{
		Question:       req.Question,
		Answer:         req.Answer,
		Category:       req.Category,
		Keywords:       req.Keywords,
		Confidence:     req.Confidence,
		Source:         req.Source,
		LearnedFrom:    req.LearnedFrom,
		KnowledgeTopic: req.KnowledgeTopic,
		Status:         "approved", // 手动添加的知识直接批准
	}

	// 保存知识
	if err := knowledgeLearner.SaveKnowledge(knowledge); err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "保存知识失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "知识保存成功",
		"data": gin.H{
			"id":       knowledge.ID,
			"question": knowledge.Question,
			"answer":   knowledge.Answer,
		},
	})
}

// getKnowledgeList 获取知识列表
func (s *Server) getKnowledgeList(c *gin.Context) {
	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "20")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 20
	}
	if limit > 100 {
		limit = 100 // 最大限制
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 获取知识列表
	knowledgeList, err := knowledgeLearner.GetRecentKnowledge(limit)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "获取知识列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    knowledgeList,
		"total":   len(knowledgeList),
	})
}

// getKnowledgeStatistics 获取知识统计信息
func (s *Server) getKnowledgeStatistics(c *gin.Context) {
	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 获取统计信息
	stats, err := knowledgeLearner.GetStatistics()
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "获取统计信息失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    stats,
	})
}

// approveKnowledge 批准知识
func (s *Server) approveKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的知识ID",
		})
		return
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 批准知识
	if err := knowledgeLearner.ApproveKnowledge(id); err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "批准知识失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "知识批准成功",
	})
}

// deleteKnowledge 删除知识
func (s *Server) deleteKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的知识ID",
		})
		return
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 删除知识
	if err := knowledgeLearner.DeleteKnowledge(id); err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "删除知识失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "知识删除成功",
	})
}

// getKnowledgeDetail 获取知识详情
func (s *Server) getKnowledgeDetail(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的知识ID",
		})
		return
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 获取知识详情
	knowledge, err := knowledgeLearner.GetKnowledgeByID(id)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "获取知识详情失败: " + err.Error(),
		})
		return
	}

	if knowledge == nil {
		c.JSON(404, gin.H{
			"success": false,
			"message": "知识不存在",
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    knowledge,
	})
}

// updateKnowledge 更新知识
func (s *Server) updateKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的知识ID",
		})
		return
	}

	var req struct {
		Question       string   `json:"question" binding:"required"`
		Answer         string   `json:"answer" binding:"required"`
		Category       string   `json:"category"`
		Keywords       []string `json:"keywords"`
		Confidence     float32  `json:"confidence"`
		Source         string   `json:"source"`
		LearnedFrom    string   `json:"learned_from"`
		KnowledgeTopic string   `json:"knowledge_topic"`
	}

	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "请求参数错误: " + err.Error(),
		})
		return
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 创建更新的知识对象
	knowledge := &learning.LearnedKnowledge{
		ID:             id,
		Question:       req.Question,
		Answer:         req.Answer,
		Category:       req.Category,
		Keywords:       req.Keywords,
		Confidence:     req.Confidence,
		Source:         req.Source,
		LearnedFrom:    req.LearnedFrom,
		KnowledgeTopic: req.KnowledgeTopic,
	}

	// 更新知识
	if err := knowledgeLearner.UpdateKnowledge(knowledge); err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "更新知识失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"message": "知识更新成功",
		"data": gin.H{
			"id":       knowledge.ID,
			"question": knowledge.Question,
			"answer":   knowledge.Answer,
		},
	})
}

// getRelatedKnowledge 获取相关知识
func (s *Server) getRelatedKnowledge(c *gin.Context) {
	idStr := c.Param("id")
	id, err := strconv.Atoi(idStr)
	if err != nil {
		c.JSON(400, gin.H{
			"success": false,
			"message": "无效的知识ID",
		})
		return
	}

	// 获取查询参数
	limitStr := c.DefaultQuery("limit", "5")
	limit, err := strconv.Atoi(limitStr)
	if err != nil || limit <= 0 {
		limit = 5
	}
	if limit > 20 {
		limit = 20 // 最大限制
	}

	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 首先获取当前知识的详情
	currentKnowledge, err := knowledgeLearner.GetKnowledgeByID(id)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "获取知识详情失败: " + err.Error(),
		})
		return
	}

	if currentKnowledge == nil {
		c.JSON(404, gin.H{
			"success": false,
			"message": "知识不存在",
		})
		return
	}

	// 获取相关知识
	relatedKnowledge, err := knowledgeLearner.GetRelatedKnowledgeByTopic(currentKnowledge.KnowledgeTopic, id, limit)
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "获取相关知识失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success":          true,
		"data":             relatedKnowledge,
		"total":            len(relatedKnowledge),
		"knowledge_topic":  currentKnowledge.KnowledgeTopic,
		"current_question": currentKnowledge.Question,
	})
}

// getKnowledgeTopics 获取所有知识点列表
func (s *Server) getKnowledgeTopics(c *gin.Context) {
	// 获取知识学习器
	if s.learningManager == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "学习系统未初始化",
		})
		return
	}

	knowledgeLearner := s.learningManager.GetKnowledgeLearner()
	if knowledgeLearner == nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "知识学习器未初始化",
		})
		return
	}

	// 获取知识点列表
	topics, err := knowledgeLearner.GetKnowledgeTopics()
	if err != nil {
		c.JSON(500, gin.H{
			"success": false,
			"message": "获取知识点列表失败: " + err.Error(),
		})
		return
	}

	c.JSON(200, gin.H{
		"success": true,
		"data":    topics,
		"total":   len(topics),
	})
}

// handleApplyEvolution 处理应用进化改进请求
func (s *Server) handleApplyEvolution(c *gin.Context) {
	logger.Info("🚀 收到进化改进应用请求")

	// 检查学习管理器是否可用
	if s.learningManager == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"error":   "学习管理器不可用",
		})
		return
	}

	// 获取进化引擎
	evolutionEngine := s.learningManager.GetEvolutionEngine()
	if evolutionEngine == nil {
		c.JSON(http.StatusServiceUnavailable, gin.H{
			"success": false,
			"error":   "进化引擎不可用",
		})
		return
	}

	// 异步执行进化改进
	go func() {
		logger.Info("🧠 开始异步执行进化改进...")
		if err := evolutionEngine.ApplyEvolutionaryImprovements(); err != nil {
			logger.Errorf("进化改进执行失败: %v", err)
		} else {
			logger.Info("✅ 进化改进执行完成")
		}
	}()

	c.JSON(http.StatusOK, gin.H{
		"success":   true,
		"message":   "进化改进已开始执行，将在后台进行知识学习和持久化",
		"timestamp": time.Now().Format(time.RFC3339),
	})
}
