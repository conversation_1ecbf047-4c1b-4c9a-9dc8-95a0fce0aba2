package nlp

import (
	"fmt"
	"log"
	"math"
	"sort"
	"strings"
	"sync"
	"time"
)

// EnhancedVectorSearch 增强向量搜索引擎
type EnhancedVectorSearch struct {
	integratedProcessor *IntegratedProcessor
	vectorStore         VectorStore
	semanticMatcher     SemanticMatcher
	initialized         bool
	mutex               sync.RWMutex

	// 搜索配置
	config SearchConfig

	// 缓存
	queryCache   map[string]*SearchResult
	cacheMaxSize int
	cacheMutex   sync.RWMutex
}

// SearchConfig 搜索配置
type SearchConfig struct {
	// 向量搜索参数
	VectorWeight   float64 `json:"vector_weight"`   // 向量相似度权重
	SemanticWeight float64 `json:"semantic_weight"` // 语义匹配权重
	ContextWeight  float64 `json:"context_weight"`  // 上下文权重
	IntentWeight   float64 `json:"intent_weight"`   // 意图权重

	// 搜索阈值
	MinSimilarity float64 `json:"min_similarity"` // 最小相似度阈值
	MaxResults    int     `json:"max_results"`    // 最大结果数

	// 多模态搜索
	EnableMultiModal bool    `json:"enable_multi_modal"` // 启用多模态搜索
	TextWeight       float64 `json:"text_weight"`        // 文本权重
	MetadataWeight   float64 `json:"metadata_weight"`    // 元数据权重

	// 高级功能
	EnableReranking bool `json:"enable_reranking"` // 启用重排序
	EnableExpansion bool `json:"enable_expansion"` // 启用查询扩展
	EnableFeedback  bool `json:"enable_feedback"`  // 启用反馈学习
}

// SearchResult 搜索结果
type SearchResult struct {
	// 基础信息
	Query          string        `json:"query"`
	ProcessingTime time.Duration `json:"processing_time"`
	TotalResults   int           `json:"total_results"`

	// 搜索结果
	Results []SearchItem `json:"results"`

	// 分析信息
	QueryAnalysis *QueryAnalysis `json:"query_analysis"`
	SearchMetrics *SearchMetrics `json:"search_metrics"`

	// 推荐和建议
	Suggestions    []string `json:"suggestions"`
	RelatedQueries []string `json:"related_queries"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// SearchItem 搜索项
type SearchItem struct {
	// 基础信息
	ID      string `json:"id"`
	Content string `json:"content"`
	Title   string `json:"title"`

	// 相似度分数
	VectorScore   float64 `json:"vector_score"`
	SemanticScore float64 `json:"semantic_score"`
	ContextScore  float64 `json:"context_score"`
	IntentScore   float64 `json:"intent_score"`
	FinalScore    float64 `json:"final_score"`

	// 匹配信息
	MatchType     string   `json:"match_type"`
	MatchedTerms  []string `json:"matched_terms"`
	HighlightText string   `json:"highlight_text"`

	// 元数据
	Metadata  map[string]interface{} `json:"metadata"`
	Timestamp time.Time              `json:"timestamp"`
}

// QueryAnalysis 查询分析
type QueryAnalysis struct {
	// NLP分析结果
	Intent    *IntentResult     `json:"intent"`
	Sentiment *SentimentResult  `json:"sentiment"`
	Entities  []ExtractedEntity `json:"entities"`
	Context   *ContextResult    `json:"context"`
	Relations []Relation        `json:"relations"`

	// 查询特征
	QueryType   string  `json:"query_type"`
	Complexity  float64 `json:"complexity"`
	Specificity float64 `json:"specificity"`

	// 扩展信息
	ExpandedTerms []string `json:"expanded_terms"`
	Synonyms      []string `json:"synonyms"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// SearchMetrics 搜索指标
type SearchMetrics struct {
	// 性能指标
	VectorSearchTime  time.Duration `json:"vector_search_time"`
	SemanticMatchTime time.Duration `json:"semantic_match_time"`
	RerankingTime     time.Duration `json:"reranking_time"`
	TotalTime         time.Duration `json:"total_time"`

	// 质量指标
	AverageScore    float64 `json:"average_score"`
	ScoreVariance   float64 `json:"score_variance"`
	ResultDiversity float64 `json:"result_diversity"`

	// 覆盖指标
	VectorMatches   int `json:"vector_matches"`
	SemanticMatches int `json:"semantic_matches"`
	ContextMatches  int `json:"context_matches"`

	// 缓存指标
	CacheHit  bool `json:"cache_hit"`
	CacheSize int  `json:"cache_size"`
}

// VectorStore 向量存储接口
type VectorStore interface {
	Search(vector []float64, topK int) ([]VectorMatch, error)
	Add(id string, vector []float64, metadata map[string]interface{}) error
	Update(id string, vector []float64, metadata map[string]interface{}) error
	Delete(id string) error
	GetSize() int
}

// VectorMatch 向量匹配结果
type VectorMatch struct {
	ID       string                 `json:"id"`
	Score    float64                `json:"score"`
	Vector   []float64              `json:"vector"`
	Metadata map[string]interface{} `json:"metadata"`
}

// SemanticMatcher 语义匹配器接口
type SemanticMatcher interface {
	Match(query string, candidates []string) ([]SemanticMatch, error)
	GetSimilarity(text1, text2 string) (float64, error)
}

// SemanticMatch 语义匹配结果
type SemanticMatch struct {
	Text      string  `json:"text"`
	Score     float64 `json:"score"`
	MatchType string  `json:"match_type"`
}

// NewEnhancedVectorSearch 创建增强向量搜索引擎
func NewEnhancedVectorSearch(processor *IntegratedProcessor, vectorStore VectorStore, semanticMatcher SemanticMatcher) *EnhancedVectorSearch {
	log.Printf("🔍 初始化增强向量搜索引擎...")

	config := SearchConfig{
		VectorWeight:     0.4,
		SemanticWeight:   0.3,
		ContextWeight:    0.2,
		IntentWeight:     0.1,
		MinSimilarity:    0.3,
		MaxResults:       10,
		EnableMultiModal: true,
		TextWeight:       0.8,
		MetadataWeight:   0.2,
		EnableReranking:  true,
		EnableExpansion:  true,
		EnableFeedback:   true,
	}

	search := &EnhancedVectorSearch{
		integratedProcessor: processor,
		vectorStore:         vectorStore,
		semanticMatcher:     semanticMatcher,
		config:              config,
		queryCache:          make(map[string]*SearchResult),
		cacheMaxSize:        1000,
		initialized:         false,
	}

	// 异步初始化
	go search.initializeAsync()

	return search
}

// initializeAsync 异步初始化
func (evs *EnhancedVectorSearch) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 增强向量搜索引擎初始化失败: %v", r)
			evs.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化增强向量搜索组件...")

	// 等待集成处理器初始化完成
	for i := 0; i < 30; i++ {
		if evs.integratedProcessor != nil && evs.integratedProcessor.IsInitialized() {
			break
		}
		time.Sleep(1 * time.Second)
		log.Printf("⏳ 等待集成处理器初始化... (%d/30)", i+1)
	}

	evs.mutex.Lock()
	evs.initialized = true
	evs.mutex.Unlock()

	log.Printf("✅ 增强向量搜索引擎初始化完成")
}

// IsInitialized 检查是否已初始化
func (evs *EnhancedVectorSearch) IsInitialized() bool {
	evs.mutex.RLock()
	defer evs.mutex.RUnlock()
	return evs.initialized
}

// Search 执行增强搜索
func (evs *EnhancedVectorSearch) Search(query string) (*SearchResult, error) {
	startTime := time.Now()

	log.Printf("🔍 开始增强向量搜索: %s", query[:min(len(query), 50)])

	// 检查缓存
	if cached := evs.getFromCache(query); cached != nil {
		log.Printf("✅ 缓存命中，返回缓存结果")
		cached.SearchMetrics.CacheHit = true
		return cached, nil
	}

	// 1. 查询分析
	queryAnalysis, err := evs.analyzeQuery(query)
	if err != nil {
		return nil, fmt.Errorf("查询分析失败: %v", err)
	}

	// 2. 多阶段搜索
	searchItems, metrics, err := evs.performMultiStageSearch(query, queryAnalysis)
	if err != nil {
		return nil, fmt.Errorf("多阶段搜索失败: %v", err)
	}

	// 3. 结果重排序
	if evs.config.EnableReranking {
		searchItems = evs.rerankResults(query, queryAnalysis, searchItems)
		metrics.RerankingTime = time.Since(startTime) - metrics.TotalTime
	}

	// 4. 生成建议和相关查询
	suggestions := evs.generateSuggestions(query, queryAnalysis, searchItems)
	relatedQueries := evs.generateRelatedQueries(query, queryAnalysis)

	// 5. 构建搜索结果
	result := &SearchResult{
		Query:          query,
		ProcessingTime: time.Since(startTime),
		TotalResults:   len(searchItems),
		Results:        searchItems,
		QueryAnalysis:  queryAnalysis,
		SearchMetrics:  metrics,
		Suggestions:    suggestions,
		RelatedQueries: relatedQueries,
		Metadata: map[string]interface{}{
			"search_config": evs.config,
			"timestamp":     time.Now(),
		},
	}

	// 6. 更新缓存
	evs.addToCache(query, result)

	log.Printf("✅ 增强向量搜索完成: %d 个结果, 耗时=%v", len(searchItems), result.ProcessingTime)

	return result, nil
}

// analyzeQuery 分析查询
func (evs *EnhancedVectorSearch) analyzeQuery(query string) (*QueryAnalysis, error) {
	if !evs.integratedProcessor.IsInitialized() {
		return &QueryAnalysis{
			QueryType:   "simple",
			Complexity:  0.5,
			Specificity: 0.5,
			Metadata:    make(map[string]interface{}),
		}, nil
	}

	// 使用集成处理器分析查询
	nlpResult := evs.integratedProcessor.ProcessText(query)

	// 计算查询特征
	complexity := evs.calculateQueryComplexity(query, nlpResult)
	specificity := evs.calculateQuerySpecificity(query, nlpResult)
	queryType := evs.determineQueryType(query, nlpResult)

	// 查询扩展
	var expandedTerms, synonyms []string
	if evs.config.EnableExpansion {
		expandedTerms = evs.expandQuery(query, nlpResult)
		synonyms = evs.findSynonyms(query, nlpResult)
	}

	return &QueryAnalysis{
		Intent:        nlpResult.Intent,
		Sentiment:     nlpResult.SentimentDetail,
		Entities:      nlpResult.ExtractedEntities,
		Context:       nlpResult.Context,
		Relations:     nlpResult.Relations,
		QueryType:     queryType,
		Complexity:    complexity,
		Specificity:   specificity,
		ExpandedTerms: expandedTerms,
		Synonyms:      synonyms,
		Metadata: map[string]interface{}{
			"nlp_result": nlpResult,
		},
	}, nil
}

// performMultiStageSearch 执行多阶段搜索
func (evs *EnhancedVectorSearch) performMultiStageSearch(query string, analysis *QueryAnalysis) ([]SearchItem, *SearchMetrics, error) {
	metrics := &SearchMetrics{
		CacheHit:  false,
		CacheSize: len(evs.queryCache),
	}

	var allItems []SearchItem
	startTime := time.Now()

	// 阶段1: 向量搜索
	vectorItems, err := evs.performVectorSearch(query, analysis)
	if err != nil {
		log.Printf("⚠️ 向量搜索失败: %v", err)
	} else {
		allItems = append(allItems, vectorItems...)
		metrics.VectorMatches = len(vectorItems)
	}
	metrics.VectorSearchTime = time.Since(startTime)

	// 阶段2: 语义匹配
	semanticStart := time.Now()
	semanticItems, err := evs.performSemanticSearch(query, analysis)
	if err != nil {
		log.Printf("⚠️ 语义搜索失败: %v", err)
	} else {
		allItems = append(allItems, semanticItems...)
		metrics.SemanticMatches = len(semanticItems)
	}
	metrics.SemanticMatchTime = time.Since(semanticStart)

	// 阶段3: 上下文匹配
	contextItems, err := evs.performContextSearch(query, analysis)
	if err != nil {
		log.Printf("⚠️ 上下文搜索失败: %v", err)
	} else {
		allItems = append(allItems, contextItems...)
		metrics.ContextMatches = len(contextItems)
	}

	// 合并和去重
	mergedItems := evs.mergeAndDeduplicateResults(allItems)

	// 计算最终分数
	finalItems := evs.calculateFinalScores(mergedItems, analysis)

	// 过滤和排序
	filteredItems := evs.filterAndSortResults(finalItems)

	// 计算指标
	metrics.TotalTime = time.Since(startTime)
	if len(filteredItems) > 0 {
		totalScore := 0.0
		for _, item := range filteredItems {
			totalScore += item.FinalScore
		}
		metrics.AverageScore = totalScore / float64(len(filteredItems))
		metrics.ScoreVariance = evs.calculateScoreVariance(filteredItems, metrics.AverageScore)
		metrics.ResultDiversity = evs.calculateResultDiversity(filteredItems)
	}

	return filteredItems, metrics, nil
}

// performVectorSearch 执行向量搜索
func (evs *EnhancedVectorSearch) performVectorSearch(query string, analysis *QueryAnalysis) ([]SearchItem, error) {
	if evs.vectorStore == nil {
		return []SearchItem{}, nil
	}

	// 生成查询向量
	queryVector, err := evs.generateQueryVector(query, analysis)
	if err != nil {
		return nil, fmt.Errorf("生成查询向量失败: %v", err)
	}

	// 执行向量搜索
	matches, err := evs.vectorStore.Search(queryVector, evs.config.MaxResults*2)
	if err != nil {
		return nil, fmt.Errorf("向量搜索失败: %v", err)
	}

	// 转换为搜索项
	var items []SearchItem
	for _, match := range matches {
		if match.Score >= evs.config.MinSimilarity {
			item := SearchItem{
				ID:          match.ID,
				VectorScore: match.Score,
				MatchType:   "vector",
				Metadata:    match.Metadata,
				Timestamp:   time.Now(),
			}

			// 从元数据中提取内容
			if content, ok := match.Metadata["content"].(string); ok {
				item.Content = content
			}
			if title, ok := match.Metadata["title"].(string); ok {
				item.Title = title
			}

			items = append(items, item)
		}
	}

	return items, nil
}

// performSemanticSearch 执行语义搜索
func (evs *EnhancedVectorSearch) performSemanticSearch(query string, analysis *QueryAnalysis) ([]SearchItem, error) {
	if evs.semanticMatcher == nil {
		return []SearchItem{}, nil
	}

	// 构建候选文本列表（这里需要从数据源获取）
	candidates := evs.getCandidateTexts()

	// 执行语义匹配
	matches, err := evs.semanticMatcher.Match(query, candidates)
	if err != nil {
		return nil, fmt.Errorf("语义匹配失败: %v", err)
	}

	// 转换为搜索项
	var items []SearchItem
	for i, match := range matches {
		if match.Score >= evs.config.MinSimilarity {
			item := SearchItem{
				ID:            fmt.Sprintf("semantic_%d", i),
				Content:       match.Text,
				SemanticScore: match.Score,
				MatchType:     "semantic",
				Timestamp:     time.Now(),
				Metadata:      map[string]interface{}{"semantic_match_type": match.MatchType},
			}
			items = append(items, item)
		}
	}

	return items, nil
}

// performContextSearch 执行上下文搜索
func (evs *EnhancedVectorSearch) performContextSearch(query string, analysis *QueryAnalysis) ([]SearchItem, error) {
	var items []SearchItem

	// 基于意图的搜索
	if analysis.Intent != nil {
		intentItems := evs.searchByIntent(query, analysis.Intent)
		items = append(items, intentItems...)
	}

	// 基于实体的搜索
	if len(analysis.Entities) > 0 {
		entityItems := evs.searchByEntities(query, analysis.Entities)
		items = append(items, entityItems...)
	}

	// 基于上下文的搜索
	if analysis.Context != nil {
		contextItems := evs.searchByContext(query, analysis.Context)
		items = append(items, contextItems...)
	}

	return items, nil
}

// generateQueryVector 生成查询向量
func (evs *EnhancedVectorSearch) generateQueryVector(query string, analysis *QueryAnalysis) ([]float64, error) {
	// 使用集成处理器生成向量
	if evs.integratedProcessor != nil && evs.integratedProcessor.IsInitialized() {
		result := evs.integratedProcessor.ProcessText(query)
		if result.SpagoResult != nil && result.SpagoResult.SentenceEmbedding != nil {
			// 从Spago结果中提取向量
			return evs.extractVectorFromTensor(result.SpagoResult.SentenceEmbedding), nil
		}
	}

	// 备用方案：生成简单向量
	return evs.generateSimpleVector(query), nil
}

// extractVectorFromTensor 从张量中提取向量
func (evs *EnhancedVectorSearch) extractVectorFromTensor(tensor interface{}) []float64 {
	// 这里需要根据实际的张量类型进行转换
	// 暂时返回一个固定长度的向量
	vector := make([]float64, 128)
	for i := range vector {
		vector[i] = 0.1 * float64(i%10)
	}
	return vector
}

// generateSimpleVector 生成简单向量
func (evs *EnhancedVectorSearch) generateSimpleVector(text string) []float64 {
	vector := make([]float64, 128)
	words := strings.Fields(strings.ToLower(text))

	for i, word := range words {
		if i >= len(vector) {
			break
		}
		// 简单的哈希函数
		hash := 0
		for _, r := range word {
			hash = hash*31 + int(r)
		}
		vector[i] = float64(hash%1000) / 1000.0
	}

	return vector
}

// getCandidateTexts 获取候选文本
func (evs *EnhancedVectorSearch) getCandidateTexts() []string {
	// 这里应该从实际的数据源获取候选文本
	// 暂时返回一些示例文本
	return []string{
		"如何使用Go语言开发Web应用",
		"Python机器学习入门指南",
		"JavaScript前端开发最佳实践",
		"数据库设计原则和规范",
		"微服务架构设计模式",
	}
}

// searchByIntent 基于意图搜索
func (evs *EnhancedVectorSearch) searchByIntent(query string, intent *IntentResult) []SearchItem {
	var items []SearchItem

	// 根据意图类型生成相关内容
	intentScore := intent.Confidence * evs.config.IntentWeight

	item := SearchItem{
		ID:           fmt.Sprintf("intent_%s", intent.Intent),
		Content:      fmt.Sprintf("基于意图 '%s' 的相关内容", intent.Intent),
		Title:        fmt.Sprintf("意图匹配: %s", intent.Category),
		IntentScore:  intentScore,
		MatchType:    "intent",
		MatchedTerms: []string{intent.Intent},
		Timestamp:    time.Now(),
		Metadata: map[string]interface{}{
			"intent_type":     intent.Intent,
			"intent_category": intent.Category,
			"intent_params":   intent.Parameters,
		},
	}

	if intentScore >= evs.config.MinSimilarity {
		items = append(items, item)
	}

	return items
}

// searchByEntities 基于实体搜索
func (evs *EnhancedVectorSearch) searchByEntities(query string, entities []ExtractedEntity) []SearchItem {
	var items []SearchItem

	for i, entity := range entities {
		entityScore := entity.Confidence * evs.config.ContextWeight

		item := SearchItem{
			ID:           fmt.Sprintf("entity_%d", i),
			Content:      fmt.Sprintf("与实体 '%s' 相关的内容", entity.Text),
			Title:        fmt.Sprintf("实体匹配: %s (%s)", entity.Text, entity.Type),
			ContextScore: entityScore,
			MatchType:    "entity",
			MatchedTerms: []string{entity.Text},
			Timestamp:    time.Now(),
			Metadata: map[string]interface{}{
				"entity_text":  entity.Text,
				"entity_type":  entity.Type,
				"entity_attrs": entity.Attributes,
			},
		}

		if entityScore >= evs.config.MinSimilarity {
			items = append(items, item)
		}
	}

	return items
}

// searchByContext 基于上下文搜索
func (evs *EnhancedVectorSearch) searchByContext(query string, context *ContextResult) []SearchItem {
	var items []SearchItem

	contextScore := evs.config.ContextWeight
	if context.Domain != "unknown" {
		contextScore *= 1.2
	}
	if context.Topic != "unknown" {
		contextScore *= 1.1
	}

	item := SearchItem{
		ID:           "context_match",
		Content:      fmt.Sprintf("领域: %s, 话题: %s 的相关内容", context.Domain, context.Topic),
		Title:        fmt.Sprintf("上下文匹配: %s/%s", context.Domain, context.Topic),
		ContextScore: contextScore,
		MatchType:    "context",
		MatchedTerms: []string{context.Domain, context.Topic},
		Timestamp:    time.Now(),
		Metadata: map[string]interface{}{
			"domain":     context.Domain,
			"topic":      context.Topic,
			"complexity": context.Complexity,
			"formality":  context.Formality,
			"urgency":    context.Urgency,
		},
	}

	if contextScore >= evs.config.MinSimilarity {
		items = append(items, item)
	}

	return items
}

// getFromCache 从缓存获取结果
func (evs *EnhancedVectorSearch) getFromCache(query string) *SearchResult {
	evs.cacheMutex.RLock()
	defer evs.cacheMutex.RUnlock()

	if result, exists := evs.queryCache[query]; exists {
		return result
	}
	return nil
}

// addToCache 添加到缓存
func (evs *EnhancedVectorSearch) addToCache(query string, result *SearchResult) {
	evs.cacheMutex.Lock()
	defer evs.cacheMutex.Unlock()

	// 如果缓存已满，删除最旧的条目
	if len(evs.queryCache) >= evs.cacheMaxSize {
		// 简单的LRU实现：删除第一个条目
		for k := range evs.queryCache {
			delete(evs.queryCache, k)
			break
		}
	}

	evs.queryCache[query] = result
}

// mergeAndDeduplicateResults 合并和去重结果
func (evs *EnhancedVectorSearch) mergeAndDeduplicateResults(items []SearchItem) []SearchItem {
	seen := make(map[string]bool)
	var merged []SearchItem

	for _, item := range items {
		key := item.ID + "|" + item.Content
		if !seen[key] {
			seen[key] = true
			merged = append(merged, item)
		}
	}

	return merged
}

// calculateFinalScores 计算最终分数
func (evs *EnhancedVectorSearch) calculateFinalScores(items []SearchItem, analysis *QueryAnalysis) []SearchItem {
	for i := range items {
		item := &items[i]

		// 加权计算最终分数
		finalScore := 0.0
		finalScore += item.VectorScore * evs.config.VectorWeight
		finalScore += item.SemanticScore * evs.config.SemanticWeight
		finalScore += item.ContextScore * evs.config.ContextWeight
		finalScore += item.IntentScore * evs.config.IntentWeight

		item.FinalScore = finalScore
	}

	return items
}

// filterAndSortResults 过滤和排序结果
func (evs *EnhancedVectorSearch) filterAndSortResults(items []SearchItem) []SearchItem {
	// 过滤低分结果
	var filtered []SearchItem
	for _, item := range items {
		if item.FinalScore >= evs.config.MinSimilarity {
			filtered = append(filtered, item)
		}
	}

	// 按分数排序
	sort.Slice(filtered, func(i, j int) bool {
		return filtered[i].FinalScore > filtered[j].FinalScore
	})

	// 限制结果数量
	if len(filtered) > evs.config.MaxResults {
		filtered = filtered[:evs.config.MaxResults]
	}

	return filtered
}

// rerankResults 重排序结果
func (evs *EnhancedVectorSearch) rerankResults(query string, analysis *QueryAnalysis, items []SearchItem) []SearchItem {
	// 基于多个因素重新排序
	for i := range items {
		item := &items[i]

		// 重排序分数调整
		rerankBonus := 0.0

		// 基于匹配类型的调整
		switch item.MatchType {
		case "vector":
			rerankBonus += 0.1
		case "semantic":
			rerankBonus += 0.15
		case "intent":
			rerankBonus += 0.05
		case "entity":
			rerankBonus += 0.08
		case "context":
			rerankBonus += 0.12
		}

		// 基于匹配词数量的调整
		if len(item.MatchedTerms) > 0 {
			rerankBonus += float64(len(item.MatchedTerms)) * 0.02
		}

		// 应用重排序调整
		item.FinalScore += rerankBonus
	}

	// 重新排序
	sort.Slice(items, func(i, j int) bool {
		return items[i].FinalScore > items[j].FinalScore
	})

	return items
}

// generateSuggestions 生成建议
func (evs *EnhancedVectorSearch) generateSuggestions(query string, analysis *QueryAnalysis, items []SearchItem) []string {
	var suggestions []string

	// 基于查询分析生成建议
	if analysis.Intent != nil {
		suggestions = append(suggestions, fmt.Sprintf("尝试搜索 '%s' 相关内容", analysis.Intent.Category))
	}

	// 基于实体生成建议
	for _, entity := range analysis.Entities {
		if entity.Confidence > 0.7 {
			suggestions = append(suggestions, fmt.Sprintf("搜索更多关于 '%s' 的信息", entity.Text))
		}
	}

	// 基于上下文生成建议
	if analysis.Context != nil && analysis.Context.Domain != "unknown" {
		suggestions = append(suggestions, fmt.Sprintf("探索 '%s' 领域的其他内容", analysis.Context.Domain))
	}

	// 限制建议数量
	if len(suggestions) > 5 {
		suggestions = suggestions[:5]
	}

	return suggestions
}

// generateRelatedQueries 生成相关查询
func (evs *EnhancedVectorSearch) generateRelatedQueries(query string, analysis *QueryAnalysis) []string {
	var related []string

	// 基于扩展词生成相关查询
	for _, term := range analysis.ExpandedTerms {
		related = append(related, fmt.Sprintf("%s %s", query, term))
	}

	// 基于同义词生成相关查询
	for _, synonym := range analysis.Synonyms {
		related = append(related, strings.ReplaceAll(query, query, synonym))
	}

	// 基于意图生成相关查询
	if analysis.Intent != nil {
		switch analysis.Intent.Intent {
		case "question":
			related = append(related, fmt.Sprintf("如何 %s", query))
		case "help":
			related = append(related, fmt.Sprintf("%s 教程", query))
		case "technical":
			related = append(related, fmt.Sprintf("%s 技术文档", query))
		}
	}

	// 限制相关查询数量
	if len(related) > 3 {
		related = related[:3]
	}

	return related
}

// calculateQueryComplexity 计算查询复杂度
func (evs *EnhancedVectorSearch) calculateQueryComplexity(query string, nlpResult *IntegratedResult) float64 {
	complexity := 0.0

	// 基于词汇数量
	wordCount := len(strings.Fields(query))
	complexity += float64(wordCount) / 20.0

	// 基于实体数量
	if len(nlpResult.ExtractedEntities) > 0 {
		complexity += float64(len(nlpResult.ExtractedEntities)) / 10.0
	}

	// 基于关系数量
	if len(nlpResult.Relations) > 0 {
		complexity += float64(len(nlpResult.Relations)) / 5.0
	}

	return math.Min(complexity, 1.0)
}

// calculateQuerySpecificity 计算查询特异性
func (evs *EnhancedVectorSearch) calculateQuerySpecificity(query string, nlpResult *IntegratedResult) float64 {
	specificity := 0.5 // 基础特异性

	// 基于实体类型
	entityTypes := make(map[string]bool)
	for _, entity := range nlpResult.ExtractedEntities {
		entityTypes[entity.Type] = true
	}
	specificity += float64(len(entityTypes)) * 0.1

	// 基于查询长度
	if len(query) > 50 {
		specificity += 0.2
	}

	return math.Min(specificity, 1.0)
}

// determineQueryType 确定查询类型
func (evs *EnhancedVectorSearch) determineQueryType(query string, nlpResult *IntegratedResult) string {
	if nlpResult.Intent != nil {
		return nlpResult.Intent.Intent
	}

	// 基于查询内容判断
	queryLower := strings.ToLower(query)
	if strings.Contains(queryLower, "?") || strings.Contains(queryLower, "？") {
		return "question"
	} else if strings.Contains(queryLower, "如何") || strings.Contains(queryLower, "怎么") {
		return "how_to"
	} else if strings.Contains(queryLower, "什么") || strings.Contains(queryLower, "是什么") {
		return "definition"
	}

	return "general"
}

// expandQuery 扩展查询
func (evs *EnhancedVectorSearch) expandQuery(query string, nlpResult *IntegratedResult) []string {
	var expanded []string

	// 基于实体扩展
	for _, entity := range nlpResult.ExtractedEntities {
		if entity.Type == "TECH" || entity.Type == "PROGRAMMING_LANGUAGE" {
			expanded = append(expanded, entity.Text+" 教程")
			expanded = append(expanded, entity.Text+" 文档")
		}
	}

	// 基于关键词扩展
	for _, keyword := range nlpResult.Keywords {
		expanded = append(expanded, keyword+" 指南")
	}

	return expanded
}

// findSynonyms 查找同义词
func (evs *EnhancedVectorSearch) findSynonyms(query string, nlpResult *IntegratedResult) []string {
	// 简单的同义词映射
	synonymMap := map[string][]string{
		"开发": {"编程", "开发", "构建"},
		"学习": {"学习", "掌握", "了解"},
		"使用": {"使用", "应用", "运用"},
		"问题": {"问题", "疑问", "困难"},
		"方法": {"方法", "方式", "途径"},
		"技术": {"技术", "技能", "工艺"},
		"系统": {"系统", "平台", "框架"},
		"数据": {"数据", "信息", "资料"},
	}

	var synonyms []string
	words := strings.Fields(strings.ToLower(query))

	for _, word := range words {
		if syns, exists := synonymMap[word]; exists {
			synonyms = append(synonyms, syns...)
		}
	}

	return synonyms
}

// calculateScoreVariance 计算分数方差
func (evs *EnhancedVectorSearch) calculateScoreVariance(items []SearchItem, avgScore float64) float64 {
	if len(items) <= 1 {
		return 0.0
	}

	variance := 0.0
	for _, item := range items {
		diff := item.FinalScore - avgScore
		variance += diff * diff
	}

	return variance / float64(len(items))
}

// calculateResultDiversity 计算结果多样性
func (evs *EnhancedVectorSearch) calculateResultDiversity(items []SearchItem) float64 {
	if len(items) <= 1 {
		return 0.0
	}

	// 基于匹配类型的多样性
	typeCount := make(map[string]int)
	for _, item := range items {
		typeCount[item.MatchType]++
	}

	diversity := float64(len(typeCount)) / float64(len(items))
	return math.Min(diversity, 1.0)
}
