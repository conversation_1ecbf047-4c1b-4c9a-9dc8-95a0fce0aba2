package main

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	dsn := "root:123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 测试数据库连接
	if err := db.<PERSON>(); err != nil {
		log.Fatal("数据库连接测试失败:", err)
	}

	fmt.Println("🔍 knowledge_vectors表状态检查")
	fmt.Println(strings.Repeat("=", 50))

	// 1. 检查表是否存在
	var tableExists int
	err = db.QueryRow(`
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_SCHEMA = 'faqdb' AND TABLE_NAME = 'knowledge_vectors'
	`).Scan(&tableExists)

	if err != nil {
		log.Fatal("检查表存在性失败:", err)
	}

	if tableExists == 0 {
		fmt.Println("❌ knowledge_vectors表不存在")
		return
	}
	fmt.Println("✅ knowledge_vectors表存在")

	// 2. 检查向量数据数量
	var vectorCount int
	err = db.QueryRow("SELECT COUNT(*) FROM knowledge_vectors").Scan(&vectorCount)
	if err != nil {
		log.Fatal("查询向量数量失败:", err)
	}
	fmt.Printf("📊 向量数据总数: %d\n", vectorCount)

	// 3. 检查learned_knowledge数据数量
	var knowledgeCount int
	err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&knowledgeCount)
	if err != nil {
		log.Fatal("查询知识数量失败:", err)
	}
	fmt.Printf("📚 学习知识总数: %d\n", knowledgeCount)

	// 4. 检查向量覆盖率
	if knowledgeCount > 0 {
		coverage := float64(vectorCount) / float64(knowledgeCount) * 100
		fmt.Printf("📈 向量覆盖率: %.1f%%\n", coverage)
	}

	// 5. 查看示例向量数据
	if vectorCount > 0 {
		fmt.Println("\n🔍 示例向量数据:")
		rows, err := db.Query(`
			SELECT kv.id, kv.knowledge_id, kv.vector_type, 
			       LENGTH(kv.vector_data) as vector_size,
			       lk.question
			FROM knowledge_vectors kv
			JOIN learned_knowledge lk ON kv.knowledge_id = lk.id
			LIMIT 3
		`)
		if err != nil {
			log.Fatal("查询示例数据失败:", err)
		}
		defer rows.Close()

		for rows.Next() {
			var id, knowledgeID, vectorSize int
			var vectorType, question string

			err := rows.Scan(&id, &knowledgeID, &vectorType, &vectorSize, &question)
			if err != nil {
				continue
			}

			fmt.Printf("  ID=%d, KnowledgeID=%d, Type=%s, Size=%d bytes\n",
				id, knowledgeID, vectorType, vectorSize)
			fmt.Printf("  Question: %s\n", question)
			fmt.Println()
		}

		// 6. 检查向量数据格式
		fmt.Println("🔍 检查向量数据格式:")
		var vectorData string
		err = db.QueryRow("SELECT vector_data FROM knowledge_vectors LIMIT 1").Scan(&vectorData)
		if err != nil {
			log.Printf("获取向量数据失败: %v", err)
		} else {
			var vector []float32
			if err := json.Unmarshal([]byte(vectorData), &vector); err != nil {
				fmt.Printf("❌ 向量数据格式错误: %v\n", err)
			} else {
				fmt.Printf("✅ 向量数据格式正确, 维度: %d\n", len(vector))
				if len(vector) > 0 {
					fmt.Printf("  示例值: [%.4f, %.4f, ...]\n", vector[0], vector[1])
				}
			}
		}
	} else {
		fmt.Println("\n⚠️ 没有向量数据")
	}

	fmt.Println("\n" + strings.Repeat("=", 50))
	fmt.Println("检查完成")
}
