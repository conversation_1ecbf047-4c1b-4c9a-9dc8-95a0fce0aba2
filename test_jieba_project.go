package main

import (
	"fmt"
	"log"
	"faq-system/internal/nlp"
)

func main() {
	fmt.Println("🧮 测试项目环境下的Jieba处理器...")
	
	// 设置日志格式
	log.SetFlags(log.LstdFlags)
	
	fmt.Println("📍 步骤1: 创建JiebaProcessor...")
	processor := nlp.NewJiebaProcessor()
	defer processor.Close()
	
	fmt.Println("📍 步骤2: 测试简单分词...")
	text := "测试"
	words := processor.SegmentText(text)
	fmt.Printf("分词结果: %v\n", words)
	
	fmt.Println("📍 步骤3: 测试复杂分词...")
	text2 := "1+1等于多少"
	words2 := processor.SegmentText(text2)
	fmt.Printf("分词结果: %v\n", words2)
	
	fmt.Println("📍 步骤4: 测试关键词提取...")
	keywords := processor.ExtractKeywords(text2, 3)
	fmt.Printf("关键词: %v\n", keywords)
	
	fmt.Println("✅ 项目环境下Jieba处理器测试完成")
}
