package static

import (
	"embed"
	"io/fs"
	"net/http"

	"github.com/gin-gonic/gin"
)

// 嵌入静态文件
//go:embed web/*
var WebFS embed.FS

// GetWebFS 获取嵌入的web文件系统
func GetWebFS() fs.FS {
	webFS, err := fs.Sub(WebFS, "web")
	if err != nil {
		// 如果出错，返回原始文件系统
		return WebFS
	}
	return webFS
}

// ServeEmbeddedFile 提供嵌入的静态文件
func ServeEmbeddedFile(filename string) gin.HandlerFunc {
	return func(c *gin.Context) {
		webFS := GetWebFS()
		
		// 尝试读取文件
		data, err := fs.ReadFile(webFS, filename)
		if err != nil {
			c.JSON(http.StatusNotFound, gin.H{
				"error": "File not found: " + filename,
			})
			return
		}

		// 设置正确的Content-Type
		contentType := "text/html; charset=utf-8"
		if len(filename) > 5 && filename[len(filename)-5:] == ".html" {
			contentType = "text/html; charset=utf-8"
		}
		
		c.<PERSON><PERSON>("Content-Type", contentType)
		c.Data(http.StatusOK, contentType, data)
	}
}

// ServeEmbeddedStatic 提供嵌入的静态文件目录
func ServeEmbeddedStatic() gin.HandlerFunc {
	webFS := GetWebFS()
	fileServer := http.FileServer(http.FS(webFS))
	
	return gin.WrapH(fileServer)
}
