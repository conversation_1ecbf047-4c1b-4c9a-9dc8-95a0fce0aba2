#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的进化改进API测试
"""

import requests
import json
import time
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/api/v1/ask"
EVOLUTION_ENDPOINT = f"{SERVER_URL}/api/learning/evolution/apply"
HEALTH_ENDPOINT = f"{SERVER_URL}/api/v1/health"

def check_server_health():
    """检查服务器健康状态"""
    print("🔍 检查服务器健康状态...")
    
    try:
        response = requests.get(HEALTH_ENDPOINT, timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 服务器连接失败: {e}")
        return False

def test_basic_qa():
    """测试基础问答功能"""
    print("\n📝 测试基础问答功能...")
    
    test_questions = [
        "什么是人工智能？",
        "如何学习编程？",
        "Python有什么特点？",
        "什么是机器学习？",
        "如何优化数据库性能？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试问题 {i}: {question}")
        
        try:
            response = requests.post(ASK_ENDPOINT,
                                   json={"question": question},
                                   headers={"Content-Type": "application/json"},
                                   timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get("answer", "")
                confidence = data.get("confidence", 0)
                intent = data.get("intent", "")
                
                print(f"✅ 回答: {answer[:100]}..." if len(answer) > 100 else f"✅ 回答: {answer}")
                print(f"📊 置信度: {confidence:.2f} | 意图: {intent}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)  # 避免请求过快

def trigger_evolution():
    """触发进化改进"""
    print("\n🚀 触发进化改进...")
    
    try:
        response = requests.post(EVOLUTION_ENDPOINT,
                               headers={"Content-Type": "application/json"},
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 进化改进已触发")
            print(f"📝 消息: {data.get('message', '')}")
            print(f"⏰ 时间戳: {data.get('timestamp', '')}")
            return True
        else:
            print(f"❌ 触发进化改进失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_nlp_features():
    """测试NLP增强功能"""
    print("\n🧠 测试NLP增强功能...")
    
    # 测试问候语识别
    greetings = ["你好", "早上好", "大家好", "晚安", "注意身体"]
    print("\n👋 测试问候语识别:")
    for greeting in greetings:
        test_single_question(greeting, "问候语")
    
    # 测试感谢识别
    thanks = ["谢谢", "非常感谢", "感谢您的帮助", "多谢", "thank you"]
    print("\n🙏 测试感谢识别:")
    for thank in thanks:
        test_single_question(thank, "感谢")
    
    # 测试道歉识别
    apologies = ["对不起", "抱歉", "我错了", "你错了", "都错了"]
    print("\n😔 测试道歉识别:")
    for apology in apologies:
        test_single_question(apology, "道歉")

def test_single_question(question, expected_type):
    """测试单个问题"""
    try:
        response = requests.post(ASK_ENDPOINT,
                               json={"question": question},
                               headers={"Content-Type": "application/json"},
                               timeout=5)
        
        if response.status_code == 200:
            data = response.json()
            intent = data.get("intent", "")
            confidence = data.get("confidence", 0)
            print(f"  📝 {question} -> {intent} ({confidence:.2f})")
        else:
            print(f"  ❌ {question} -> 请求失败 ({response.status_code})")
            
    except Exception as e:
        print(f"  ❌ {question} -> 异常: {e}")

def test_learning_capabilities():
    """测试学习能力"""
    print("\n📚 测试学习能力...")
    
    # 模拟一些高质量的问答对话
    learning_scenarios = [
        {
            "question": "什么是RESTful API？",
            "context": "用户询问API设计相关问题"
        },
        {
            "question": "如何优化MySQL查询？",
            "context": "用户询问数据库优化问题"
        },
        {
            "question": "Python装饰器怎么用？",
            "context": "用户询问Python编程问题"
        }
    ]
    
    for i, scenario in enumerate(learning_scenarios, 1):
        print(f"\n📖 学习场景 {i}: {scenario['question']}")
        
        # 先询问问题
        try:
            response = requests.post(ASK_ENDPOINT,
                                   json={"question": scenario["question"]},
                                   headers={"Content-Type": "application/json"},
                                   timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get("answer", "")
                confidence = data.get("confidence", 0)
                
                print(f"  💬 系统回答: {answer[:80]}..." if len(answer) > 80 else f"  💬 系统回答: {answer}")
                print(f"  📊 置信度: {confidence:.2f}")
                
                # 这里可以模拟用户反馈，但由于没有反馈API，我们跳过
                print(f"  🎯 上下文: {scenario['context']}")
            else:
                print(f"  ❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"  ❌ 请求异常: {e}")
        
        time.sleep(1)

def main():
    """主测试函数"""
    print("🧠 FAQ系统进化改进功能测试")
    print("=" * 50)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查服务器健康状态
    if not check_server_health():
        print("❌ 服务器不可用，退出测试")
        return
    
    # 2. 测试基础问答功能
    test_basic_qa()
    
    # 3. 测试NLP增强功能
    test_nlp_features()
    
    # 4. 测试学习能力
    test_learning_capabilities()
    
    # 5. 触发进化改进
    print("\n" + "="*50)
    if trigger_evolution():
        print("\n⏳ 等待进化改进处理...")
        time.sleep(5)  # 给系统时间处理
        
        print("\n🔄 进化改进后再次测试...")
        # 再次测试一些问题，看是否有改进
        test_questions = [
            "人工智能的应用领域有哪些？",
            "编程学习的最佳实践是什么？",
            "数据库设计的原则有哪些？"
        ]
        
        for question in test_questions:
            print(f"\n🔍 改进后测试: {question}")
            test_single_question(question, "技术问题")
    else:
        print("❌ 进化改进触发失败")
    
    print(f"\n⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 进化改进功能测试完成！")
    
    # 总结
    print("\n📋 测试总结:")
    print("✅ 服务器健康检查")
    print("✅ 基础问答功能测试")
    print("✅ NLP增强功能测试")
    print("✅ 学习能力测试")
    print("✅ 进化改进API测试")
    print("\n💡 注意: 真正的知识学习和持久化需要数据库表支持")
    print("   请确保 learned_knowledge 和 knowledge_vectors 表已创建")

if __name__ == "__main__":
    main()
