#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试简短输入处理改进
"""

import requests
import json
import time
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/api/v1/ask"

def test_short_input(question, expected_behavior=""):
    """测试单个简短输入"""
    print(f"\n🔍 测试输入: '{question}'")
    if expected_behavior:
        print(f"   期望行为: {expected_behavior}")
    
    try:
        response = requests.post(ASK_ENDPOINT,
                               json={"question": question},
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get("answer", "")
            confidence = data.get("confidence", 0)
            intent = data.get("intent", "")
            
            print(f"✅ 回答: {answer[:200]}..." if len(answer) > 200 else f"✅ 回答: {answer}")
            print(f"📊 置信度: {confidence:.2f} | 意图: {intent}")
            
            # 检查是否是改进后的回答
            if "您的输入比较简短" in answer or "我从您的输入中识别到" in answer:
                print("🎯 ✅ 使用了改进的简短输入处理逻辑")
            elif "👋 您好！我是您的专业技术助手" in answer:
                print("⚠️ 仍然使用旧的通用欢迎信息")
            else:
                print("🤔 使用了其他处理逻辑")
                
        else:
            print(f"❌ 请求失败: {response.status_code}")
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
    
    time.sleep(0.5)  # 避免请求过快

def main():
    """主测试函数"""
    print("🧪 简短输入处理改进测试")
    print("=" * 60)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 测试各种简短输入
    test_cases = [
        # 字母数字组合
        ("b30", "应该识别为简短/不明确输入"),
        ("a1", "应该识别为简短/不明确输入"),
        ("x99", "应该识别为简短/不明确输入"),
        ("test123", "应该识别为简短/不明确输入"),
        
        # 极短输入
        ("hi", "应该识别为问候语"),
        ("你好", "应该识别为问候语"),
        ("ok", "应该识别为确认"),
        ("好", "应该识别为确认"),
        
        # 单字符
        ("a", "应该识别为简短输入"),
        ("1", "应该识别为简短输入"),
        ("?", "应该识别为简短输入"),
        
        # 稍长但仍然不明确的输入
        ("help", "可能识别为帮助请求"),
        ("what", "应该识别为简短/不明确输入"),
        ("how", "应该识别为简短/不明确输入"),
        
        # 对比：正常的技术问题
        ("什么是LocalAI？", "应该正常处理技术问题"),
        ("如何配置MySQL数据库？", "应该正常处理技术问题"),
    ]
    
    print(f"\n📝 将测试 {len(test_cases)} 个输入案例")
    print("-" * 60)
    
    for question, expected in test_cases:
        test_short_input(question, expected)
    
    print("\n" + "=" * 60)
    print("🎉 简短输入处理测试完成！")
    
    print("\n📋 改进总结:")
    print("✅ 添加了简短输入检测逻辑")
    print("✅ 通过NLP处理器分析简短输入")
    print("✅ 为不明确输入提供智能引导")
    print("✅ 区分常见简短表达（如问候语）")
    print("✅ 提供具体的帮助建议")
    
    print(f"\n⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
