package learning

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"math"
	"regexp"
	"strings"
	"time"

	"faq-system/internal/logger"
)

// EvolutionEngine 智能进化引擎 - 真正的学习和进化
type EvolutionEngine struct {
	db     *sql.DB
	config *LearningConfig
}

// UserThinkingPattern 用户思维模式
type UserThinkingPattern struct {
	ID              int                    `json:"id"`
	PatternType     string                 `json:"pattern_type"` // query_style, intent_preference, feedback_pattern
	PatternName     string                 `json:"pattern_name"`
	UserSegment     string                 `json:"user_segment"`     // technical, business, general
	ThinkingStyle   string                 `json:"thinking_style"`   // direct, exploratory, comparative
	QueryPatterns   []string               `json:"query_patterns"`   // 用户常用的查询模式
	PreferredTopics []string               `json:"preferred_topics"` // 偏好的话题
	ResponseStyle   string                 `json:"response_style"`   // detailed, concise, example_based
	Confidence      float32                `json:"confidence"`
	UsageCount      int                    `json:"usage_count"`
	SuccessRate     float32                `json:"success_rate"`
	LastUpdated     time.Time              `json:"last_updated"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// SystemEvolution 系统进化记录
type SystemEvolution struct {
	ID              int       `json:"id"`
	EvolutionType   string    `json:"evolution_type"` // matching_improvement, response_optimization, new_pattern_discovery
	Description     string    `json:"description"`
	BeforeMetrics   string    `json:"before_metrics"`   // JSON格式的改进前指标
	AfterMetrics    string    `json:"after_metrics"`    // JSON格式的改进后指标
	ImprovementRate float32   `json:"improvement_rate"` // 改进幅度
	AppliedAt       time.Time `json:"applied_at"`
	Status          string    `json:"status"` // active, testing, deprecated
}

// NewEvolutionEngine 创建进化引擎
func NewEvolutionEngine(db *sql.DB, config *LearningConfig) *EvolutionEngine {
	return &EvolutionEngine{
		db:     db,
		config: config,
	}
}

// AnalyzeUserThinking 分析用户思维模式
func (ee *EvolutionEngine) AnalyzeUserThinking() error {
	logger.Info("🧠 开始分析用户思维模式...")

	// 1. 分析查询风格模式
	if err := ee.analyzeQueryStyles(); err != nil {
		logger.Errorf("查询风格分析失败: %v", err)
	}

	// 2. 分析意图偏好模式
	if err := ee.analyzeIntentPreferences(); err != nil {
		logger.Errorf("意图偏好分析失败: %v", err)
	}

	// 3. 分析反馈模式
	if err := ee.analyzeFeedbackPatterns(); err != nil {
		logger.Errorf("反馈模式分析失败: %v", err)
	}

	// 4. 发现新的思维模式
	if err := ee.discoverNewPatterns(); err != nil {
		logger.Errorf("新模式发现失败: %v", err)
	}

	logger.Info("✅ 用户思维模式分析完成")
	return nil
}

// analyzeQueryStyles 分析查询风格
func (ee *EvolutionEngine) analyzeQueryStyles() error {
	// 分析用户查询的语言特征
	query := `
		SELECT 
			uq.user_id,
			uq.query_text,
			uq.query_intent,
			sr.confidence_score,
			COALESCE(uf.feedback_type, 'none') as feedback,
			COALESCE(uf.rating, 0) as rating
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		ORDER BY uq.user_id, uq.created_at
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	userQueries := make(map[string][]QueryAnalysis)
	for rows.Next() {
		var qa QueryAnalysis
		err := rows.Scan(&qa.UserID, &qa.QueryText, &qa.Intent, &qa.Confidence, &qa.Feedback, &qa.Rating)
		if err != nil {
			continue
		}
		userQueries[qa.UserID] = append(userQueries[qa.UserID], qa)
	}

	// 为每个用户分析查询风格
	for userID, queries := range userQueries {
		if len(queries) < 3 { // 至少需要3个查询才能分析模式
			continue
		}

		pattern := ee.extractQueryStylePattern(userID, queries)
		if pattern != nil {
			ee.saveUserThinkingPattern(pattern)
		}
	}

	return nil
}

// extractQueryStylePattern 提取查询风格模式
func (ee *EvolutionEngine) extractQueryStylePattern(userID string, queries []QueryAnalysis) *UserThinkingPattern {
	// 分析查询长度分布
	var totalLength, shortQueries, longQueries int
	var questionWords, directQueries int
	var technicalTerms int

	for _, q := range queries {
		length := len(strings.Fields(q.QueryText))
		totalLength += length

		if length <= 3 {
			shortQueries++
		} else if length >= 8 {
			longQueries++
		}

		// 检查是否包含疑问词
		if strings.Contains(q.QueryText, "什么") || strings.Contains(q.QueryText, "如何") ||
			strings.Contains(q.QueryText, "怎么") || strings.Contains(q.QueryText, "为什么") {
			questionWords++
		}

		// 检查是否是直接查询（没有疑问词）
		if !strings.Contains(q.QueryText, "什么") && !strings.Contains(q.QueryText, "如何") {
			directQueries++
		}

		// 检查技术术语
		technicalKeywords := []string{"API", "数据库", "算法", "向量", "MySQL", "Go", "LocalAI"}
		for _, keyword := range technicalKeywords {
			if strings.Contains(q.QueryText, keyword) {
				technicalTerms++
				break
			}
		}
	}

	avgLength := float32(totalLength) / float32(len(queries))

	// 确定思维风格
	var thinkingStyle string
	if float32(questionWords)/float32(len(queries)) > 0.7 {
		thinkingStyle = "exploratory" // 探索型
	} else if float32(directQueries)/float32(len(queries)) > 0.6 {
		thinkingStyle = "direct" // 直接型
	} else {
		thinkingStyle = "comparative" // 比较型
	}

	// 确定用户细分
	var userSegment string
	if float32(technicalTerms)/float32(len(queries)) > 0.5 {
		userSegment = "technical"
	} else {
		userSegment = "general"
	}

	// 确定偏好的响应风格
	var responseStyle string
	if avgLength > 6 {
		responseStyle = "detailed" // 详细型用户偏好详细回答
	} else {
		responseStyle = "concise" // 简洁型用户偏好简洁回答
	}

	// 计算成功率
	var successfulQueries int
	for _, q := range queries {
		if q.Confidence > 0.5 || q.Feedback == "helpful" || q.Rating >= 4 {
			successfulQueries++
		}
	}
	successRate := float32(successfulQueries) / float32(len(queries))

	return &UserThinkingPattern{
		PatternType:   "query_style",
		PatternName:   fmt.Sprintf("%s_%s_style", userSegment, thinkingStyle),
		UserSegment:   userSegment,
		ThinkingStyle: thinkingStyle,
		ResponseStyle: responseStyle,
		Confidence:    0.8,
		UsageCount:    len(queries),
		SuccessRate:   successRate,
		LastUpdated:   time.Now(),
		Metadata: map[string]interface{}{
			"avg_query_length": avgLength,
			"question_ratio":   float32(questionWords) / float32(len(queries)),
			"direct_ratio":     float32(directQueries) / float32(len(queries)),
			"technical_ratio":  float32(technicalTerms) / float32(len(queries)),
		},
	}
}

// analyzeIntentPreferences 分析意图偏好
func (ee *EvolutionEngine) analyzeIntentPreferences() error {
	// 分析不同意图的成功率和用户满意度
	query := `
		SELECT 
			uq.query_intent,
			sr.matched_faq_id,
			AVG(sr.confidence_score) as avg_confidence,
			COUNT(*) as query_count,
			SUM(CASE WHEN uf.feedback_type = 'helpful' THEN 1 ELSE 0 END) as helpful_count,
			AVG(COALESCE(uf.rating, 0)) as avg_rating
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		  AND uq.query_intent IS NOT NULL
		GROUP BY uq.query_intent, sr.matched_faq_id
		HAVING query_count >= 3
		ORDER BY avg_confidence DESC, avg_rating DESC
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	intentPatterns := make(map[string]*UserThinkingPattern)
	for rows.Next() {
		var intent string
		var faqID sql.NullInt64
		var avgConfidence float32
		var queryCount, helpfulCount int
		var avgRating float32

		err := rows.Scan(&intent, &faqID, &avgConfidence, &queryCount, &helpfulCount, &avgRating)
		if err != nil {
			continue
		}

		successRate := float32(helpfulCount) / float32(queryCount)

		// 如果这个意图模式表现良好，记录为用户偏好
		if avgConfidence > 0.6 && successRate > 0.7 {
			pattern := &UserThinkingPattern{
				PatternType:   "intent_preference",
				PatternName:   fmt.Sprintf("preferred_%s", intent),
				UserSegment:   "general",
				ThinkingStyle: intent,
				Confidence:    avgConfidence,
				UsageCount:    queryCount,
				SuccessRate:   successRate,
				LastUpdated:   time.Now(),
				Metadata: map[string]interface{}{
					"avg_confidence": avgConfidence,
					"avg_rating":     avgRating,
					"helpful_ratio":  successRate,
				},
			}
			intentPatterns[intent] = pattern
		}
	}

	// 保存意图偏好模式
	for _, pattern := range intentPatterns {
		ee.saveUserThinkingPattern(pattern)
	}

	return nil
}

// analyzeFeedbackPatterns 分析反馈模式
func (ee *EvolutionEngine) analyzeFeedbackPatterns() error {
	// 分析用户反馈的模式，了解用户对不同类型回答的偏好
	query := `
		SELECT 
			sr.response_source,
			sr.confidence_score,
			uf.feedback_type,
			uf.rating,
			uf.feedback_text,
			COUNT(*) as feedback_count
		FROM system_responses sr
		JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE sr.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		GROUP BY sr.response_source, sr.confidence_score, uf.feedback_type, uf.rating
		HAVING feedback_count >= 2
		ORDER BY feedback_count DESC
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	feedbackPatterns := make(map[string]*UserThinkingPattern)
	for rows.Next() {
		var source, feedbackType string
		var confidence float32
		var rating, feedbackCount int
		var feedbackText sql.NullString

		err := rows.Scan(&source, &confidence, &feedbackType, &rating, &feedbackText, &feedbackCount)
		if err != nil {
			continue
		}

		// 分析正面反馈的模式
		if feedbackType == "helpful" && rating >= 4 {
			patternName := fmt.Sprintf("positive_%s_feedback", strings.ReplaceAll(source, " ", "_"))

			pattern := &UserThinkingPattern{
				PatternType:   "feedback_pattern",
				PatternName:   patternName,
				UserSegment:   "general",
				ThinkingStyle: "positive_feedback",
				ResponseStyle: source,
				Confidence:    confidence,
				UsageCount:    feedbackCount,
				SuccessRate:   1.0, // 正面反馈的成功率为100%
				LastUpdated:   time.Now(),
				Metadata: map[string]interface{}{
					"source":         source,
					"avg_confidence": confidence,
					"avg_rating":     rating,
					"feedback_type":  feedbackType,
				},
			}
			feedbackPatterns[patternName] = pattern
		}
	}

	// 保存反馈模式
	for _, pattern := range feedbackPatterns {
		ee.saveUserThinkingPattern(pattern)
	}

	return nil
}

// QueryAnalysis 查询分析结构
type QueryAnalysis struct {
	UserID     string
	QueryText  string
	Intent     string
	Confidence float32
	Feedback   string
	Rating     int
}

// discoverNewPatterns 发现新的思维模式
func (ee *EvolutionEngine) discoverNewPatterns() error {
	logger.Info("🔍 发现新的用户思维模式...")

	// 使用聚类分析发现新的查询模式
	query := `
		SELECT
			uq.query_text,
			uq.query_intent,
			sr.confidence_score,
			sr.response_source,
			COALESCE(uf.feedback_type, 'none') as feedback
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		  AND sr.confidence_score < 0.5  -- 关注低置信度的查询，可能是新模式
		ORDER BY uq.created_at DESC
		LIMIT 100
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	var lowConfidenceQueries []QueryAnalysis
	for rows.Next() {
		var qa QueryAnalysis
		var source string
		err := rows.Scan(&qa.QueryText, &qa.Intent, &qa.Confidence, &source, &qa.Feedback)
		if err != nil {
			continue
		}
		lowConfidenceQueries = append(lowConfidenceQueries, qa)
	}

	// 分析这些低置信度查询的共同特征
	if len(lowConfidenceQueries) >= 5 {
		newPattern := ee.analyzeEmergingPattern(lowConfidenceQueries)
		if newPattern != nil {
			ee.saveUserThinkingPattern(newPattern)
			logger.Infof("🆕 发现新的思维模式: %s", newPattern.PatternName)
		}
	}

	return nil
}

// analyzeEmergingPattern 分析新兴模式
func (ee *EvolutionEngine) analyzeEmergingPattern(queries []QueryAnalysis) *UserThinkingPattern {
	// 分析查询的共同特征
	commonKeywords := ee.findCommonKeywords(queries)
	if len(commonKeywords) == 0 {
		return nil
	}

	// 计算平均置信度
	var totalConfidence float32
	for _, q := range queries {
		totalConfidence += q.Confidence
	}
	avgConfidence := totalConfidence / float32(len(queries))

	// 如果发现了有意义的模式
	if len(commonKeywords) >= 2 && avgConfidence < 0.3 {
		return &UserThinkingPattern{
			PatternType:   "emerging_pattern",
			PatternName:   fmt.Sprintf("emerging_%s", strings.Join(commonKeywords[:2], "_")),
			UserSegment:   "general",
			ThinkingStyle: "emerging",
			QueryPatterns: commonKeywords,
			Confidence:    0.6, // 新模式的初始置信度
			UsageCount:    len(queries),
			SuccessRate:   avgConfidence, // 使用置信度作为成功率的初始值
			LastUpdated:   time.Now(),
			Metadata: map[string]interface{}{
				"avg_confidence":   avgConfidence,
				"pattern_keywords": commonKeywords,
				"discovery_date":   time.Now().Format("2006-01-02"),
			},
		}
	}

	return nil
}

// findCommonKeywords 查找共同关键词
func (ee *EvolutionEngine) findCommonKeywords(queries []QueryAnalysis) []string {
	wordCount := make(map[string]int)

	for _, q := range queries {
		words := strings.Fields(strings.ToLower(q.QueryText))
		for _, word := range words {
			// 过滤掉常见的停用词
			if !ee.isStopWord(word) && len(word) > 1 {
				wordCount[word]++
			}
		}
	}

	// 找出出现频率高的词
	var commonWords []string
	threshold := len(queries) / 3 // 至少在1/3的查询中出现
	for word, count := range wordCount {
		if count >= threshold {
			commonWords = append(commonWords, word)
		}
	}

	return commonWords
}

// isStopWord 判断是否为停用词
func (ee *EvolutionEngine) isStopWord(word string) bool {
	stopWords := []string{"的", "是", "在", "有", "和", "与", "或", "但", "如果", "那么", "这个", "那个", "什么", "如何", "怎么", "为什么"}
	for _, stopWord := range stopWords {
		if word == stopWord {
			return true
		}
	}
	return false
}

// saveUserThinkingPattern 保存用户思维模式
func (ee *EvolutionEngine) saveUserThinkingPattern(pattern *UserThinkingPattern) error {
	// 检查是否已存在相似模式
	existingID, err := ee.findSimilarPattern(pattern)
	if err == nil && existingID > 0 {
		// 更新现有模式
		return ee.updateThinkingPattern(existingID, pattern)
	}

	// 创建新模式
	metadataJSON, _ := json.Marshal(pattern.Metadata)
	queryPatternsJSON, _ := json.Marshal(pattern.QueryPatterns)
	preferredTopicsJSON, _ := json.Marshal(pattern.PreferredTopics)

	query := `
		INSERT INTO user_thinking_patterns
		(pattern_type, pattern_name, user_segment, thinking_style, query_patterns,
		 preferred_topics, response_style, confidence, usage_count, success_rate, metadata)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err = ee.db.Exec(query,
		pattern.PatternType, pattern.PatternName, pattern.UserSegment, pattern.ThinkingStyle,
		queryPatternsJSON, preferredTopicsJSON, pattern.ResponseStyle,
		pattern.Confidence, pattern.UsageCount, pattern.SuccessRate, metadataJSON)

	if err != nil {
		return fmt.Errorf("保存思维模式失败: %v", err)
	}

	logger.Infof("💾 保存新的思维模式: %s (成功率: %.2f)", pattern.PatternName, pattern.SuccessRate)
	return nil
}

// findSimilarPattern 查找相似模式
func (ee *EvolutionEngine) findSimilarPattern(pattern *UserThinkingPattern) (int, error) {
	query := `
		SELECT id FROM user_thinking_patterns
		WHERE pattern_type = ? AND user_segment = ? AND thinking_style = ?
		LIMIT 1
	`

	var id int
	err := ee.db.QueryRow(query, pattern.PatternType, pattern.UserSegment, pattern.ThinkingStyle).Scan(&id)
	if err == sql.ErrNoRows {
		return 0, nil
	}
	return id, err
}

// updateThinkingPattern 更新思维模式
func (ee *EvolutionEngine) updateThinkingPattern(id int, pattern *UserThinkingPattern) error {
	metadataJSON, _ := json.Marshal(pattern.Metadata)

	query := `
		UPDATE user_thinking_patterns
		SET usage_count = usage_count + ?,
		    success_rate = (success_rate + ?) / 2,
		    confidence = (confidence + ?) / 2,
		    metadata = ?,
		    last_updated = NOW()
		WHERE id = ?
	`

	_, err := ee.db.Exec(query, pattern.UsageCount, pattern.SuccessRate, pattern.Confidence, metadataJSON, id)
	if err != nil {
		return fmt.Errorf("更新思维模式失败: %v", err)
	}

	logger.Infof("🔄 更新思维模式 ID=%d", id)
	return nil
}

// ApplyEvolutionaryImprovements 应用进化改进 - 真正的知识学习和持久化
func (ee *EvolutionEngine) ApplyEvolutionaryImprovements() error {
	logger.Info("🚀 开始应用进化改进...")

	// 1. 从用户交互中学习新知识并持久化到数据库
	if err := ee.learnAndPersistKnowledge(); err != nil {
		logger.Errorf("知识学习和持久化失败: %v", err)
	}

	// 2. 基于用户思维模式优化匹配算法
	if err := ee.optimizeMatchingAlgorithm(); err != nil {
		logger.Errorf("匹配算法优化失败: %v", err)
	}

	// 3. 基于反馈模式优化响应生成
	if err := ee.optimizeResponseGeneration(); err != nil {
		logger.Errorf("响应生成优化失败: %v", err)
	}

	// 4. 动态调整系统参数
	if err := ee.adaptSystemParameters(); err != nil {
		logger.Errorf("系统参数调整失败: %v", err)
	}

	// 5. 更新知识向量和索引
	if err := ee.updateKnowledgeVectors(); err != nil {
		logger.Errorf("知识向量更新失败: %v", err)
	}

	// 6. 记录进化历史
	if err := ee.recordEvolutionHistory(); err != nil {
		logger.Errorf("进化历史记录失败: %v", err)
	}

	logger.Info("✅ 进化改进应用完成")
	return nil
}

// optimizeMatchingAlgorithm 优化匹配算法
func (ee *EvolutionEngine) optimizeMatchingAlgorithm() error {
	// 获取表现最好的思维模式
	patterns, err := ee.getBestPerformingPatterns()
	if err != nil {
		return err
	}

	for _, pattern := range patterns {
		// 根据成功的模式调整匹配权重
		if pattern.SuccessRate > 0.8 {
			err := ee.adjustMatchingWeights(pattern)
			if err != nil {
				logger.Warnf("调整匹配权重失败: %v", err)
			}
		}
	}

	return nil
}

// getBestPerformingPatterns 获取表现最好的模式
func (ee *EvolutionEngine) getBestPerformingPatterns() ([]*UserThinkingPattern, error) {
	query := `
		SELECT pattern_type, pattern_name, user_segment, thinking_style,
		       response_style, confidence, usage_count, success_rate, metadata
		FROM user_thinking_patterns
		WHERE success_rate > 0.7 AND usage_count >= 5
		ORDER BY success_rate DESC, usage_count DESC
		LIMIT 10
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	var patterns []*UserThinkingPattern
	for rows.Next() {
		pattern := &UserThinkingPattern{}
		var metadataJSON string

		err := rows.Scan(&pattern.PatternType, &pattern.PatternName, &pattern.UserSegment,
			&pattern.ThinkingStyle, &pattern.ResponseStyle, &pattern.Confidence,
			&pattern.UsageCount, &pattern.SuccessRate, &metadataJSON)
		if err != nil {
			continue
		}

		json.Unmarshal([]byte(metadataJSON), &pattern.Metadata)
		patterns = append(patterns, pattern)
	}

	return patterns, nil
}

// adjustMatchingWeights 调整匹配权重
func (ee *EvolutionEngine) adjustMatchingWeights(pattern *UserThinkingPattern) error {
	// 根据用户思维模式调整FAQ匹配的权重
	var adjustmentFactor float32 = 1.0 + (pattern.SuccessRate-0.5)*0.5 // 成功率越高，权重调整越大

	// 更新学习配置中的权重参数
	configUpdates := map[string]interface{}{
		fmt.Sprintf("weight_%s", pattern.ThinkingStyle): adjustmentFactor,
		fmt.Sprintf("boost_%s", pattern.UserSegment):    pattern.SuccessRate,
	}

	for key, value := range configUpdates {
		err := ee.updateLearningConfig(key, value)
		if err != nil {
			logger.Warnf("更新配置失败 %s: %v", key, err)
		}
	}

	logger.Infof("🎯 调整匹配权重: %s -> %.3f", pattern.PatternName, adjustmentFactor)
	return nil
}

// optimizeResponseGeneration 优化响应生成
func (ee *EvolutionEngine) optimizeResponseGeneration() error {
	// 分析用户偏好的响应风格
	stylePreferences, err := ee.analyzeResponseStylePreferences()
	if err != nil {
		return err
	}

	// 根据偏好调整响应生成策略
	for style, preference := range stylePreferences {
		if preference > 0.7 {
			err := ee.updateResponseStrategy(style, preference)
			if err != nil {
				logger.Warnf("更新响应策略失败 %s: %v", style, err)
			}
		}
	}

	return nil
}

// analyzeResponseStylePreferences 分析响应风格偏好
func (ee *EvolutionEngine) analyzeResponseStylePreferences() (map[string]float32, error) {
	query := `
		SELECT
			utp.response_style,
			AVG(utp.success_rate) as avg_success_rate,
			COUNT(*) as pattern_count
		FROM user_thinking_patterns utp
		WHERE utp.pattern_type = 'feedback_pattern'
		  AND utp.success_rate > 0.5
		GROUP BY utp.response_style
		HAVING pattern_count >= 2
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	preferences := make(map[string]float32)
	for rows.Next() {
		var style string
		var avgSuccessRate float32
		var count int

		err := rows.Scan(&style, &avgSuccessRate, &count)
		if err != nil {
			continue
		}

		preferences[style] = avgSuccessRate
	}

	return preferences, nil
}

// updateResponseStrategy 更新响应策略
func (ee *EvolutionEngine) updateResponseStrategy(style string, preference float32) error {
	// 根据用户偏好更新响应生成的策略配置
	configKey := fmt.Sprintf("response_style_%s_weight", strings.ReplaceAll(style, " ", "_"))
	return ee.updateLearningConfig(configKey, preference)
}

// adaptSystemParameters 动态调整系统参数
func (ee *EvolutionEngine) adaptSystemParameters() error {
	// 基于系统表现动态调整关键参数

	// 1. 调整置信度阈值
	optimalThreshold, err := ee.calculateOptimalConfidenceThreshold()
	if err == nil {
		ee.updateLearningConfig("min_confidence_threshold", optimalThreshold)
		logger.Infof("🎛️ 调整置信度阈值: %.3f", optimalThreshold)
	}

	// 2. 调整反馈权重
	optimalFeedbackWeight, err := ee.calculateOptimalFeedbackWeight()
	if err == nil {
		ee.updateLearningConfig("feedback_weight", optimalFeedbackWeight)
		logger.Infof("🎛️ 调整反馈权重: %.3f", optimalFeedbackWeight)
	}

	return nil
}

// calculateOptimalConfidenceThreshold 计算最优置信度阈值
func (ee *EvolutionEngine) calculateOptimalConfidenceThreshold() (float32, error) {
	// 分析不同置信度阈值下的系统表现
	query := `
		SELECT
			CASE
				WHEN sr.confidence_score >= 0.8 THEN '0.8+'
				WHEN sr.confidence_score >= 0.6 THEN '0.6-0.8'
				WHEN sr.confidence_score >= 0.4 THEN '0.4-0.6'
				WHEN sr.confidence_score >= 0.2 THEN '0.2-0.4'
				ELSE '0.0-0.2'
			END as confidence_range,
			COUNT(*) as total_responses,
			SUM(CASE WHEN uf.feedback_type = 'helpful' THEN 1 ELSE 0 END) as helpful_count,
			AVG(COALESCE(uf.rating, 0)) as avg_rating
		FROM system_responses sr
		LEFT JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE sr.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		GROUP BY confidence_range
		ORDER BY confidence_range DESC
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return 0, err
	}
	defer rows.Close()

	bestThreshold := float32(0.3) // 默认阈值
	bestScore := float32(0)

	for rows.Next() {
		var confidenceRange string
		var totalResponses, helpfulCount int
		var avgRating float32

		err := rows.Scan(&confidenceRange, &totalResponses, &helpfulCount, &avgRating)
		if err != nil {
			continue
		}

		if totalResponses > 0 {
			helpfulRatio := float32(helpfulCount) / float32(totalResponses)
			// 综合考虑有用率和评分
			score := helpfulRatio*0.7 + (avgRating/5.0)*0.3

			if score > bestScore {
				bestScore = score
				// 根据范围设置阈值
				switch confidenceRange {
				case "0.8+":
					bestThreshold = 0.8
				case "0.6-0.8":
					bestThreshold = 0.6
				case "0.4-0.6":
					bestThreshold = 0.4
				case "0.2-0.4":
					bestThreshold = 0.2
				default:
					bestThreshold = 0.1
				}
			}
		}
	}

	return bestThreshold, nil
}

// calculateOptimalFeedbackWeight 计算最优反馈权重
func (ee *EvolutionEngine) calculateOptimalFeedbackWeight() (float32, error) {
	// 分析反馈权重对系统改进的影响
	query := `
		SELECT
			AVG(CASE WHEN uf.feedback_type = 'helpful' THEN 1.0 ELSE 0.0 END) as helpful_ratio,
			AVG(COALESCE(uf.rating, 0)) as avg_rating,
			COUNT(*) as feedback_count
		FROM user_feedback uf
		WHERE uf.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
	`

	var helpfulRatio, avgRating float32
	var feedbackCount int

	err := ee.db.QueryRow(query).Scan(&helpfulRatio, &avgRating, &feedbackCount)
	if err != nil {
		return 0.7, err // 返回默认值
	}

	// 根据反馈质量计算最优权重
	if feedbackCount > 10 {
		// 反馈质量越高，权重越大
		optimalWeight := helpfulRatio*0.6 + (avgRating/5.0)*0.4
		// 限制在合理范围内
		if optimalWeight < 0.3 {
			optimalWeight = 0.3
		} else if optimalWeight > 0.9 {
			optimalWeight = 0.9
		}
		return optimalWeight, nil
	}

	return 0.7, nil // 默认权重
}

// updateLearningConfig 更新学习配置
func (ee *EvolutionEngine) updateLearningConfig(key string, value interface{}) error {
	// 确定配置值的类型
	var configValue string
	var configType string

	switch v := value.(type) {
	case float32:
		configValue = fmt.Sprintf("%.6f", v)
		configType = "float"
	case int:
		configValue = fmt.Sprintf("%d", v)
		configType = "int"
	case bool:
		configValue = fmt.Sprintf("%t", v)
		configType = "boolean"
	default:
		configValue = fmt.Sprintf("%v", v)
		configType = "string"
	}

	query := `
		INSERT INTO learning_config (config_key, config_value, config_type, description)
		VALUES (?, ?, ?, ?)
		ON DUPLICATE KEY UPDATE
		config_value = VALUES(config_value),
		config_type = VALUES(config_type),
		updated_at = NOW()
	`

	description := fmt.Sprintf("Auto-optimized parameter for %s", key)
	_, err := ee.db.Exec(query, key, configValue, configType, description)
	if err != nil {
		return fmt.Errorf("更新学习配置失败: %v", err)
	}

	logger.Infof("⚙️ 更新配置: %s = %s", key, configValue)
	return nil
}

// recordEvolutionHistory 记录进化历史
func (ee *EvolutionEngine) recordEvolutionHistory() error {
	// 获取当前系统指标
	currentMetrics, err := ee.getCurrentSystemMetrics()
	if err != nil {
		return err
	}

	// 获取上次记录的指标
	lastMetrics, err := ee.getLastSystemMetrics()
	if err != nil {
		// 如果没有历史记录，直接保存当前指标
		return ee.saveEvolutionRecord("system_initialization", "系统初始化", "", currentMetrics, 0.0)
	}

	// 计算改进率
	improvementRate := ee.calculateImprovementRate(lastMetrics, currentMetrics)

	// 如果有显著改进，记录进化历史
	if math.Abs(float64(improvementRate)) > 0.05 { // 5%以上的变化才记录
		evolutionType := "performance_optimization"
		description := fmt.Sprintf("系统性能优化，改进率: %.2f%%", improvementRate*100)

		if improvementRate > 0 {
			description = "✅ " + description
		} else {
			description = "⚠️ " + description
		}

		return ee.saveEvolutionRecord(evolutionType, description, lastMetrics, currentMetrics, improvementRate)
	}

	return nil
}

// getCurrentSystemMetrics 获取当前系统指标
func (ee *EvolutionEngine) getCurrentSystemMetrics() (string, error) {
	metrics := make(map[string]interface{})

	// 查询各种系统指标
	queries := map[string]string{
		"avg_confidence": "SELECT AVG(confidence_score) FROM system_responses WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
		"avg_rating":     "SELECT AVG(rating) FROM user_feedback WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
		"helpful_ratio":  "SELECT AVG(CASE WHEN feedback_type = 'helpful' THEN 1.0 ELSE 0.0 END) FROM user_feedback WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
		"response_time":  "SELECT AVG(processing_time_ms) FROM system_responses WHERE created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)",
	}

	for key, query := range queries {
		var value sql.NullFloat64
		err := ee.db.QueryRow(query).Scan(&value)
		if err == nil && value.Valid {
			metrics[key] = value.Float64
		} else {
			metrics[key] = 0.0
		}
	}

	// 添加时间戳
	metrics["timestamp"] = time.Now().Format("2006-01-02 15:04:05")

	metricsJSON, err := json.Marshal(metrics)
	return string(metricsJSON), err
}

// getLastSystemMetrics 获取上次系统指标
func (ee *EvolutionEngine) getLastSystemMetrics() (string, error) {
	query := `
		SELECT after_metrics
		FROM system_evolution
		WHERE status = 'active'
		ORDER BY applied_at DESC
		LIMIT 1
	`

	var metrics string
	err := ee.db.QueryRow(query).Scan(&metrics)
	if err == sql.ErrNoRows {
		return "", fmt.Errorf("no previous metrics found")
	}
	return metrics, err
}

// calculateImprovementRate 计算改进率
func (ee *EvolutionEngine) calculateImprovementRate(lastMetrics, currentMetrics string) float32 {
	var last, current map[string]interface{}

	json.Unmarshal([]byte(lastMetrics), &last)
	json.Unmarshal([]byte(currentMetrics), &current)

	// 计算关键指标的综合改进率
	improvements := []float32{}

	// 置信度改进
	if lastConf, ok := last["avg_confidence"].(float64); ok {
		if currentConf, ok := current["avg_confidence"].(float64); ok && lastConf > 0 {
			improvement := float32((currentConf - lastConf) / lastConf)
			improvements = append(improvements, improvement)
		}
	}

	// 用户满意度改进
	if lastRating, ok := last["avg_rating"].(float64); ok {
		if currentRating, ok := current["avg_rating"].(float64); ok && lastRating > 0 {
			improvement := float32((currentRating - lastRating) / lastRating)
			improvements = append(improvements, improvement)
		}
	}

	// 有用率改进
	if lastHelpful, ok := last["helpful_ratio"].(float64); ok {
		if currentHelpful, ok := current["helpful_ratio"].(float64); ok && lastHelpful > 0 {
			improvement := float32((currentHelpful - lastHelpful) / lastHelpful)
			improvements = append(improvements, improvement)
		}
	}

	// 计算平均改进率
	if len(improvements) > 0 {
		var total float32
		for _, imp := range improvements {
			total += imp
		}
		return total / float32(len(improvements))
	}

	return 0.0
}

// saveEvolutionRecord 保存进化记录
func (ee *EvolutionEngine) saveEvolutionRecord(evolutionType, description, beforeMetrics, afterMetrics string, improvementRate float32) error {
	query := `
		INSERT INTO system_evolution (evolution_type, description, before_metrics, after_metrics, improvement_rate, applied_at, status)
		VALUES (?, ?, ?, ?, ?, NOW(), 'active')
	`

	_, err := ee.db.Exec(query, evolutionType, description, beforeMetrics, afterMetrics, improvementRate)
	if err != nil {
		return fmt.Errorf("保存进化记录失败: %v", err)
	}

	logger.Infof("📈 记录系统进化: %s (改进率: %.2f%%)", description, improvementRate*100)
	return nil
}

// learnAndPersistKnowledge 从用户交互中学习新知识并持久化到数据库
func (ee *EvolutionEngine) learnAndPersistKnowledge() error {
	logger.Info("🧠 开始从用户交互中学习新知识...")

	// 1. 分析最近的用户查询和反馈，发现新的知识模式
	newKnowledge, err := ee.extractKnowledgeFromInteractions()
	if err != nil {
		return fmt.Errorf("从交互中提取知识失败: %v", err)
	}

	// 2. 将学习到的知识持久化到learned_knowledge表
	for _, knowledge := range newKnowledge {
		if err := ee.persistKnowledgeToDatabase(knowledge); err != nil {
			logger.Warnf("持久化知识失败: %v", err)
			continue
		}
		logger.Infof("✅ 成功学习并持久化知识: %s", knowledge.Question)
	}

	// 3. 从用户纠错中学习
	corrections, err := ee.learnFromUserCorrections()
	if err != nil {
		logger.Warnf("从用户纠错中学习失败: %v", err)
	} else {
		for _, correction := range corrections {
			if err := ee.persistKnowledgeToDatabase(correction); err != nil {
				logger.Warnf("持久化纠错知识失败: %v", err)
				continue
			}
			logger.Infof("✅ 成功学习并持久化纠错知识: %s", correction.Question)
		}
	}

	// 4. 从成功的问答对中学习
	successfulQA, err := ee.learnFromSuccessfulQA()
	if err != nil {
		logger.Warnf("从成功问答中学习失败: %v", err)
	} else {
		for _, qa := range successfulQA {
			if err := ee.persistKnowledgeToDatabase(qa); err != nil {
				logger.Warnf("持久化成功问答知识失败: %v", err)
				continue
			}
			logger.Infof("✅ 成功学习并持久化问答知识: %s", qa.Question)
		}
	}

	logger.Infof("🎯 知识学习完成，共学习到 %d 条新知识", len(newKnowledge)+len(corrections)+len(successfulQA))
	return nil
}

// updateKnowledgeVectors 更新知识向量和索引
func (ee *EvolutionEngine) updateKnowledgeVectors() error {
	logger.Info("🔄 开始更新知识向量...")

	// 1. 查找没有向量的学习知识
	query := `
		SELECT lk.id, lk.question, lk.answer, lk.category, lk.keywords
		FROM learned_knowledge lk
		LEFT JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE kv.knowledge_id IS NULL AND lk.status = 'approved'
		ORDER BY lk.created_at DESC
		LIMIT 100
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return fmt.Errorf("查询无向量知识失败: %v", err)
	}
	defer rows.Close()

	var knowledgeList []struct {
		ID       int
		Question string
		Answer   string
		Category string
		Keywords string
	}

	for rows.Next() {
		var k struct {
			ID       int
			Question string
			Answer   string
			Category string
			Keywords string
		}
		err := rows.Scan(&k.ID, &k.Question, &k.Answer, &k.Category, &k.Keywords)
		if err != nil {
			logger.Warnf("扫描知识记录失败: %v", err)
			continue
		}
		knowledgeList = append(knowledgeList, k)
	}

	// 2. 为每个知识生成向量并存储
	for _, knowledge := range knowledgeList {
		vector, err := ee.generateKnowledgeVector(knowledge.Question, knowledge.Answer)
		if err != nil {
			logger.Warnf("生成知识向量失败 (ID=%d): %v", knowledge.ID, err)
			continue
		}

		// 存储向量到knowledge_vectors表
		if err := ee.storeKnowledgeVector(knowledge.ID, vector); err != nil {
			logger.Warnf("存储知识向量失败 (ID=%d): %v", knowledge.ID, err)
			continue
		}

		logger.Infof("✅ 成功为知识 ID=%d 生成并存储向量", knowledge.ID)
	}

	// 3. 更新现有向量（如果知识内容有更新）
	if err := ee.updateExistingVectors(); err != nil {
		logger.Warnf("更新现有向量失败: %v", err)
	}

	logger.Infof("🎯 知识向量更新完成，处理了 %d 条知识", len(knowledgeList))
	return nil
}

// extractKnowledgeFromInteractions 从用户交互中提取知识
func (ee *EvolutionEngine) extractKnowledgeFromInteractions() ([]*LearnedKnowledge, error) {
	// 由于FAQ表已删除，直接从用户查询和系统响应中学习
	knowledgeFromQueries, err := ee.extractKnowledgeFromQueries()
	if err != nil {
		logger.Warnf("从查询中提取知识失败: %v", err)
		return []*LearnedKnowledge{}, err
	}

	logger.Infof("从用户交互中提取到 %d 条知识", len(knowledgeFromQueries))
	return knowledgeFromQueries, nil
}

// extractKnowledgeFromQueries 从用户查询中提取知识（不依赖反馈表）
func (ee *EvolutionEngine) extractKnowledgeFromQueries() ([]*LearnedKnowledge, error) {
	// 查询最近的高置信度问答对，不依赖user_feedback表
	query := `
		SELECT uq.query_text, sr.response_text, uq.user_id, sr.confidence_score, uq.query_intent
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 7 DAY)
		  AND sr.confidence_score > 0.8
		  AND LENGTH(uq.query_text) > 5
		  AND LENGTH(sr.response_text) > 20
		  AND uq.query_intent NOT IN ('greeting', 'thanks', 'apology')
		ORDER BY sr.confidence_score DESC, uq.created_at DESC
		LIMIT 30
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询用户交互失败: %v", err)
	}
	defer rows.Close()

	var knowledgeList []*LearnedKnowledge
	for rows.Next() {
		var queryText, responseText, userID, queryIntent string
		var confidence float32

		err := rows.Scan(&queryText, &responseText, &userID, &confidence, &queryIntent)
		if err != nil {
			continue
		}

		// 创建学习知识
		knowledge := &LearnedKnowledge{
			Question:    queryText,
			Answer:      responseText,
			Source:      "high_confidence_qa",
			Confidence:  confidence,
			Category:    ee.categorizeQuery(queryIntent),
			Keywords:    ee.extractKeywords(queryText + " " + responseText),
			Context:     fmt.Sprintf("从高置信度问答中学习，用户ID: %s", userID),
			LearnedFrom: userID,
			Status:      "approved",
			Metadata: map[string]interface{}{
				"original_intent":     queryIntent,
				"original_confidence": confidence,
				"learning_source":     "high_confidence_qa",
				"extraction_time":     time.Now().Format(time.RFC3339),
			},
		}

		// 验证知识质量
		if ee.validateKnowledgeQuality(knowledge) {
			knowledgeList = append(knowledgeList, knowledge)
		}
	}

	return knowledgeList, nil
}

// learnFromUserCorrections 从用户纠错中学习
func (ee *EvolutionEngine) learnFromUserCorrections() ([]*LearnedKnowledge, error) {
	// 由于user_feedback表可能没有数据，我们采用更保守的方法
	// 查询是否存在user_feedback表和数据
	var tableExists bool
	checkTableQuery := `
		SELECT COUNT(*) > 0
		FROM INFORMATION_SCHEMA.TABLES
		WHERE TABLE_SCHEMA = DATABASE()
		AND TABLE_NAME = 'user_feedback'
	`

	if err := ee.db.QueryRow(checkTableQuery).Scan(&tableExists); err != nil || !tableExists {
		logger.Infof("user_feedback表不存在或无法访问，跳过用户纠错学习")
		return []*LearnedKnowledge{}, nil
	}

	// 检查是否有纠错数据
	var correctionCount int
	countQuery := `
		SELECT COUNT(*)
		FROM user_feedback
		WHERE feedback_type = 'correction'
		AND created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
	`

	if err := ee.db.QueryRow(countQuery).Scan(&correctionCount); err != nil || correctionCount == 0 {
		logger.Infof("没有找到用户纠错数据，跳过纠错学习")
		return []*LearnedKnowledge{}, nil
	}

	// 查询用户纠错反馈
	query := `
		SELECT uq.query_text, sr.response_text, uf.feedback_content, uq.user_id
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		JOIN user_feedback uf ON sr.id = uf.response_id
		WHERE uf.feedback_type = 'correction'
		  AND uq.created_at > DATE_SUB(NOW(), INTERVAL 30 DAY)
		  AND LENGTH(uf.feedback_content) > 10
		ORDER BY uq.created_at DESC
		LIMIT 20
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询用户纠错失败: %v", err)
	}
	defer rows.Close()

	var corrections []*LearnedKnowledge
	for rows.Next() {
		var queryText, responseText, correctionContent, userID string

		err := rows.Scan(&queryText, &responseText, &correctionContent, &userID)
		if err != nil {
			continue
		}

		// 创建纠错知识
		knowledge := &LearnedKnowledge{
			Question:    queryText,
			Answer:      correctionContent, // 使用用户提供的正确答案
			Source:      "user_correction",
			Confidence:  0.9, // 用户纠错通常质量较高
			Category:    "user_correction",
			Keywords:    ee.extractKeywords(queryText + " " + correctionContent),
			Context:     fmt.Sprintf("用户纠错学习，原答案: %s", responseText),
			LearnedFrom: userID,
			Status:      "approved",
			Metadata: map[string]interface{}{
				"original_answer":  responseText,
				"corrected_answer": correctionContent,
				"learning_source":  "user_correction",
				"correction_time":  time.Now().Format(time.RFC3339),
			},
		}

		if ee.validateKnowledgeQuality(knowledge) {
			corrections = append(corrections, knowledge)
		}
	}

	logger.Infof("从用户纠错中学习到 %d 条知识", len(corrections))
	return corrections, nil
}

// learnFromSuccessfulQA 从成功的问答对中学习
func (ee *EvolutionEngine) learnFromSuccessfulQA() ([]*LearnedKnowledge, error) {
	// 简化查询，不依赖user_feedback表的评分，只基于高置信度
	query := `
		SELECT uq.query_text, sr.response_text, uq.user_id, sr.confidence_score, uq.query_intent
		FROM user_queries uq
		JOIN system_responses sr ON uq.id = sr.query_id
		WHERE uq.created_at > DATE_SUB(NOW(), INTERVAL 14 DAY)
		  AND sr.confidence_score > 0.85
		  AND LENGTH(uq.query_text) > 5
		  AND LENGTH(sr.response_text) > 30
		  AND uq.query_intent NOT IN ('greeting', 'thanks', 'apology', 'chat')
		ORDER BY sr.confidence_score DESC, uq.created_at DESC
		LIMIT 25
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return nil, fmt.Errorf("查询成功问答失败: %v", err)
	}
	defer rows.Close()

	var successfulQA []*LearnedKnowledge
	for rows.Next() {
		var queryText, responseText, userID, queryIntent string
		var confidence float32

		err := rows.Scan(&queryText, &responseText, &userID, &confidence, &queryIntent)
		if err != nil {
			continue
		}

		// 创建成功问答知识
		knowledge := &LearnedKnowledge{
			Question:    queryText,
			Answer:      responseText,
			Source:      "successful_qa",
			Confidence:  confidence,
			Category:    ee.categorizeQuery(queryIntent),
			Keywords:    ee.extractKeywords(queryText + " " + responseText),
			Context:     fmt.Sprintf("高置信度问答学习，置信度: %.2f", confidence),
			LearnedFrom: "system",
			Status:      "approved",
			Metadata: map[string]interface{}{
				"original_intent":     queryIntent,
				"original_confidence": confidence,
				"learning_source":     "successful_qa",
				"extraction_time":     time.Now().Format(time.RFC3339),
			},
		}

		if ee.validateKnowledgeQuality(knowledge) {
			successfulQA = append(successfulQA, knowledge)
		}
	}

	logger.Infof("从成功问答中学习到 %d 条知识", len(successfulQA))
	return successfulQA, nil
}

// persistKnowledgeToDatabase 将知识持久化到数据库（包含NLP处理和内容审核）
func (ee *EvolutionEngine) persistKnowledgeToDatabase(knowledge *LearnedKnowledge) error {
	// 1. NLP内容分析和预处理
	if err := ee.preprocessKnowledgeWithNLP(knowledge); err != nil {
		return fmt.Errorf("NLP预处理失败: %v", err)
	}

	// 2. 内容安全审核（法律道德检查）
	if err := ee.validateContentSafety(knowledge); err != nil {
		return fmt.Errorf("内容安全审核失败: %v", err)
	}

	// 3. 知识质量验证
	if !ee.validateKnowledgeQuality(knowledge) {
		return fmt.Errorf("知识质量验证失败")
	}

	// 4. 检查重复性
	if isDuplicate, err := ee.checkKnowledgeDuplication(knowledge); err != nil {
		return fmt.Errorf("重复性检查失败: %v", err)
	} else if isDuplicate {
		return fmt.Errorf("知识已存在，跳过重复保存")
	}

	// 5. 保存到learned_knowledge表
	knowledgeID, err := ee.saveToLearnedKnowledge(knowledge)
	if err != nil {
		return fmt.Errorf("保存到learned_knowledge表失败: %v", err)
	}

	// 6. 生成并保存知识向量到knowledge_vectors表
	if err := ee.generateAndSaveKnowledgeVector(knowledgeID, knowledge); err != nil {
		logger.Warnf("生成知识向量失败 (ID=%d): %v", knowledgeID, err)
		// 向量生成失败不影响知识保存，只记录警告
	}

	logger.Infof("✅ 成功持久化知识到数据库 (ID=%d): %s", knowledgeID, knowledge.Question)
	return nil
}

// preprocessKnowledgeWithNLP 使用NLP对知识进行预处理
func (ee *EvolutionEngine) preprocessKnowledgeWithNLP(knowledge *LearnedKnowledge) error {
	// 1. 文本清理和标准化
	knowledge.Question = ee.cleanAndNormalizeText(knowledge.Question)
	knowledge.Answer = ee.cleanAndNormalizeText(knowledge.Answer)

	// 2. 使用NLP提取关键词
	if len(knowledge.Keywords) == 0 {
		knowledge.Keywords = ee.extractKeywordsWithNLP(knowledge.Question + " " + knowledge.Answer)
	}

	// 3. 自动分类
	if knowledge.Category == "" || knowledge.Category == "general" {
		knowledge.Category = ee.classifyKnowledgeWithNLP(knowledge.Question, knowledge.Answer)
	}

	// 4. 情感分析（确保内容积极正面）
	sentiment := ee.analyzeSentiment(knowledge.Answer)
	if sentiment.Label == "negative" && sentiment.Score > 0.8 {
		return fmt.Errorf("内容情感过于负面，不适合作为知识保存")
	}

	// 5. 意图识别
	intent := ee.classifyIntent(knowledge.Question)
	if knowledge.Metadata == nil {
		knowledge.Metadata = make(map[string]interface{})
	}
	knowledge.Metadata["nlp_intent"] = intent.Intent
	knowledge.Metadata["nlp_confidence"] = intent.Confidence

	return nil
}

// validateContentSafety 验证内容安全性（法律道德检查）
func (ee *EvolutionEngine) validateContentSafety(knowledge *LearnedKnowledge) error {
	// 1. 敏感词检查
	if ee.containsSensitiveContent(knowledge.Question) || ee.containsSensitiveContent(knowledge.Answer) {
		return fmt.Errorf("内容包含敏感词汇")
	}

	// 2. 违法内容检查
	if ee.containsIllegalContent(knowledge.Question) || ee.containsIllegalContent(knowledge.Answer) {
		return fmt.Errorf("内容可能涉及违法信息")
	}

	// 3. 不当内容检查
	if ee.containsInappropriateContent(knowledge.Question) || ee.containsInappropriateContent(knowledge.Answer) {
		return fmt.Errorf("内容不当，不符合道德标准")
	}

	// 4. 技术内容验证（确保是有价值的技术知识）
	if !ee.isTechnicallyValid(knowledge.Question, knowledge.Answer) {
		return fmt.Errorf("内容不是有效的技术知识")
	}

	return nil
}

// validateKnowledgeQuality 验证知识质量
func (ee *EvolutionEngine) validateKnowledgeQuality(knowledge *LearnedKnowledge) bool {
	// 1. 基本长度检查
	if len(strings.TrimSpace(knowledge.Question)) < 5 || len(strings.TrimSpace(knowledge.Answer)) < 10 {
		return false
	}

	// 2. 问题和答案不能相同
	if strings.TrimSpace(knowledge.Question) == strings.TrimSpace(knowledge.Answer) {
		return false
	}

	// 3. 置信度检查
	if knowledge.Confidence < 0.3 {
		return false
	}

	// 4. 内容完整性检查
	if !ee.isContentComplete(knowledge.Question, knowledge.Answer) {
		return false
	}

	// 5. 语言质量检查
	if !ee.isLanguageQualityGood(knowledge.Question, knowledge.Answer) {
		return false
	}

	return true
}

// checkKnowledgeDuplication 检查知识重复性
func (ee *EvolutionEngine) checkKnowledgeDuplication(knowledge *LearnedKnowledge) (bool, error) {
	// 使用相似度检查避免重复知识
	query := `
		SELECT id, question, answer
		FROM learned_knowledge
		WHERE status IN ('approved', 'pending')
		ORDER BY created_at DESC
		LIMIT 100
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return false, err
	}
	defer rows.Close()

	for rows.Next() {
		var id int
		var existingQuestion, existingAnswer string
		if err := rows.Scan(&id, &existingQuestion, &existingAnswer); err != nil {
			continue
		}

		// 计算问题相似度
		questionSimilarity := ee.calculateTextSimilarity(knowledge.Question, existingQuestion)
		answerSimilarity := ee.calculateTextSimilarity(knowledge.Answer, existingAnswer)

		// 如果问题和答案都高度相似，认为是重复
		if questionSimilarity > 0.85 && answerSimilarity > 0.85 {
			logger.Infof("发现重复知识 (ID=%d), 问题相似度=%.2f, 答案相似度=%.2f",
				id, questionSimilarity, answerSimilarity)
			return true, nil
		}
	}

	return false, nil
}

// saveToLearnedKnowledge 保存到learned_knowledge表
func (ee *EvolutionEngine) saveToLearnedKnowledge(knowledge *LearnedKnowledge) (int, error) {
	keywordsJSON, _ := json.Marshal(knowledge.Keywords)
	metadataJSON, _ := json.Marshal(knowledge.Metadata)

	query := `
		INSERT INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status, metadata, created_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, NOW())
	`

	result, err := ee.db.Exec(query,
		knowledge.Question, knowledge.Answer, knowledge.Source, knowledge.Confidence,
		knowledge.Category, keywordsJSON, knowledge.Context, knowledge.LearnedFrom,
		knowledge.Status, metadataJSON)

	if err != nil {
		return 0, err
	}

	id, err := result.LastInsertId()
	if err != nil {
		return 0, err
	}

	return int(id), nil
}

// generateAndSaveKnowledgeVector 生成并保存知识向量
func (ee *EvolutionEngine) generateAndSaveKnowledgeVector(knowledgeID int, knowledge *LearnedKnowledge) error {
	// 1. 生成向量
	vector, err := ee.generateKnowledgeVector(knowledge.Question, knowledge.Answer)
	if err != nil {
		return err
	}

	// 2. 保存到knowledge_vectors表
	return ee.storeKnowledgeVector(knowledgeID, vector)
}

// cleanAndNormalizeText 清理和标准化文本
func (ee *EvolutionEngine) cleanAndNormalizeText(text string) string {
	// 1. 去除多余空白字符
	text = strings.TrimSpace(text)
	text = regexp.MustCompile(`\s+`).ReplaceAllString(text, " ")

	// 2. 统一标点符号
	text = strings.ReplaceAll(text, "？", "?")
	text = strings.ReplaceAll(text, "！", "!")
	text = strings.ReplaceAll(text, "，", ",")
	text = strings.ReplaceAll(text, "。", ".")

	// 3. 去除特殊字符（保留基本标点）
	text = regexp.MustCompile(`[^\w\s\u4e00-\u9fff.,!?;:()\-\[\]{}'"/@#$%^&*+=<>|\\~`+"`"+`]`).ReplaceAllString(text, "")

	return text
}

// extractKeywordsWithNLP 使用NLP提取关键词
func (ee *EvolutionEngine) extractKeywordsWithNLP(text string) []string {
	// 这里应该调用NLP处理器来提取关键词
	// 暂时使用简单的关键词提取逻辑
	words := strings.Fields(strings.ToLower(text))
	keywordMap := make(map[string]int)

	// 过滤停用词并统计词频
	stopWords := map[string]bool{
		"的": true, "是": true, "在": true, "有": true, "和": true, "与": true,
		"或": true, "但": true, "如果": true, "因为": true, "所以": true,
		"the": true, "is": true, "are": true, "and": true, "or": true, "but": true,
		"if": true, "because": true, "so": true, "that": true, "this": true,
	}

	for _, word := range words {
		if len(word) > 1 && !stopWords[word] {
			keywordMap[word]++
		}
	}

	// 选择频率最高的关键词
	var keywords []string
	for word, freq := range keywordMap {
		if freq >= 1 && len(keywords) < 10 {
			keywords = append(keywords, word)
		}
	}

	return keywords
}

// classifyKnowledgeWithNLP 使用NLP对知识进行分类
func (ee *EvolutionEngine) classifyKnowledgeWithNLP(question, answer string) string {
	text := strings.ToLower(question + " " + answer)

	// 技术分类关键词
	categories := map[string][]string{
		"database":    {"数据库", "mysql", "sql", "mongodb", "redis", "database", "table", "query"},
		"programming": {"编程", "代码", "函数", "方法", "类", "对象", "programming", "code", "function", "method", "class"},
		"web":         {"网页", "网站", "前端", "后端", "html", "css", "javascript", "web", "frontend", "backend"},
		"system":      {"系统", "服务器", "网络", "配置", "部署", "system", "server", "network", "config", "deploy"},
		"algorithm":   {"算法", "数据结构", "排序", "搜索", "algorithm", "data structure", "sort", "search"},
		"security":    {"安全", "加密", "认证", "权限", "security", "encryption", "authentication", "authorization"},
		"api":         {"接口", "api", "rest", "http", "请求", "响应", "interface", "request", "response"},
	}

	for category, keywords := range categories {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				return category
			}
		}
	}

	return "general"
}

// analyzeSentiment 分析情感
func (ee *EvolutionEngine) analyzeSentiment(text string) struct {
	Label string
	Score float64
} {
	// 简单的情感分析逻辑
	negativeWords := []string{
		"错误", "失败", "问题", "bug", "崩溃", "异常", "不行", "不好", "糟糕",
		"error", "fail", "problem", "bug", "crash", "exception", "bad", "terrible",
	}

	positiveWords := []string{
		"成功", "正确", "好", "优秀", "完美", "解决", "修复",
		"success", "correct", "good", "excellent", "perfect", "solve", "fix",
	}

	textLower := strings.ToLower(text)
	negativeCount := 0
	positiveCount := 0

	for _, word := range negativeWords {
		if strings.Contains(textLower, word) {
			negativeCount++
		}
	}

	for _, word := range positiveWords {
		if strings.Contains(textLower, word) {
			positiveCount++
		}
	}

	if negativeCount > positiveCount {
		return struct {
			Label string
			Score float64
		}{"negative", float64(negativeCount) / float64(negativeCount+positiveCount+1)}
	} else if positiveCount > negativeCount {
		return struct {
			Label string
			Score float64
		}{"positive", float64(positiveCount) / float64(negativeCount+positiveCount+1)}
	}

	return struct {
		Label string
		Score float64
	}{"neutral", 0.5}
}

// classifyIntent 分类意图
func (ee *EvolutionEngine) classifyIntent(question string) struct {
	Intent     string
	Confidence float64
} {
	questionLower := strings.ToLower(question)

	// 简单的意图分类
	if strings.Contains(questionLower, "怎么") || strings.Contains(questionLower, "如何") || strings.Contains(questionLower, "how") {
		return struct {
			Intent     string
			Confidence float64
		}{"how_to", 0.8}
	}

	if strings.Contains(questionLower, "什么") || strings.Contains(questionLower, "what") {
		return struct {
			Intent     string
			Confidence float64
		}{"what_is", 0.8}
	}

	if strings.Contains(questionLower, "为什么") || strings.Contains(questionLower, "why") {
		return struct {
			Intent     string
			Confidence float64
		}{"why", 0.8}
	}

	if strings.Contains(questionLower, "哪里") || strings.Contains(questionLower, "where") {
		return struct {
			Intent     string
			Confidence float64
		}{"where", 0.8}
	}

	return struct {
		Intent     string
		Confidence float64
	}{"general", 0.5}
}

// containsSensitiveContent 检查敏感内容
func (ee *EvolutionEngine) containsSensitiveContent(text string) bool {
	sensitiveWords := []string{
		// 政治敏感词
		"政治", "政府", "领导人", "党", "选举", "革命", "抗议", "示威",
		// 暴力相关
		"暴力", "杀害", "伤害", "攻击", "武器", "爆炸", "恐怖",
		// 色情相关
		"色情", "性", "裸体", "成人", "黄色",
		// 赌博相关
		"赌博", "博彩", "彩票", "赌场", "下注",
		// 毒品相关
		"毒品", "吸毒", "贩毒", "大麻", "海洛因",
	}

	textLower := strings.ToLower(text)
	for _, word := range sensitiveWords {
		if strings.Contains(textLower, word) {
			return true
		}
	}
	return false
}

// containsIllegalContent 检查违法内容
func (ee *EvolutionEngine) containsIllegalContent(text string) bool {
	illegalKeywords := []string{
		"违法", "犯罪", "诈骗", "欺诈", "洗钱", "贿赂", "腐败",
		"盗版", "侵权", "抄袭", "假冒", "伪造", "走私",
		"illegal", "crime", "fraud", "scam", "bribe", "corruption",
	}

	textLower := strings.ToLower(text)
	for _, keyword := range illegalKeywords {
		if strings.Contains(textLower, keyword) {
			return true
		}
	}
	return false
}

// containsInappropriateContent 检查不当内容
func (ee *EvolutionEngine) containsInappropriateContent(text string) bool {
	inappropriateWords := []string{
		"仇恨", "歧视", "种族主义", "性别歧视", "宗教歧视",
		"侮辱", "谩骂", "诽谤", "中伤", "恶意攻击",
		"hate", "discrimination", "racism", "sexism", "insult",
	}

	textLower := strings.ToLower(text)
	for _, word := range inappropriateWords {
		if strings.Contains(textLower, word) {
			return true
		}
	}
	return false
}

// isTechnicallyValid 验证是否是有效的技术内容
func (ee *EvolutionEngine) isTechnicallyValid(question, answer string) bool {
	// 检查是否包含技术相关关键词
	technicalKeywords := []string{
		"编程", "代码", "算法", "数据库", "网络", "系统", "软件", "硬件",
		"开发", "测试", "部署", "配置", "API", "接口", "框架", "库",
		"programming", "code", "algorithm", "database", "network", "system",
		"software", "hardware", "development", "testing", "deployment", "config",
		"api", "interface", "framework", "library", "server", "client",
	}

	text := strings.ToLower(question + " " + answer)
	for _, keyword := range technicalKeywords {
		if strings.Contains(text, keyword) {
			return true
		}
	}

	// 如果不包含技术关键词，但内容有价值也可以接受
	return len(strings.TrimSpace(answer)) > 20
}

// isContentComplete 检查内容完整性
func (ee *EvolutionEngine) isContentComplete(question, answer string) bool {
	// 检查问题是否是完整的问句
	questionTrimmed := strings.TrimSpace(question)
	if !strings.HasSuffix(questionTrimmed, "?") && !strings.HasSuffix(questionTrimmed, "？") &&
		!strings.Contains(questionTrimmed, "什么") && !strings.Contains(questionTrimmed, "如何") &&
		!strings.Contains(questionTrimmed, "怎么") && !strings.Contains(questionTrimmed, "为什么") {
		// 如果不是明显的问句格式，检查是否至少有疑问词
		questionWords := []string{"what", "how", "why", "where", "when", "which", "who"}
		hasQuestionWord := false
		questionLower := strings.ToLower(questionTrimmed)
		for _, word := range questionWords {
			if strings.Contains(questionLower, word) {
				hasQuestionWord = true
				break
			}
		}
		if !hasQuestionWord && len(questionTrimmed) < 10 {
			return false
		}
	}

	// 检查答案是否提供了有用信息
	answerTrimmed := strings.TrimSpace(answer)
	if len(answerTrimmed) < 10 {
		return false
	}

	// 检查答案是否只是重复问题
	if strings.Contains(strings.ToLower(answerTrimmed), strings.ToLower(questionTrimmed)) &&
		len(answerTrimmed) < len(questionTrimmed)*2 {
		return false
	}

	return true
}

// isLanguageQualityGood 检查语言质量
func (ee *EvolutionEngine) isLanguageQualityGood(question, answer string) bool {
	// 检查是否有过多的重复字符（使用简单的字符串检查代替正则表达式）
	if ee.hasExcessiveRepeatedChars(question) || ee.hasExcessiveRepeatedChars(answer) {
		return false
	}

	// 检查是否有过多的特殊字符
	specialCharCount := len(regexp.MustCompile(`[^a-zA-Z0-9\u4e00-\u9fff\s.,!?;:()\-\[\]{}'"/@#$%^&*+=<>|\\~`+"`"+`]`).FindAllString(question+answer, -1))
	totalCharCount := len(question + answer)
	if totalCharCount > 0 && float64(specialCharCount)/float64(totalCharCount) > 0.3 {
		return false
	}

	// 检查是否有基本的语法结构
	if !regexp.MustCompile(`[a-zA-Z\u4e00-\u9fff]`).MatchString(question) || !regexp.MustCompile(`[a-zA-Z\u4e00-\u9fff]`).MatchString(answer) {
		return false
	}

	return true
}

// calculateTextSimilarity 计算文本相似度
func (ee *EvolutionEngine) calculateTextSimilarity(text1, text2 string) float64 {
	// 简单的基于词汇重叠的相似度计算
	words1 := strings.Fields(strings.ToLower(text1))
	words2 := strings.Fields(strings.ToLower(text2))

	if len(words1) == 0 || len(words2) == 0 {
		return 0.0
	}

	// 计算交集
	wordSet1 := make(map[string]bool)
	for _, word := range words1 {
		wordSet1[word] = true
	}

	intersection := 0
	for _, word := range words2 {
		if wordSet1[word] {
			intersection++
		}
	}

	// 使用Jaccard相似度
	union := len(words1) + len(words2) - intersection
	if union == 0 {
		return 0.0
	}

	return float64(intersection) / float64(union)
}

// categorizeQuery 对查询进行分类
func (ee *EvolutionEngine) categorizeQuery(intent string) string {
	intentCategoryMap := map[string]string{
		"technical":    "technical",
		"programming":  "programming",
		"database":     "database",
		"web":          "web",
		"system":       "system",
		"algorithm":    "algorithm",
		"security":     "security",
		"api":          "api",
		"how_to":       "tutorial",
		"what_is":      "definition",
		"why":          "explanation",
		"troubleshoot": "troubleshooting",
	}

	if category, exists := intentCategoryMap[intent]; exists {
		return category
	}
	return "general"
}

// extractKeywords 提取关键词（简化版本）
func (ee *EvolutionEngine) extractKeywords(text string) []string {
	return ee.extractKeywordsWithNLP(text)
}

// parseKeywords 解析关键词字符串为数组
func (ee *EvolutionEngine) parseKeywords(keywordsStr string) []string {
	if keywordsStr == "" {
		return []string{}
	}

	// 尝试解析JSON格式的关键词
	var keywords []string
	if err := json.Unmarshal([]byte(keywordsStr), &keywords); err == nil {
		return keywords
	}

	// 如果不是JSON格式，按逗号分割
	keywords = strings.Split(keywordsStr, ",")
	for i, keyword := range keywords {
		keywords[i] = strings.TrimSpace(keyword)
	}

	// 过滤空关键词
	var filteredKeywords []string
	for _, keyword := range keywords {
		if keyword != "" {
			filteredKeywords = append(filteredKeywords, keyword)
		}
	}

	return filteredKeywords
}

// hasExcessiveRepeatedChars 检查是否有过多重复字符
func (ee *EvolutionEngine) hasExcessiveRepeatedChars(text string) bool {
	if len(text) < 5 {
		return false
	}

	runes := []rune(text)
	for i := 0; i < len(runes)-4; i++ {
		char := runes[i]
		count := 1
		for j := i + 1; j < len(runes) && j < i+10; j++ {
			if runes[j] == char {
				count++
				if count >= 5 {
					return true
				}
			} else {
				break
			}
		}
	}
	return false
}

// generateKnowledgeVector 生成知识向量
func (ee *EvolutionEngine) generateKnowledgeVector(question, answer string) ([]float32, error) {
	// 简单的向量生成逻辑 - 在实际应用中应该使用更复杂的嵌入模型
	text := question + " " + answer
	words := strings.Fields(strings.ToLower(text))

	// 创建一个固定维度的向量（例如128维）
	vectorSize := 128
	vector := make([]float32, vectorSize)

	// 使用简单的哈希方法生成向量
	for i, word := range words {
		if i >= vectorSize {
			break
		}
		// 简单的字符串哈希转换为浮点数
		hash := 0
		for _, char := range word {
			hash = hash*31 + int(char)
		}
		vector[i%vectorSize] += float32(hash%1000) / 1000.0
	}

	// 归一化向量
	var norm float32
	for _, val := range vector {
		norm += val * val
	}
	norm = float32(math.Sqrt(float64(norm)))

	if norm > 0 {
		for i := range vector {
			vector[i] /= norm
		}
	}

	return vector, nil
}

// storeKnowledgeVector 存储知识向量
func (ee *EvolutionEngine) storeKnowledgeVector(knowledgeID int, vector []float32) error {
	vectorJSON, err := json.Marshal(vector)
	if err != nil {
		return fmt.Errorf("序列化向量失败: %v", err)
	}

	query := `
		INSERT INTO knowledge_vectors (knowledge_id, vector_data, created_at)
		VALUES (?, ?, NOW())
		ON DUPLICATE KEY UPDATE
		vector_data = VALUES(vector_data),
		updated_at = NOW()
	`

	_, err = ee.db.Exec(query, knowledgeID, string(vectorJSON))
	if err != nil {
		return fmt.Errorf("存储向量到数据库失败: %v", err)
	}

	return nil
}

// updateExistingVectors 更新现有向量
func (ee *EvolutionEngine) updateExistingVectors() error {
	// 查找需要更新向量的知识（例如最近修改的）
	query := `
		SELECT lk.id, lk.question, lk.answer
		FROM learned_knowledge lk
		JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.updated_at > kv.updated_at
		  AND lk.status = 'approved'
		LIMIT 50
	`

	rows, err := ee.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	updateCount := 0
	for rows.Next() {
		var id int
		var question, answer string
		if err := rows.Scan(&id, &question, &answer); err != nil {
			continue
		}

		// 重新生成向量
		vector, err := ee.generateKnowledgeVector(question, answer)
		if err != nil {
			logger.Warnf("重新生成向量失败 (ID=%d): %v", id, err)
			continue
		}

		// 更新向量
		if err := ee.storeKnowledgeVector(id, vector); err != nil {
			logger.Warnf("更新向量失败 (ID=%d): %v", id, err)
			continue
		}

		updateCount++
	}

	if updateCount > 0 {
		logger.Infof("✅ 成功更新了 %d 个知识向量", updateCount)
	}

	return nil
}
