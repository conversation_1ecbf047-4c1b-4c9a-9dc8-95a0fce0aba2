#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试修复后的进化改进功能
"""

import requests
import json
import time
import mysql.connector
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/api/v1/ask"
EVOLUTION_ENDPOINT = f"{SERVER_URL}/api/learning/evolution/apply"

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root123',
    'database': 'faq_system',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return mysql.connector.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def create_test_data():
    """创建一些测试数据"""
    print("📝 创建测试数据...")
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        # 创建一些用户查询和系统响应
        test_data = [
            {
                "query": "什么是Python？",
                "response": "Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。它广泛用于Web开发、数据科学、人工智能等领域。",
                "confidence": 0.9,
                "intent": "technical"
            },
            {
                "query": "如何学习机器学习？",
                "response": "学习机器学习需要掌握数学基础（线性代数、统计学）、编程技能（Python/R）、理论知识和实践项目。建议从基础算法开始，逐步深入。",
                "confidence": 0.85,
                "intent": "advice"
            },
            {
                "query": "数据库优化有哪些方法？",
                "response": "数据库优化包括：1)索引优化 2)查询优化 3)表结构设计 4)硬件配置 5)缓存策略等多个方面。",
                "confidence": 0.88,
                "intent": "technical"
            }
        ]
        
        for data in test_data:
            # 插入用户查询
            query_sql = """
            INSERT INTO user_queries (query_text, user_id, query_intent, created_at)
            VALUES (%s, %s, %s, NOW())
            """
            cursor.execute(query_sql, (data["query"], "test_user", data["intent"]))
            query_id = cursor.lastrowid
            
            # 插入系统响应
            response_sql = """
            INSERT INTO system_responses (query_id, response_text, confidence_score, created_at)
            VALUES (%s, %s, %s, NOW())
            """
            cursor.execute(response_sql, (query_id, data["response"], data["confidence"]))
        
        conn.commit()
        print(f"✅ 成功创建了 {len(test_data)} 条测试数据")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def check_existing_data():
    """检查现有数据"""
    print("\n📊 检查现有数据...")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 检查FAQ数据
        cursor.execute("SELECT COUNT(*) FROM faq WHERE status = 'active'")
        faq_count = cursor.fetchone()[0]
        
        # 检查用户查询数据
        cursor.execute("SELECT COUNT(*) FROM user_queries WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)")
        query_count = cursor.fetchone()[0]
        
        # 检查系统响应数据
        cursor.execute("SELECT COUNT(*) FROM system_responses WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 DAY)")
        response_count = cursor.fetchone()[0]
        
        # 检查是否存在user_feedback表
        cursor.execute("""
            SELECT COUNT(*) 
            FROM INFORMATION_SCHEMA.TABLES 
            WHERE TABLE_SCHEMA = 'faq_system' 
            AND TABLE_NAME = 'user_feedback'
        """)
        feedback_table_exists = cursor.fetchone()[0] > 0
        
        print(f"📚 FAQ数据: {faq_count} 条活跃记录")
        print(f"❓ 最近1天用户查询: {query_count} 条")
        print(f"💬 最近1天系统响应: {response_count} 条")
        print(f"📋 user_feedback表存在: {'是' if feedback_table_exists else '否'}")
        
        if feedback_table_exists:
            cursor.execute("SELECT COUNT(*) FROM user_feedback")
            feedback_count = cursor.fetchone()[0]
            print(f"💭 用户反馈数据: {feedback_count} 条")
        
    except Exception as e:
        print(f"❌ 检查数据失败: {e}")
    finally:
        conn.close()

def trigger_evolution():
    """触发进化改进"""
    print("\n🚀 触发进化改进...")
    
    try:
        response = requests.post(EVOLUTION_ENDPOINT,
                               headers={"Content-Type": "application/json"},
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 进化改进已触发")
            print(f"📝 消息: {data.get('message', '')}")
            return True
        else:
            print(f"❌ 触发进化改进失败: {response.status_code}")
            try:
                error_data = response.json()
                print(f"错误信息: {error_data}")
            except:
                print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_qa_after_learning():
    """测试学习后的问答效果"""
    print("\n🧪 测试学习后的问答效果...")
    
    test_questions = [
        "Python编程语言有什么特点？",
        "机器学习的学习路径是什么？",
        "怎样优化数据库性能？",
        "什么是人工智能？",
        "如何进行Web开发？"
    ]
    
    for i, question in enumerate(test_questions, 1):
        print(f"\n🔍 测试问题 {i}: {question}")
        
        try:
            response = requests.post(ASK_ENDPOINT,
                                   json={"question": question},
                                   headers={"Content-Type": "application/json"},
                                   timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get("answer", "")
                confidence = data.get("confidence", 0)
                intent = data.get("intent", "")
                
                print(f"✅ 回答: {answer[:150]}..." if len(answer) > 150 else f"✅ 回答: {answer}")
                print(f"📊 置信度: {confidence:.2f} | 意图: {intent}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)

def main():
    """主测试函数"""
    print("🔧 测试修复后的进化改进功能")
    print("=" * 50)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 检查现有数据
    check_existing_data()
    
    # 2. 创建测试数据（如果需要）
    create_test_data()
    
    # 3. 触发进化改进
    if trigger_evolution():
        print("\n⏳ 等待进化改进处理...")
        time.sleep(8)  # 给系统时间处理
        
        # 4. 测试学习后的效果
        test_qa_after_learning()
    else:
        print("❌ 进化改进触发失败，跳过后续测试")
    
    print(f"\n⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 修复后的进化改进功能测试完成！")
    
    # 总结修复内容
    print("\n📋 修复内容总结:")
    print("✅ 不再依赖可能不存在的user_feedback表")
    print("✅ 从现有FAQ数据中学习知识")
    print("✅ 从高置信度问答对中学习")
    print("✅ 增加了数据存在性检查")
    print("✅ 改进了错误处理和日志记录")
    print("✅ 使用response_text而不是response_content字段")

if __name__ == "__main__":
    main()
