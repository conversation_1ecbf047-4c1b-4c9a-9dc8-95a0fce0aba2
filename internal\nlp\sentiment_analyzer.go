package nlp

import (
	"log"
	"math"
	"strings"
)

// SentimentAnalyzer 情感分析器
type SentimentAnalyzer struct {
	positiveWords map[string]float64
	negativeWords map[string]float64
	intensifiers  map[string]float64
	negators      []string
}

// SentimentResult 情感分析结果
type SentimentResult struct {
	Label      string             `json:"label"`      // positive, negative, neutral
	Score      float64            `json:"score"`      // -1.0 to 1.0
	Confidence float64            `json:"confidence"` // 0.0 to 1.0
	Emotions   map[string]float64 `json:"emotions"`   // 具体情感
	Intensity  float64            `json:"intensity"`  // 情感强度
	Metadata   map[string]interface{} `json:"metadata"`
}

// NewSentimentAnalyzer 创建情感分析器
func NewSentimentAnalyzer() *SentimentAnalyzer {
	log.Printf("😊 初始化情感分析器...")
	
	sa := &SentimentAnalyzer{
		positiveWords: make(map[string]float64),
		negativeWords: make(map[string]float64),
		intensifiers:  make(map[string]float64),
		negators:      []string{},
	}
	
	sa.initializeWordLists()
	
	log.Printf("✅ 情感分析器初始化完成")
	return sa
}

// initializeWordLists 初始化词汇列表
func (sa *SentimentAnalyzer) initializeWordLists() {
	// 积极词汇
	positiveWords := map[string]float64{
		"好":     0.8, "很好":   0.9, "非常好": 1.0, "棒":     0.8, "优秀":   0.9,
		"完美":   1.0, "满意":   0.8, "喜欢":   0.7, "爱":     0.9, "开心":   0.8,
		"高兴":   0.8, "快乐":   0.9, "兴奋":   0.8, "激动":   0.8, "惊喜":   0.7,
		"赞":     0.7, "支持":   0.6, "同意":   0.6, "正确":   0.7, "对":     0.6,
		"成功":   0.8, "胜利":   0.9, "赢":     0.8, "优势":   0.7, "强":     0.6,
		"美":     0.7, "漂亮":   0.8, "帅":     0.7, "可爱":   0.8, "温暖":   0.7,
		"舒服":   0.7, "轻松":   0.6, "简单":   0.5, "容易":   0.5, "方便":   0.6,
		"快":     0.5, "高效":   0.7, "稳定":   0.6, "可靠":   0.7, "安全":   0.6,
		"excellent": 0.9, "good": 0.7, "great": 0.8, "awesome": 0.9, "amazing": 0.9,
		"perfect": 1.0, "wonderful": 0.9, "fantastic": 0.9, "brilliant": 0.8,
	}
	
	// 消极词汇
	negativeWords := map[string]float64{
		"坏":     -0.8, "很坏":   -0.9, "非常坏": -1.0, "差":     -0.8, "糟糕":   -0.9,
		"失败":   -0.8, "错误":   -0.7, "问题":   -0.6, "故障":   -0.7, "异常":   -0.6,
		"不好":   -0.7, "不行":   -0.7, "不对":   -0.6, "错":     -0.6, "假":     -0.6,
		"难":     -0.5, "困难":   -0.6, "复杂":   -0.5, "麻烦":   -0.6, "烦":     -0.6,
		"慢":     -0.5, "卡":     -0.6, "崩溃":   -0.8, "死机":   -0.8, "无法":   -0.6,
		"不能":   -0.6, "无效":   -0.6, "失效":   -0.7, "损坏":   -0.8, "破坏":   -0.8,
		"讨厌":   -0.8, "恨":     -0.9, "愤怒":   -0.8, "生气":   -0.7, "不满":   -0.7,
		"失望":   -0.7, "沮丧":   -0.7, "难过":   -0.7, "伤心":   -0.8, "痛苦":   -0.8,
		"bad": -0.7, "terrible": -0.9, "awful": -0.8, "horrible": -0.9, "worst": -1.0,
		"fail": -0.7, "error": -0.6, "problem": -0.6, "issue": -0.5, "bug": -0.6,
	}
	
	// 强化词
	intensifiers := map[string]float64{
		"非常": 1.5, "很": 1.3, "特别": 1.4, "极其": 1.6, "超级": 1.5,
		"相当": 1.2, "比较": 1.1, "有点": 0.8, "稍微": 0.7, "略": 0.6,
		"really": 1.4, "very": 1.3, "extremely": 1.6, "quite": 1.2, "rather": 1.1,
		"somewhat": 0.8, "slightly": 0.7, "a bit": 0.8, "pretty": 1.2,
	}
	
	// 否定词
	negators := []string{
		"不", "没", "无", "非", "未", "否", "别", "勿",
		"not", "no", "never", "neither", "nor", "none", "nothing",
	}
	
	sa.positiveWords = positiveWords
	sa.negativeWords = negativeWords
	sa.intensifiers = intensifiers
	sa.negators = negators
}

// AnalyzeSentiment 分析情感
func (sa *SentimentAnalyzer) AnalyzeSentiment(text string) SentimentResult {
	if strings.TrimSpace(text) == "" {
		return SentimentResult{
			Label:      "neutral",
			Score:      0.0,
			Confidence: 0.0,
			Emotions:   make(map[string]float64),
			Intensity:  0.0,
			Metadata:   make(map[string]interface{}),
		}
	}
	
	text = strings.ToLower(text)
	words := strings.Fields(text)
	
	var totalScore float64
	var wordCount int
	var positiveCount int
	var negativeCount int
	
	emotions := map[string]float64{
		"joy":     0.0,
		"anger":   0.0,
		"sadness": 0.0,
		"fear":    0.0,
		"surprise": 0.0,
		"disgust": 0.0,
	}
	
	// 分析每个词
	for i, word := range words {
		var wordScore float64
		var isNegated bool
		var intensifier float64 = 1.0
		
		// 检查否定词
		if i > 0 {
			for _, negator := range sa.negators {
				if words[i-1] == negator {
					isNegated = true
					break
				}
			}
		}
		
		// 检查强化词
		if i > 0 {
			if intensity, exists := sa.intensifiers[words[i-1]]; exists {
				intensifier = intensity
			}
		}
		
		// 计算词汇得分
		if score, exists := sa.positiveWords[word]; exists {
			wordScore = score
			positiveCount++
			emotions["joy"] += score * 0.8
			emotions["surprise"] += score * 0.2
		} else if score, exists := sa.negativeWords[word]; exists {
			wordScore = score
			negativeCount++
			emotions["anger"] += math.Abs(score) * 0.4
			emotions["sadness"] += math.Abs(score) * 0.4
			emotions["disgust"] += math.Abs(score) * 0.2
		}
		
		// 应用修饰符
		if isNegated {
			wordScore = -wordScore
		}
		wordScore *= intensifier
		
		totalScore += wordScore
		if wordScore != 0 {
			wordCount++
		}
	}
	
	// 计算平均分数
	var avgScore float64
	if wordCount > 0 {
		avgScore = totalScore / float64(wordCount)
	}
	
	// 标准化分数到 [-1, 1]
	normalizedScore := math.Max(-1.0, math.Min(1.0, avgScore))
	
	// 确定标签
	var label string
	if normalizedScore > 0.1 {
		label = "positive"
	} else if normalizedScore < -0.1 {
		label = "negative"
	} else {
		label = "neutral"
	}
	
	// 计算置信度
	confidence := sa.calculateConfidence(normalizedScore, wordCount, len(words))
	
	// 计算情感强度
	intensity := math.Abs(normalizedScore)
	
	// 标准化情感分数
	maxEmotion := 0.0
	for emotion, score := range emotions {
		emotions[emotion] = math.Min(1.0, score)
		if emotions[emotion] > maxEmotion {
			maxEmotion = emotions[emotion]
		}
	}
	
	return SentimentResult{
		Label:      label,
		Score:      normalizedScore,
		Confidence: confidence,
		Emotions:   emotions,
		Intensity:  intensity,
		Metadata: map[string]interface{}{
			"word_count":      len(words),
			"sentiment_words": wordCount,
			"positive_count":  positiveCount,
			"negative_count":  negativeCount,
			"total_score":     totalScore,
		},
	}
}

// calculateConfidence 计算置信度
func (sa *SentimentAnalyzer) calculateConfidence(score float64, sentimentWords, totalWords int) float64 {
	// 基础置信度基于情感词汇的比例
	wordRatio := float64(sentimentWords) / float64(totalWords)
	baseConfidence := math.Min(wordRatio*2, 1.0)
	
	// 分数强度影响置信度
	intensityBonus := math.Abs(score) * 0.5
	
	// 情感词汇数量影响置信度
	var countBonus float64
	if sentimentWords >= 3 {
		countBonus = 0.2
	} else if sentimentWords >= 1 {
		countBonus = 0.1
	}
	
	confidence := baseConfidence + intensityBonus + countBonus
	return math.Min(confidence, 1.0)
}

// GetEmotionSummary 获取情感摘要
func (sa *SentimentAnalyzer) GetEmotionSummary(result SentimentResult) string {
	if result.Label == "neutral" {
		return "情感中性"
	}
	
	// 找到主要情感
	maxEmotion := ""
	maxScore := 0.0
	for emotion, score := range result.Emotions {
		if score > maxScore {
			maxScore = score
			maxEmotion = emotion
		}
	}
	
	emotionNames := map[string]string{
		"joy":      "喜悦",
		"anger":    "愤怒",
		"sadness":  "悲伤",
		"fear":     "恐惧",
		"surprise": "惊讶",
		"disgust":  "厌恶",
	}
	
	if name, exists := emotionNames[maxEmotion]; exists && maxScore > 0.3 {
		return name
	}
	
	if result.Label == "positive" {
		return "积极"
	} else {
		return "消极"
	}
}

// AnalyzeBatch 批量分析情感
func (sa *SentimentAnalyzer) AnalyzeBatch(texts []string) []SentimentResult {
	results := make([]SentimentResult, len(texts))
	for i, text := range texts {
		results[i] = sa.AnalyzeSentiment(text)
	}
	return results
}

// GetSentimentTrend 获取情感趋势
func (sa *SentimentAnalyzer) GetSentimentTrend(results []SentimentResult) map[string]interface{} {
	if len(results) == 0 {
		return map[string]interface{}{
			"trend":            "stable",
			"average_score":    0.0,
			"positive_ratio":   0.0,
			"negative_ratio":   0.0,
			"neutral_ratio":    0.0,
		}
	}
	
	var totalScore float64
	var positiveCount, negativeCount, neutralCount int
	
	for _, result := range results {
		totalScore += result.Score
		switch result.Label {
		case "positive":
			positiveCount++
		case "negative":
			negativeCount++
		case "neutral":
			neutralCount++
		}
	}
	
	avgScore := totalScore / float64(len(results))
	total := float64(len(results))
	
	// 确定趋势
	var trend string
	if avgScore > 0.2 {
		trend = "positive"
	} else if avgScore < -0.2 {
		trend = "negative"
	} else {
		trend = "stable"
	}
	
	return map[string]interface{}{
		"trend":          trend,
		"average_score":  avgScore,
		"positive_ratio": float64(positiveCount) / total,
		"negative_ratio": float64(negativeCount) / total,
		"neutral_ratio":  float64(neutralCount) / total,
		"total_samples":  len(results),
	}
}
