package recommendation

import (
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"fmt"
	"strings"
)

// Service 推荐服务
type Service struct {
	config           *RecommendationConfig
	knowledgeLearner *learning.KnowledgeLearner
	strategies       []RecommendationStrategy
}

// NewService 创建推荐服务
func NewService(knowledgeLearner *learning.KnowledgeLearner, config *RecommendationConfig) *Service {
	if config == nil {
		config = DefaultRecommendationConfig()
	}

	service := &Service{
		config:           config,
		knowledgeLearner: knowledgeLearner,
		strategies:       make([]RecommendationStrategy, 0),
	}

	// 注册推荐策略
	service.registerStrategies()

	return service
}

// registerStrategies 注册推荐策略
func (s *Service) registerStrategies() {
	// 基于知识点的推荐策略
	if s.config.EnableTopicBased {
		s.strategies = append(s.strategies, NewTopicBasedStrategy(s.knowledgeLearner, s.config))
	}

	// 基于语义的推荐策略
	if s.config.EnableSemanticBased {
		s.strategies = append(s.strategies, NewSemanticBasedStrategy(s.knowledgeLearner, s.config))
	}

	// 基于用户的推荐策略（暂时禁用）
	if s.config.EnableUserBased {
		// s.strategies = append(s.strategies, NewUserBasedStrategy(s.knowledgeLearner, s.config))
	}

	logger.Infof("📋 推荐服务已注册 %d 个推荐策略", len(s.strategies))
}

// GetRecommendations 获取推荐
func (s *Service) GetRecommendations(ctx *RecommendationContext) *RecommendationResult {
	logger.Infof("🔍 开始获取推荐: 问题='%s', 知识点='%s'", ctx.Question, ctx.KnowledgeTopic)

	// 尝试各种推荐策略
	for _, strategy := range s.strategies {
		if !strategy.IsApplicable(ctx) {
			logger.Infof("⏭️ 策略 '%s' 不适用，跳过", strategy.GetStrategyName())
			continue
		}

		logger.Infof("🎯 尝试策略: %s", strategy.GetStrategyName())
		result, err := strategy.GetRecommendations(ctx)
		if err != nil {
			logger.Warnf("❌ 策略 '%s' 执行失败: %v", strategy.GetStrategyName(), err)
			continue
		}

		if result != nil && result.Success && len(result.Items) > 0 {
			logger.Infof("✅ 策略 '%s' 成功返回 %d 个推荐", strategy.GetStrategyName(), len(result.Items))
			return result
		}
	}

	// 如果所有策略都失败，返回空结果但包含知识点标签
	if ctx.KnowledgeTopic != "" {
		return &RecommendationResult{
			Success:            true,
			RecommendationType: "topic_label_only",
			KnowledgeTopic:     ctx.KnowledgeTopic,
			Items:              []*RecommendationItem{},
			FormattedText:      fmt.Sprintf("🏷️ **知识点标签**: %s", ctx.KnowledgeTopic),
			Metadata: map[string]interface{}{
				"fallback": true,
				"reason":   "no_related_knowledge_found",
			},
		}
	}

	logger.Infof("❌ 未找到任何推荐")
	return &RecommendationResult{
		Success:            false,
		RecommendationType: "none",
		Items:              []*RecommendationItem{},
		FormattedText:      "",
		Metadata: map[string]interface{}{
			"reason": "no_applicable_strategy",
		},
	}
}

// GetRecommendationsByKnowledge 根据知识对象获取推荐
func (s *Service) GetRecommendationsByKnowledge(knowledge *learning.LearnedKnowledge, excludeID int) *RecommendationResult {
	ctx := &RecommendationContext{
		Question:         knowledge.Question,
		Answer:           knowledge.Answer,
		KnowledgeID:      excludeID,
		KnowledgeTopic:   knowledge.KnowledgeTopic,
		CurrentKnowledge: knowledge,
		Confidence:       float64(knowledge.Confidence),
		Source:           knowledge.Source,
	}

	return s.GetRecommendations(ctx)
}

// GetRecommendationsByTopic 根据知识点获取推荐
func (s *Service) GetRecommendationsByTopic(topic string, excludeID int, limit int) *RecommendationResult {
	ctx := &RecommendationContext{
		KnowledgeTopic: topic,
		KnowledgeID:    excludeID,
	}

	// 临时设置限制
	originalLimit := s.config.MaxRecommendations
	if limit > 0 && limit < s.config.MaxRecommendations {
		s.config.MaxRecommendations = limit
	}

	result := s.GetRecommendations(ctx)

	// 恢复原始限制
	s.config.MaxRecommendations = originalLimit

	return result
}

// FormatRecommendations 格式化推荐结果
func (s *Service) FormatRecommendations(result *RecommendationResult) string {
	if result == nil || !result.Success {
		return ""
	}

	// 如果已经有格式化文本，直接返回
	if result.FormattedText != "" {
		return result.FormattedText
	}

	// 如果没有推荐项目，返回空
	if len(result.Items) == 0 {
		return ""
	}

	// 格式化推荐项目
	var recommendations []string
	for i, item := range result.Items {
		if i >= s.config.MaxRecommendations {
			break
		}
		// 添加知识ID信息，用于前端交互
		recommendations = append(recommendations, fmt.Sprintf("• [📖 %s](#knowledge-%d)", item.Question, item.ID))
	}

	if len(recommendations) == 0 {
		return ""
	}

	return fmt.Sprintf("🔗 **您可能还想了解 (%s)：**\n%s",
		result.KnowledgeTopic, strings.Join(recommendations, "\n"))
}

// UpdateConfig 更新配置
func (s *Service) UpdateConfig(config *RecommendationConfig) {
	s.config = config
	// 重新注册策略
	s.strategies = make([]RecommendationStrategy, 0)
	s.registerStrategies()
}

// GetConfig 获取当前配置
func (s *Service) GetConfig() *RecommendationConfig {
	return s.config
}

// GetStrategies 获取已注册的策略
func (s *Service) GetStrategies() []string {
	strategies := make([]string, len(s.strategies))
	for i, strategy := range s.strategies {
		strategies[i] = strategy.GetStrategyName()
	}
	return strategies
}
