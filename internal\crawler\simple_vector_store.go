package crawler

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math"
)

// SimpleVectorStore 简单向量存储实现
type SimpleVectorStore struct {
	db *sql.DB
}

// NewSimpleVectorStore 创建简单向量存储
func NewSimpleVectorStore(db *sql.DB) *SimpleVectorStore {
	return &SimpleVectorStore{
		db: db,
	}
}

// UpsertVector 插入或更新向量
func (svs *SimpleVectorStore) UpsertVector(id int, content string, embedding []float32) error {
	if svs.db == nil {
		return fmt.Errorf("数据库连接不可用")
	}

	// 序列化向量数据
	vectorJSON, err := json.Marshal(embedding)
	if err != nil {
		return fmt.Errorf("向量序列化失败: %v", err)
	}

	// 插入或更新向量数据
	query := `
		INSERT INTO knowledge_vectors (knowledge_id, vector_data, vector_type)
		VALUES (?, ?, 'combined')
		ON DUPLICATE KEY UPDATE
		vector_data = VALUES(vector_data)
	`

	_, err = svs.db.Exec(query, id, vectorJSON)
	if err != nil {
		return fmt.Errorf("向量存储失败: %v", err)
	}

	log.Printf("✅ 向量存储成功: ID=%d, 维度=%d", id, len(embedding))
	return nil
}

// GetVector 获取向量
func (svs *SimpleVectorStore) GetVector(id int) ([]float32, error) {
	if svs.db == nil {
		return nil, fmt.Errorf("数据库连接不可用")
	}

	query := `
		SELECT vector_data
		FROM knowledge_vectors
		WHERE knowledge_id = ? AND vector_type = 'combined'
	`

	var vectorJSON string
	err := svs.db.QueryRow(query, id).Scan(&vectorJSON)
	if err != nil {
		if err == sql.ErrNoRows {
			return nil, fmt.Errorf("向量不存在: ID=%d", id)
		}
		return nil, fmt.Errorf("查询向量失败: %v", err)
	}

	// 反序列化向量数据
	var vector []float32
	err = json.Unmarshal([]byte(vectorJSON), &vector)
	if err != nil {
		return nil, fmt.Errorf("向量反序列化失败: %v", err)
	}

	return vector, nil
}

// SearchSimilar 搜索相似向量（简单实现）
func (svs *SimpleVectorStore) SearchSimilar(queryVector []float32, limit int) ([]VectorSearchResult, error) {
	if svs.db == nil {
		return nil, fmt.Errorf("数据库连接不可用")
	}

	// 获取所有向量进行比较（简单实现，生产环境需要优化）
	query := `
		SELECT kv.knowledge_id, kv.vector_data, lk.question, lk.answer
		FROM knowledge_vectors kv
		JOIN learned_knowledge lk ON kv.knowledge_id = lk.id
		WHERE kv.vector_type = 'combined'
		LIMIT ?
	`

	rows, err := svs.db.Query(query, limit*10) // 获取更多数据进行筛选
	if err != nil {
		return nil, fmt.Errorf("查询向量失败: %v", err)
	}
	defer rows.Close()

	var results []VectorSearchResult
	for rows.Next() {
		var id int
		var vectorJSON, question, answer string

		err := rows.Scan(&id, &vectorJSON, &question, &answer)
		if err != nil {
			continue
		}

		// 反序列化向量
		var vector []float32
		if err := json.Unmarshal([]byte(vectorJSON), &vector); err != nil {
			continue
		}

		// 计算余弦相似度
		similarity := svs.cosineSimilarity(queryVector, vector)

		results = append(results, VectorSearchResult{
			ID:         id,
			Question:   question,
			Answer:     answer,
			Similarity: similarity,
		})
	}

	// 按相似度排序并返回前N个结果
	return svs.sortAndLimit(results, limit), nil
}

// VectorSearchResult 向量搜索结果
type VectorSearchResult struct {
	ID         int     `json:"id"`
	Question   string  `json:"question"`
	Answer     string  `json:"answer"`
	Similarity float32 `json:"similarity"`
}

// cosineSimilarity 计算余弦相似度
func (svs *SimpleVectorStore) cosineSimilarity(a, b []float32) float32 {
	if len(a) != len(b) {
		return 0
	}

	var dotProduct, normA, normB float32
	for i := range a {
		dotProduct += a[i] * b[i]
		normA += a[i] * a[i]
		normB += b[i] * b[i]
	}

	if normA == 0 || normB == 0 {
		return 0
	}

	return dotProduct / (float32(math.Sqrt(float64(normA))) * float32(math.Sqrt(float64(normB))))
}

// sortAndLimit 排序并限制结果数量
func (svs *SimpleVectorStore) sortAndLimit(results []VectorSearchResult, limit int) []VectorSearchResult {
	// 简单的冒泡排序（按相似度降序）
	for i := 0; i < len(results)-1; i++ {
		for j := 0; j < len(results)-i-1; j++ {
			if results[j].Similarity < results[j+1].Similarity {
				results[j], results[j+1] = results[j+1], results[j]
			}
		}
	}

	// 限制结果数量
	if len(results) > limit {
		results = results[:limit]
	}

	return results
}

// GetVectorCount 获取向量总数
func (svs *SimpleVectorStore) GetVectorCount() (int, error) {
	if svs.db == nil {
		return 0, fmt.Errorf("数据库连接不可用")
	}

	var count int
	query := `SELECT COUNT(*) FROM knowledge_vectors WHERE vector_type = 'combined'`
	err := svs.db.QueryRow(query).Scan(&count)
	if err != nil {
		return 0, fmt.Errorf("查询向量数量失败: %v", err)
	}

	return count, nil
}
