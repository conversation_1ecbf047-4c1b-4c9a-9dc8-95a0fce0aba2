package nlp

import (
	"strings"
	"sync"
)

// StopWordsManager 停用词管理器
type StopWordsManager struct {
	stopWords      map[string]bool
	questionWords  map[string]float64 // 疑问词及其权重降低系数
	technicalWords map[string]float64 // 技术词汇及其权重提升系数
	mutex          sync.RWMutex
}

var (
	globalStopWordsManager *StopWordsManager
	stopWordsOnce          sync.Once
)

// GetGlobalStopWordsManager 获取全局停用词管理器
func GetGlobalStopWordsManager() *StopWordsManager {
	stopWordsOnce.Do(func() {
		globalStopWordsManager = NewStopWordsManager()
	})
	return globalStopWordsManager
}

// NewStopWordsManager 创建停用词管理器
func NewStopWordsManager() *StopWordsManager {
	swm := &StopWordsManager{
		stopWords:      make(map[string]bool),
		questionWords:  make(map[string]float64),
		technicalWords: make(map[string]float64),
	}

	swm.initializeStopWords()
	swm.initializeQuestionWords()
	swm.initializeTechnicalWords()

	return swm
}

// initializeStopWords 初始化停用词列表
func (swm *StopWordsManager) initializeStopWords() {
	// 基础停用词
	basicStopWords := []string{
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人",
		"都", "一", "一个", "上", "也", "很", "到", "说", "要", "去",
		"你", "会", "着", "没有", "看", "好", "自己", "这", "那",
		"为", "以", "时", "来", "用", "们", "生", "对", "作", "地",
		"于", "出", "分", "可", "下", "又", "后", "同", "现", "或",
		"但", "而", "因为", "所以", "如果", "那么", "这个", "那个",
		"一种", "一些", "这些", "那些", "其他", "另外", "还有", "以及",
	}

	// 网页和界面相关停用词
	webStopWords := []string{
		"百度", "搜索", "首页", "登录", "注册", "更多", "展开", "收起",
		"点击", "链接", "跳转", "网站", "页面", "浏览器", "加载",
		"javascript", "function", "var", "document", "window",
	}

	// 标点和符号
	punctuationStopWords := []string{
		"，", "。", "！", "？", "；", "：", "\"", "\"", "'", "'",
		"（", "）", "【", "】", "《", "》", "、", "·", "-", "_",
	}

	// 英文停用词
	englishStopWords := []string{
		"the", "is", "are", "was", "were", "be", "been", "being",
		"have", "has", "had", "do", "does", "did", "will", "would",
		"could", "should", "may", "might", "must", "can", "shall",
		"a", "an", "and", "or", "but", "if", "then", "this", "that",
		"these", "those", "in", "on", "at", "by", "for", "with",
		"from", "to", "of", "as", "into", "through", "during",
	}

	// 合并所有停用词
	allStopWords := append(basicStopWords, webStopWords...)
	allStopWords = append(allStopWords, punctuationStopWords...)
	allStopWords = append(allStopWords, englishStopWords...)

	for _, word := range allStopWords {
		swm.stopWords[strings.ToLower(word)] = true
	}
}

// initializeQuestionWords 初始化疑问词列表（这些词会降低匹配权重）
func (swm *StopWordsManager) initializeQuestionWords() {
	questionWords := map[string]float64{
		// 中文疑问词 - 权重降低系数（越小权重越低）
		"什么":   0.1, // 最通用的疑问词，权重最低
		"是什么":  0.1,
		"什么是":  0.1,
		"怎么":   0.2,
		"如何":   0.2,
		"为什么":  0.2,
		"哪里":   0.3,
		"哪个":   0.3,
		"哪些":   0.3,
		"谁":    0.3,
		"何时":   0.3,
		"什么时候": 0.3,
		"多少":   0.3,
		"几个":   0.3,

		// 英文疑问词
		"what":  0.1,
		"how":   0.2,
		"why":   0.2,
		"where": 0.3,
		"when":  0.3,
		"who":   0.3,
		"which": 0.3,
		"whose": 0.3,

		// 疑问句式
		"是否":  0.2,
		"能否":  0.2,
		"可以吗": 0.2,
		"行吗":  0.2,
		"好吗":  0.2,
		"对吗":  0.2,
	}

	for word, weight := range questionWords {
		swm.questionWords[strings.ToLower(word)] = weight
	}
}

// initializeTechnicalWords 初始化技术词汇列表（这些词会提高匹配权重）
func (swm *StopWordsManager) initializeTechnicalWords() {
	technicalWords := map[string]float64{
		// 编程语言 - 权重提升系数（越大权重越高）
		"go":         3.0,
		"golang":     3.0,
		"python":     3.0,
		"java":       3.0,
		"javascript": 3.0,
		"c++":        3.0,
		"c#":         3.0,
		"csharp":     3.0,
		"rust":       3.0,
		"php":        3.0,
		"ruby":       3.0,
		"swift":      3.0,
		"kotlin":     3.0,
		"scala":      3.0,

		// 数据库
		"mysql":      2.5,
		"postgresql": 2.5,
		"mongodb":    2.5,
		"redis":      2.5,
		"sqlite":     2.5,
		"oracle":     2.5,

		// 框架和工具
		"react":      2.0,
		"vue":        2.0,
		"angular":    2.0,
		"django":     2.0,
		"flask":      2.0,
		"spring":     2.0,
		"express":    2.0,
		"nodejs":     2.0,
		"docker":     2.0,
		"kubernetes": 2.0,
		"k8s":        2.0,

		// AI和机器学习
		"localai":   2.5,
		"embedding": 2.0,
		"vector":    2.0,
		"rag":       2.0,
		"nlp":       2.0,
		"ai":        2.0,
		"ml":        2.0,

		// 技术概念
		"api":          1.5,
		"rest":         1.5,
		"graphql":      1.5,
		"microservice": 1.5,
		"devops":       1.5,
		"cicd":         1.5,
		"配置":           1.5,
		"部署":           1.5,
		"安装":           1.5,
		"环境":           1.5,
		"开发":           1.5,
		"编程":           1.5,
		"数据库":          1.5,
	}

	for word, weight := range technicalWords {
		swm.technicalWords[strings.ToLower(word)] = weight
	}
}

// IsStopWord 检查是否为停用词
func (swm *StopWordsManager) IsStopWord(word string) bool {
	swm.mutex.RLock()
	defer swm.mutex.RUnlock()
	return swm.stopWords[strings.ToLower(word)]
}

// GetQuestionWordWeight 获取疑问词的权重系数
func (swm *StopWordsManager) GetQuestionWordWeight(word string) float64 {
	swm.mutex.RLock()
	defer swm.mutex.RUnlock()
	if weight, exists := swm.questionWords[strings.ToLower(word)]; exists {
		return weight
	}
	return 1.0 // 默认权重
}

// GetTechnicalWordWeight 获取技术词汇的权重系数
func (swm *StopWordsManager) GetTechnicalWordWeight(word string) float64 {
	swm.mutex.RLock()
	defer swm.mutex.RUnlock()
	if weight, exists := swm.technicalWords[strings.ToLower(word)]; exists {
		return weight
	}
	return 1.0 // 默认权重
}

// CalculateWeightedScore 计算加权分数
func (swm *StopWordsManager) CalculateWeightedScore(words []string, baseScore float64) float64 {
	if len(words) == 0 {
		return baseScore
	}

	totalWeight := 0.0
	weightedSum := 0.0

	for _, word := range words {
		wordLower := strings.ToLower(word)

		// 跳过停用词
		if swm.IsStopWord(wordLower) {
			continue
		}

		// 获取权重
		weight := 1.0
		if qWeight := swm.GetQuestionWordWeight(wordLower); qWeight != 1.0 {
			weight = qWeight // 疑问词降低权重
		} else if tWeight := swm.GetTechnicalWordWeight(wordLower); tWeight != 1.0 {
			weight = tWeight // 技术词汇提高权重
		}

		totalWeight += weight
		weightedSum += baseScore * weight
	}

	if totalWeight == 0 {
		return baseScore * 0.1 // 如果全是停用词，大幅降低分数
	}

	return weightedSum / totalWeight
}

// FilterWords 过滤停用词并返回有效词汇
func (swm *StopWordsManager) FilterWords(words []string) []string {
	var filtered []string
	for _, word := range words {
		if !swm.IsStopWord(word) && len(strings.TrimSpace(word)) > 1 {
			filtered = append(filtered, word)
		}
	}
	return filtered
}

// HasSignificantTechnicalWords 检查是否包含重要的技术词汇
func (swm *StopWordsManager) HasSignificantTechnicalWords(text string) bool {
	words := strings.Fields(strings.ToLower(text))
	textLower := strings.ToLower(text)

	// 检查单个词汇
	for _, word := range words {
		if weight := swm.GetTechnicalWordWeight(word); weight > 2.0 {
			return true
		}
	}

	// 检查复合技术词汇
	compoundTechWords := []string{
		"go语言", "c#", "c++", "javascript", "python", "java",
		"mysql", "postgresql", "mongodb", "redis",
		"react", "vue", "angular", "django", "flask",
		"docker", "kubernetes", "localai",
	}

	for _, techWord := range compoundTechWords {
		if strings.Contains(textLower, techWord) {
			return true
		}
	}

	return false
}

// GetDominantWordType 获取文本中占主导地位的词汇类型
func (swm *StopWordsManager) GetDominantWordType(text string) string {
	words := strings.Fields(strings.ToLower(text))

	questionCount := 0
	technicalCount := 0
	totalCount := 0

	for _, word := range words {
		if swm.IsStopWord(word) {
			continue
		}

		totalCount++
		if swm.GetQuestionWordWeight(word) < 1.0 {
			questionCount++
		}
		if swm.GetTechnicalWordWeight(word) > 1.0 {
			technicalCount++
		}
	}

	if totalCount == 0 {
		return "empty"
	}

	questionRatio := float64(questionCount) / float64(totalCount)
	technicalRatio := float64(technicalCount) / float64(totalCount)

	if questionRatio > 0.5 {
		return "question_heavy"
	}
	if technicalRatio > 0.3 {
		return "technical"
	}
	if questionRatio > 0.2 {
		return "question_mixed"
	}

	return "general"
}
