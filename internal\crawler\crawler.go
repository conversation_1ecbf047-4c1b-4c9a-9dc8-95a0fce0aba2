package crawler

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"regexp"
	"sync"
	"time"

	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/nlp"
)

// CrawlerConfig 爬虫配置
type CrawlerConfig struct {
	MaxConcurrency   int           `json:"max_concurrency"`   // 最大并发数
	RequestDelay     time.Duration `json:"request_delay"`     // 请求间隔
	Timeout          time.Duration `json:"timeout"`           // 请求超时
	UserAgent        string        `json:"user_agent"`        // User-Agent
	MaxRetries       int           `json:"max_retries"`       // 最大重试次数
	EnableJavaScript bool          `json:"enable_javascript"` // 是否启用JavaScript渲染
}

// CrawlTarget 爬取目标
type CrawlTarget struct {
	ID          int                    `json:"id"`
	Name        string                 `json:"name"`
	URL         string                 `json:"url"`
	Type        string                 `json:"type"`      // website, api, rss, search_engine
	Category    string                 `json:"category"`  // 知识分类
	Keywords    []string               `json:"keywords"`  // 关键词
	Selectors   map[string]string      `json:"selectors"` // CSS选择器
	Filters     map[string]interface{} `json:"filters"`   // 过滤条件
	Schedule    string                 `json:"schedule"`  // 调度表达式 (cron)
	Enabled     bool                   `json:"enabled"`   // 是否启用
	LastCrawled time.Time              `json:"last_crawled"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// CrawlResult 爬取结果
type CrawlResult struct {
	TargetID    int                    `json:"target_id"`
	URL         string                 `json:"url"`
	Title       string                 `json:"title"`
	Content     string                 `json:"content"`
	Summary     string                 `json:"summary"`
	Keywords    []string               `json:"keywords"`
	Category    string                 `json:"category"`
	Metadata    map[string]interface{} `json:"metadata"`
	CrawledAt   time.Time              `json:"crawled_at"`
	ProcessedAt time.Time              `json:"processed_at"`
	Status      string                 `json:"status"` // pending, processed, failed
}

// KnowledgeCrawler 知识爬虫
type KnowledgeCrawler struct {
	db               *sql.DB
	config           *CrawlerConfig
	knowledgeLearner *learning.KnowledgeLearner
	httpClient       *http.Client
	targets          map[int]*CrawlTarget
	targetsMutex     sync.RWMutex
	activeCrawls     map[int]*CrawlTarget // 正在爬取的目标
	activeMutex      sync.RWMutex
	stopChan         chan struct{}
	wg               sync.WaitGroup
	running          bool

	// 智能爬虫集成
	smartCrawler    *SmartKnowledgeCrawler
	enableSmartMode bool
}

// NewKnowledgeCrawler 创建知识爬虫
func NewKnowledgeCrawler(db *sql.DB, knowledgeLearner *learning.KnowledgeLearner) *KnowledgeCrawler {
	config := &CrawlerConfig{
		MaxConcurrency:   5,
		RequestDelay:     time.Second * 2,
		Timeout:          time.Second * 30,
		UserAgent:        "FAQ-System-Crawler/1.0",
		MaxRetries:       3,
		EnableJavaScript: false,
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	kc := &KnowledgeCrawler{
		db:               db,
		config:           config,
		knowledgeLearner: knowledgeLearner,
		httpClient:       httpClient,
		targets:          make(map[int]*CrawlTarget),
		activeCrawls:     make(map[int]*CrawlTarget),
		stopChan:         make(chan struct{}),
		enableSmartMode:  true, // 默认启用智能模式
	}

	// 初始化智能爬虫
	kc.smartCrawler = NewSmartKnowledgeCrawler(db, knowledgeLearner, nil, nil)

	return kc
}

// NewKnowledgeCrawlerWithNLP 创建带NLP向量化功能的知识爬虫
func NewKnowledgeCrawlerWithNLP(db *sql.DB, knowledgeLearner *learning.KnowledgeLearner, embedClient interface{}, integratedProcessor *nlp.IntegratedProcessor) *KnowledgeCrawler {
	// 首先创建基础爬虫
	kc := NewKnowledgeCrawler(db, knowledgeLearner)

	// 使用NLP组件重新初始化智能爬虫
	kc.smartCrawler = NewSmartKnowledgeCrawler(db, knowledgeLearner, embedClient, integratedProcessor)

	logger.Info("🧠 Knowledge crawler with NLP vectorization created")
	return kc
}

// Start 启动爬虫
func (kc *KnowledgeCrawler) Start() error {
	if kc.running {
		return fmt.Errorf("爬虫已经在运行中")
	}

	log.Println("🕷️ 启动知识爬虫...")

	// 加载爬取目标
	if err := kc.loadTargets(); err != nil {
		return fmt.Errorf("加载爬取目标失败: %v", err)
	}

	kc.running = true

	// 启动调度器
	kc.wg.Add(1)
	go kc.scheduler()

	// 启动处理器
	kc.wg.Add(1)
	go kc.processor()

	log.Println("✅ 知识爬虫启动成功")
	return nil
}

// Stop 停止爬虫
func (kc *KnowledgeCrawler) Stop() {
	if !kc.running {
		return
	}

	log.Println("🛑 停止知识爬虫...")
	kc.running = false
	close(kc.stopChan)
	kc.wg.Wait()
	log.Println("✅ 知识爬虫已停止")
}

// AddTarget 添加爬取目标
func (kc *KnowledgeCrawler) AddTarget(target *CrawlTarget) error {
	// 验证目标配置
	if err := kc.validateTarget(target); err != nil {
		return fmt.Errorf("目标配置无效: %v", err)
	}

	// 保存到数据库
	query := `
		INSERT INTO crawl_targets 
		(name, url, type, category, keywords, selectors, filters, schedule, enabled, created_at, updated_at)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, NOW(), NOW())
	`

	keywordsJSON, _ := json.Marshal(target.Keywords)
	selectorsJSON, _ := json.Marshal(target.Selectors)
	filtersJSON, _ := json.Marshal(target.Filters)

	result, err := kc.db.Exec(query, target.Name, target.URL, target.Type, target.Category,
		keywordsJSON, selectorsJSON, filtersJSON, target.Schedule, target.Enabled)
	if err != nil {
		return fmt.Errorf("保存爬取目标失败: %v", err)
	}

	id, _ := result.LastInsertId()
	target.ID = int(id)
	target.CreatedAt = time.Now()
	target.UpdatedAt = time.Now()

	// 添加到内存缓存
	kc.targetsMutex.Lock()
	kc.targets[target.ID] = target
	kc.targetsMutex.Unlock()

	log.Printf("✅ 添加爬取目标: %s (%s)", target.Name, target.URL)
	return nil
}

// RemoveTarget 移除爬取目标
func (kc *KnowledgeCrawler) RemoveTarget(targetID int) error {
	// 从数据库删除
	_, err := kc.db.Exec("DELETE FROM crawl_targets WHERE id = ?", targetID)
	if err != nil {
		return fmt.Errorf("删除爬取目标失败: %v", err)
	}

	// 从内存缓存删除
	kc.targetsMutex.Lock()
	delete(kc.targets, targetID)
	kc.targetsMutex.Unlock()

	log.Printf("✅ 删除爬取目标: %d", targetID)
	return nil
}

// GetTargets 获取所有爬取目标
func (kc *KnowledgeCrawler) GetTargets() []*CrawlTarget {
	kc.targetsMutex.RLock()
	defer kc.targetsMutex.RUnlock()

	targets := make([]*CrawlTarget, 0, len(kc.targets))
	for _, target := range kc.targets {
		targets = append(targets, target)
	}
	return targets
}

// GetActiveCrawls 获取正在爬取的目标
func (kc *KnowledgeCrawler) GetActiveCrawls() []*CrawlTarget {
	kc.activeMutex.RLock()
	defer kc.activeMutex.RUnlock()

	active := make([]*CrawlTarget, 0, len(kc.activeCrawls))
	for _, target := range kc.activeCrawls {
		active = append(active, target)
	}
	return active
}

// IsRunning 获取爬虫运行状态
func (kc *KnowledgeCrawler) IsRunning() bool {
	return kc.running
}

// EnableSmartMode 启用智能爬取模式
func (kc *KnowledgeCrawler) EnableSmartMode() {
	kc.enableSmartMode = true
	log.Println("🧠 智能爬取模式已启用")
}

// DisableSmartMode 禁用智能爬取模式
func (kc *KnowledgeCrawler) DisableSmartMode() {
	kc.enableSmartMode = false
	log.Println("📝 基础爬取模式已启用")
}

// IsSmartModeEnabled 检查是否启用智能模式
func (kc *KnowledgeCrawler) IsSmartModeEnabled() bool {
	return kc.enableSmartMode
}

// GetSmartCrawler 获取智能爬虫实例
func (kc *KnowledgeCrawler) GetSmartCrawler() *SmartKnowledgeCrawler {
	return kc.smartCrawler
}

// UpdateTarget 更新爬取目标
func (kc *KnowledgeCrawler) UpdateTarget(target *CrawlTarget) error {
	// 验证目标配置
	if err := kc.validateTarget(target); err != nil {
		return fmt.Errorf("目标配置无效: %v", err)
	}

	// 检查目标是否存在
	kc.targetsMutex.RLock()
	existingTarget, exists := kc.targets[target.ID]
	kc.targetsMutex.RUnlock()

	if !exists {
		return fmt.Errorf("目标ID %d 不存在", target.ID)
	}

	// 检查目标是否正在运行
	kc.activeMutex.RLock()
	isActive := kc.activeCrawls[target.ID] != nil
	kc.activeMutex.RUnlock()

	if isActive {
		return fmt.Errorf("目标 %s 正在爬取中，无法修改", existingTarget.Name)
	}

	// 保留原有创建时间，设置更新时间
	target.CreatedAt = existingTarget.CreatedAt
	target.UpdatedAt = time.Now()

	// 更新数据库
	query := `
		UPDATE crawl_targets
		SET name = ?, url = ?, type = ?, category = ?, keywords = ?,
		    selectors = ?, filters = ?, schedule = ?, enabled = ?, updated_at = ?
		WHERE id = ?
	`

	keywordsJSON, _ := json.Marshal(target.Keywords)
	selectorsJSON, _ := json.Marshal(target.Selectors)
	filtersJSON, _ := json.Marshal(target.Filters)

	_, err := kc.db.Exec(query, target.Name, target.URL, target.Type, target.Category,
		keywordsJSON, selectorsJSON, filtersJSON, target.Schedule, target.Enabled, target.UpdatedAt, target.ID)
	if err != nil {
		return fmt.Errorf("更新爬取目标失败: %v", err)
	}

	// 更新内存缓存
	kc.targetsMutex.Lock()
	kc.targets[target.ID] = target
	kc.targetsMutex.Unlock()

	log.Printf("✅ 更新爬取目标: %s (%s)", target.Name, target.URL)
	return nil
}

// loadTargets 从数据库加载爬取目标
func (kc *KnowledgeCrawler) loadTargets() error {
	query := `
		SELECT id, name, url, type, category, keywords, selectors, filters, 
		       schedule, enabled, last_crawled, created_at, updated_at
		FROM crawl_targets
		WHERE enabled = 1
	`

	rows, err := kc.db.Query(query)
	if err != nil {
		return err
	}
	defer rows.Close()

	kc.targetsMutex.Lock()
	defer kc.targetsMutex.Unlock()

	for rows.Next() {
		var target CrawlTarget
		var keywordsJSON, selectorsJSON, filtersJSON string
		var lastCrawled sql.NullTime

		err := rows.Scan(&target.ID, &target.Name, &target.URL, &target.Type, &target.Category,
			&keywordsJSON, &selectorsJSON, &filtersJSON, &target.Schedule, &target.Enabled,
			&lastCrawled, &target.CreatedAt, &target.UpdatedAt)
		if err != nil {
			continue
		}

		json.Unmarshal([]byte(keywordsJSON), &target.Keywords)
		json.Unmarshal([]byte(selectorsJSON), &target.Selectors)
		json.Unmarshal([]byte(filtersJSON), &target.Filters)

		if lastCrawled.Valid {
			target.LastCrawled = lastCrawled.Time
		}

		kc.targets[target.ID] = &target
	}

	log.Printf("📊 加载了 %d 个爬取目标", len(kc.targets))
	return nil
}

// validateTarget 验证爬取目标配置
func (kc *KnowledgeCrawler) validateTarget(target *CrawlTarget) error {
	if target.Name == "" {
		return fmt.Errorf("目标名称不能为空")
	}
	if target.URL == "" {
		return fmt.Errorf("目标URL不能为空")
	}
	if target.Type == "" {
		target.Type = "website"
	}
	if target.Category == "" {
		target.Category = "general"
	}
	if target.Schedule == "" {
		target.Schedule = "0 */6 * * *" // 默认每6小时执行一次
	}
	return nil
}

// scheduler 调度器
func (kc *KnowledgeCrawler) scheduler() {
	defer kc.wg.Done()

	ticker := time.NewTicker(time.Minute) // 每1分钟检查一次，支持每分钟调度
	defer ticker.Stop()

	for {
		select {
		case <-kc.stopChan:
			return
		case <-ticker.C:
			kc.checkAndScheduleTasks()
		}
	}
}

// processor 处理器
func (kc *KnowledgeCrawler) processor() {
	defer kc.wg.Done()

	ticker := time.NewTicker(time.Second * 10) // 每10秒处理一次
	defer ticker.Stop()

	for {
		select {
		case <-kc.stopChan:
			return
		case <-ticker.C:
			kc.processResults()
		}
	}
}

// checkAndScheduleTasks 检查并调度任务
func (kc *KnowledgeCrawler) checkAndScheduleTasks() {
	kc.targetsMutex.RLock()
	targets := make([]*CrawlTarget, 0, len(kc.targets))
	for _, target := range kc.targets {
		if target.Enabled {
			targets = append(targets, target)
		}
	}
	kc.targetsMutex.RUnlock()

	for _, target := range targets {
		if kc.shouldCrawl(target) {
			if kc.enableSmartMode {
				// 使用智能爬取
				go func(t *CrawlTarget) {
					if err := kc.startSmartCrawlForTarget(t); err != nil {
						log.Printf("❌ 智能爬取启动失败 %s: %v", t.Name, err)
					}
				}(target)
			} else {
				// 使用基础爬取
				kc.scheduleCrawlTask(target)
			}
		}
	}
}

// shouldCrawl 判断是否应该爬取
func (kc *KnowledgeCrawler) shouldCrawl(target *CrawlTarget) bool {
	// 简单的时间间隔检查（实际应该使用cron表达式解析）
	if target.LastCrawled.IsZero() {
		return true
	}

	// 根据调度表达式判断
	switch target.Schedule {
	case "* * * * *": // 每分钟
		return time.Since(target.LastCrawled) >= time.Minute
	case "0 */1 * * *": // 每小时
		return time.Since(target.LastCrawled) >= time.Hour
	case "0 */6 * * *": // 每6小时
		return time.Since(target.LastCrawled) >= time.Hour*6
	case "0 0 * * *": // 每天
		return time.Since(target.LastCrawled) >= time.Hour*24
	case "0 0 * * 0": // 每周
		return time.Since(target.LastCrawled) >= time.Hour*24*7
	default:
		return time.Since(target.LastCrawled) >= time.Hour*6
	}
}

// scheduleCrawlTask 调度爬取任务
func (kc *KnowledgeCrawler) scheduleCrawlTask(target *CrawlTarget) {
	go func() {
		// 添加到活跃爬取列表
		kc.activeMutex.Lock()
		kc.activeCrawls[target.ID] = target
		kc.activeMutex.Unlock()

		log.Printf("🕷️ 开始爬取: %s (%s)", target.Name, target.URL)

		result, err := kc.crawlTarget(target)
		if err != nil {
			log.Printf("爬取失败 %s: %v", target.Name, err)
			// 从活跃列表中移除
			kc.activeMutex.Lock()
			delete(kc.activeCrawls, target.ID)
			kc.activeMutex.Unlock()
			return
		}

		// 保存爬取结果
		if err := kc.saveCrawlResult(result); err != nil {
			log.Printf("保存爬取结果失败: %v", err)
			// 从活跃列表中移除
			kc.activeMutex.Lock()
			delete(kc.activeCrawls, target.ID)
			kc.activeMutex.Unlock()
			return
		}

		// 更新最后爬取时间
		kc.updateLastCrawled(target.ID) //internal\crawler\crawler.go

		// 从活跃列表中移除
		kc.activeMutex.Lock()
		delete(kc.activeCrawls, target.ID)
		kc.activeMutex.Unlock()

		log.Printf("✅ 爬取完成: %s", target.Name)
	}()
}

// ManualCrawl 手动触发爬取
func (kc *KnowledgeCrawler) ManualCrawl(targetID int) error {
	kc.targetsMutex.RLock()
	target, exists := kc.targets[targetID]
	kc.targetsMutex.RUnlock()

	if !exists {
		return fmt.Errorf("目标不存在: %d", targetID)
	}

	// 检查是否已经在爬取中
	kc.activeMutex.RLock()
	_, isActive := kc.activeCrawls[targetID]
	kc.activeMutex.RUnlock()

	if isActive {
		return fmt.Errorf("目标正在爬取中: %s", target.Name)
	}

	// 根据模式选择爬取方式
	if kc.enableSmartMode {
		log.Printf("🧠 手动触发智能爬取: %s (%s)", target.Name, target.URL)
		return kc.startSmartCrawlForTarget(target)
	} else {
		log.Printf("🚀 手动触发基础爬取: %s (%s)", target.Name, target.URL)
		kc.scheduleCrawlTask(target)
		return nil
	}
}

// startSmartCrawlForTarget 为单个目标启动智能爬取
func (kc *KnowledgeCrawler) startSmartCrawlForTarget(target *CrawlTarget) error {
	if kc.smartCrawler == nil {
		return fmt.Errorf("智能爬虫未初始化")
	}

	// 转换为智能爬取目标
	smartTarget := kc.convertToSmartTarget(target)

	// 启动智能爬取
	return kc.smartCrawler.StartSmartCrawl(smartTarget)
}

// convertToSmartTarget 将基础目标转换为智能爬取目标
func (kc *KnowledgeCrawler) convertToSmartTarget(target *CrawlTarget) *SmartCrawlTarget {
	// 创建默认的智能爬取规则
	rules := []CrawlRule{
		{
			URLPattern:      fmt.Sprintf(`%s.*`, regexp.QuoteMeta(target.URL)),
			FollowLinks:     []string{"a[href]"},
			IgnoreLinks:     []string{"a[href*='download']", "a[href*='ad']", "a[href*='login']"},
			ContentSelector: "article, .content, .post, .entry, main, #content, .article-content",
			TitleSelector:   "h1, .title, .post-title, .article-title",
			MetaSelectors: map[string]string{
				"author":       ".author, .by-author, .post-author",
				"publish_time": ".date, .time, .publish-time, .post-date",
				"tags":         ".tags a, .tag, .post-tags",
			},
			RequiredWords: target.Keywords,
			ExcludedWords: []string{"广告", "推广", "营销", "advertisement", "promotion"},
			MinWordCount:  200,
			MaxDepth:      3,
		},
	}

	// 如果有自定义选择器，使用自定义规则
	if len(target.Selectors) > 0 {
		customRule := CrawlRule{
			URLPattern:    fmt.Sprintf(`%s.*`, regexp.QuoteMeta(target.URL)),
			FollowLinks:   []string{"a[href]"},
			IgnoreLinks:   []string{"a[href*='download']", "a[href*='ad']"},
			RequiredWords: target.Keywords,
			MinWordCount:  150,
			MaxDepth:      2,
		}

		if contentSelector, ok := target.Selectors["content"]; ok {
			customRule.ContentSelector = contentSelector
		}
		if titleSelector, ok := target.Selectors["title"]; ok {
			customRule.TitleSelector = titleSelector
		}

		rules = []CrawlRule{customRule}
	}

	// 提取域名作为允许域名
	allowedDomains := []string{}
	if u, err := url.Parse(target.URL); err == nil {
		allowedDomains = append(allowedDomains, u.Host)
	}

	return &SmartCrawlTarget{
		CrawlTarget:      target,
		Rules:            rules,
		SeedURLs:         []string{target.URL},
		AllowedDomains:   allowedDomains,
		BlockedDomains:   []string{"ads.", "ad.", "advertisement.", "promo."},
		CrawlStrategy:    "smart",
		PriorityKeywords: target.Keywords,
	}
}

// StartSmartCrawlForAll 为所有启用的目标启动智能爬取
func (kc *KnowledgeCrawler) StartSmartCrawlForAll() error {
	if !kc.enableSmartMode {
		return fmt.Errorf("智能模式未启用")
	}

	kc.targetsMutex.RLock()
	targets := make([]*CrawlTarget, 0, len(kc.targets))
	for _, target := range kc.targets {
		if target.Enabled {
			targets = append(targets, target)
		}
	}
	kc.targetsMutex.RUnlock()

	log.Printf("🧠 开始为 %d 个目标启动智能爬取", len(targets))

	for _, target := range targets {
		log.Printf("🚀 启动智能爬取: %s", target.Name)
		if err := kc.startSmartCrawlForTarget(target); err != nil {
			log.Printf("❌ 智能爬取启动失败 %s: %v", target.Name, err)
			continue
		}
	}

	return nil
}

// StopSmartCrawl 停止智能爬取
func (kc *KnowledgeCrawler) StopSmartCrawl() {
	if kc.smartCrawler != nil {
		kc.smartCrawler.StopSmartCrawl()
	}
}

// DeleteTarget 删除爬取目标
func (kc *KnowledgeCrawler) DeleteTarget(targetID int) error {
	kc.targetsMutex.Lock()
	defer kc.targetsMutex.Unlock()

	// 检查目标是否存在
	target, exists := kc.targets[targetID]
	if !exists {
		return fmt.Errorf("目标ID %d 不存在", targetID)
	}

	// 检查目标是否正在运行
	kc.activeMutex.RLock()
	isActive := kc.activeCrawls[targetID] != nil
	kc.activeMutex.RUnlock()

	if isActive {
		return fmt.Errorf("目标 %s 正在爬取中，无法删除", target.Name)
	}

	// 从数据库删除
	if err := kc.deleteTargetFromDB(targetID); err != nil {
		return fmt.Errorf("删除数据库记录失败: %v", err)
	}

	// 从内存删除
	delete(kc.targets, targetID)

	log.Printf("🗑️ 删除爬取目标: %s (%s)", target.Name, target.URL)
	return nil
}

// deleteTargetFromDB 从数据库删除目标
func (kc *KnowledgeCrawler) deleteTargetFromDB(targetID int) error {
	query := "DELETE FROM crawl_targets WHERE id = ?"
	_, err := kc.db.Exec(query, targetID)
	return err
}

// GetTargetByID 根据ID获取目标
func (kc *KnowledgeCrawler) GetTargetByID(targetID int) (*CrawlTarget, error) {
	kc.targetsMutex.RLock()
	defer kc.targetsMutex.RUnlock()

	target, exists := kc.targets[targetID]
	if !exists {
		return nil, fmt.Errorf("目标ID %d 不存在", targetID)
	}

	// 返回副本，避免外部修改
	targetCopy := *target
	return &targetCopy, nil
}

// EnableTarget 启用目标
func (kc *KnowledgeCrawler) EnableTarget(targetID int) error {
	return kc.setTargetEnabled(targetID, true)
}

// DisableTarget 禁用目标
func (kc *KnowledgeCrawler) DisableTarget(targetID int) error {
	return kc.setTargetEnabled(targetID, false)
}

// setTargetEnabled 设置目标启用状态
func (kc *KnowledgeCrawler) setTargetEnabled(targetID int, enabled bool) error {
	kc.targetsMutex.Lock()
	defer kc.targetsMutex.Unlock()

	target, exists := kc.targets[targetID]
	if !exists {
		return fmt.Errorf("目标ID %d 不存在", targetID)
	}

	// 更新状态
	target.Enabled = enabled
	target.UpdatedAt = time.Now()

	// 更新数据库
	query := "UPDATE crawl_targets SET enabled = ?, updated_at = ? WHERE id = ?"
	_, err := kc.db.Exec(query, enabled, target.UpdatedAt, targetID)
	if err != nil {
		return fmt.Errorf("更新数据库失败: %v", err)
	}

	status := "禁用"
	if enabled {
		status = "启用"
	}
	log.Printf("🔄 %s爬取目标: %s", status, target.Name)
	return nil
}
