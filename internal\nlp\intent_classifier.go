package nlp

import (
	"log"
	"regexp"
	"strings"
)

// IntentClassifier 意图分类器
type IntentClassifier struct {
	patterns map[string][]*regexp.Regexp
	keywords map[string][]string
}

// IntentResult 意图识别结果
type IntentResult struct {
	Intent     string                 `json:"intent"`
	Category   string                 `json:"category"`
	Confidence float64                `json:"confidence"`
	Parameters map[string]interface{} `json:"parameters"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// NewIntentClassifier 创建意图分类器
func NewIntentClassifier() *IntentClassifier {
	log.Printf("🎯 初始化意图分类器...")

	ic := &IntentClassifier{
		patterns: make(map[string][]*regexp.Regexp),
		keywords: make(map[string][]string),
	}

	ic.initializePatterns()
	ic.initializeKeywords()

	log.Printf("✅ 意图分类器初始化完成")
	return ic
}

// initializePatterns 初始化模式
func (ic *IntentClassifier) initializePatterns() {
	// 问题询问类
	ic.addPattern("question", `^(什么是|什么叫|如何|怎么|怎样|为什么|为何|哪里|哪个|谁|何时|何地)`)
	ic.addPattern("question", `(是什么|怎么办|如何做|怎么用|怎么样|好不好)`)
	ic.addPattern("question", `.*[？?]$`)

	// 请求帮助类
	ic.addPattern("help", `^(帮我|请帮|能否|可以|麻烦|求助)`)
	ic.addPattern("help", `(需要帮助|寻求帮助|求解|指导)`)

	// 身份询问类 - 使用更精确的正则表达式
	ic.addPattern("identity_inquiry", `^(你是谁|你叫什么|你的名字|你是什么|你是哪个|你到底是谁|你究竟是什么)`)
	ic.addPattern("identity_inquiry", `^(告诉我你是谁|说说你是谁|你的真实身份|你的身份是什么|你的身份)`)
	ic.addPattern("identity_inquiry", `^(你是机器人吗|你是AI吗|你是人工智能吗|你是真人吗|你是程序吗|你是软件吗)`)
	ic.addPattern("identity_inquiry", `^(你是什么系统|你是什么助手|你是什么机器人|你从哪来|你来自哪里)`)
	ic.addPattern("identity_inquiry", `^(你的创造者|谁创造了你|谁开发了你|你的开发者|你的制作者)`)
	ic.addPattern("identity_inquiry", `^(你的背景|你的来历|你的故事|关于你|你的简介|你的资料)`)
	ic.addPattern("identity_inquiry", `^(认识一下|自我介绍|介绍下你自己|说说你自己|你自己介绍一下|介绍一下自己)`)
	ic.addPattern("identity_inquiry", `^你是`) // 匹配以"你是"开头的问题
	ic.addPattern("identity_inquiry", `^你`)  // 匹配以"你"开头的身份相关问题

	// 技术查询类
	ic.addPattern("technical", `(API|接口|函数|方法|算法|数据库|服务器|代码|编程|开发)`)
	ic.addPattern("technical", `(配置|部署|安装|调试|错误|异常|bug|性能|优化)`)

	// 信息查找类
	ic.addPattern("search", `^(查找|搜索|寻找|找到|获取|得到)`)
	ic.addPattern("search", `(在哪里|如何找|怎么找|查询|检索)`)

	// 比较评估类
	ic.addPattern("compare", `(比较|对比|区别|差异|优缺点|哪个好|选择)`)
	ic.addPattern("compare", `(vs|versus|和.*比|相比|对比)`)

	// 操作指令类
	ic.addPattern("command", `^(创建|删除|修改|更新|添加|移除|启动|停止|重启)`)
	ic.addPattern("command", `(执行|运行|操作|处理|生成|导出|导入)`)

	// 解释说明类
	ic.addPattern("explain", `(解释|说明|阐述|详细|具体|举例|例子)`)
	ic.addPattern("explain", `(原理|机制|工作方式|实现方法)`)

	// 问题报告类
	ic.addPattern("issue", `(问题|错误|异常|故障|失败|不工作|无法|不能)`)
	ic.addPattern("issue", `(报错|出错|崩溃|卡住|慢|超时)`)

	// 建议咨询类
	ic.addPattern("advice", `(建议|推荐|意见|看法|想法|觉得)`)
	ic.addPattern("advice", `(应该|最好|推荐|建议用|用什么好)`)

	// 闲聊类
	ic.addPattern("chat", `^(你好|hi|hello|嗨|早上好|下午好|晚上好)`)
	ic.addPattern("chat", `(谢谢|感谢|再见|拜拜|bye|thanks)`)

	// 反馈评价类
	ic.addPattern("feedback", `^(不错|很好|好的|棒|赞|厉害|优秀|完美|太好了|好|ok|OK)$`)
	ic.addPattern("feedback", `^(满意|满足|可以|行|没问题)$`)

	// 感谢类 - 专门的感谢识别模式
	ic.addPattern("thanks", `^(谢谢|多谢|感谢|thank you|thanks|thx|ty)$`)
	ic.addPattern("thanks", `^(谢谢|多谢|感谢|thank you|thanks|thx|ty)[！!。.]*$`)
	ic.addPattern("thanks", `^(真的谢谢|真的感谢|非常感谢|十分感谢|万分感谢|特别感谢)$`)
	ic.addPattern("thanks", `^(真的谢谢|真的感谢|非常感谢|十分感谢|万分感谢|特别感谢)[！!。.]*$`)
	ic.addPattern("thanks", `^(太感谢了|感谢不尽|无比感谢|深深感谢|由衷感谢)$`)
	ic.addPattern("thanks", `^(thank you so much|thank you very much|thanks a lot|many thanks)$`)
	ic.addPattern("thanks", `^(感谢您|谢谢您|多谢您|致谢|表示感谢|深表感谢)$`)
	ic.addPattern("thanks", `^(感谢您|谢谢您|多谢您|致谢|表示感谢|深表感谢)[！!。.]*$`)
	ic.addPattern("thanks", `^(谢了|谢啦|3q|3Q|tks)$`)
	ic.addPattern("thanks", `^(谢了|谢啦|3q|3Q|tks)[！!。.]*$`)
	ic.addPattern("thanks", `^(谢谢你的帮助|感谢你的帮助|谢谢你的回答|感谢你的回答)$`)
	ic.addPattern("thanks", `^(谢谢你的帮助|感谢你的帮助|谢谢你的回答|感谢你的回答)[！!。.]*$`)
	ic.addPattern("thanks", `(谢谢.*帮助|感谢.*帮助|谢谢.*回答|感谢.*回答)`)
	ic.addPattern("thanks", `(谢谢.*解答|感谢.*解答|谢谢.*指导|感谢.*指导)`)

	// 问候类 - 使用更全面的问候语模式
	ic.addPattern("greeting", `^(你好|您好|hi|hello|嗨|hey|哈喽)$`)
	ic.addPattern("greeting", `^(你好|您好|hi|hello|嗨|hey|哈喽)[！!。.]*$`)
	ic.addPattern("greeting", `^(早上好|上午好|中午好|下午好|晚上好|晚安|good morning|good afternoon|good evening|good night)$`)
	ic.addPattern("greeting", `^(早上好|上午好|中午好|下午好|晚上好|晚安|good morning|good afternoon|good evening|good night)[！!。.]*$`)
	ic.addPattern("greeting", `^(早|早啊|晚安|安)$`)
	ic.addPattern("greeting", `^(大家好|各位好|朋友们好|小伙伴们好|同学们好|老师好|同事们好)$`)
	ic.addPattern("greeting", `^(大家好|各位好|朋友们好|小伙伴们好|同学们好|老师好|同事们好)[！!。.]*$`)
	ic.addPattern("greeting", `^(注意身体|保重|多保重|身体健康|注意休息|早点休息|好好休息)$`)
	ic.addPattern("greeting", `^(注意身体|保重|多保重|身体健康|注意休息|早点休息|好好休息)[！!。.]*$`)
	ic.addPattern("greeting", `^(再见|拜拜|bye|goodbye|see you|回见|一会见|明天见|下次见|88)$`)
	ic.addPattern("greeting", `^(再见|拜拜|bye|goodbye|see you|回见|一会见|明天见|下次见|88)[！!。.]*$`)

	// 确认同意类
	ic.addPattern("confirmation", `^(嗯|嗯嗯|嗯嗯嗯|嗯哼|嗯呢|嗯啊)$`)
	ic.addPattern("confirmation", `^(是的|是|对|对的|没错|正确|同意|认同)$`)
	ic.addPattern("confirmation", `^(好吧|行吧|可以|行|知道了|明白了|了解)$`)
	ic.addPattern("confirmation", `^(嗯好|嗯行|嗯对|嗯是|嗯可以)$`)

	// 算法/数学计算类
	ic.addPattern("algorithm_request", `^\d+\s*[\+\-\*\/]\s*\d+\s*=?\s*\??\s*$`) // 1+1, 2*3=?, 等
	ic.addPattern("algorithm_request", `(计算|求|算|等于|多少|结果).*\d+.*[\+\-\*\/].*\d+`)
	ic.addPattern("algorithm_request", `\d+.*[\+\-\*\/].*\d+.*(等于|多少|结果|答案)`)
	ic.addPattern("algorithm_request", `^(计算|求解|求|算).*[\+\-\*\/]`)
	ic.addPattern("algorithm_request", `(数学|算术|运算|计算|求解)`)
	ic.addPattern("algorithm_request", `(加|减|乘|除|平方|开方|幂|次方).*\d+`)
	ic.addPattern("algorithm_request", `\d+.*(加|减|乘|除|平方|开方|幂|次方)`)

	// 道歉类
	ic.addPattern("apology", `^(对不起|抱歉|不好意思|sorry|excuse me|pardon)$`)
	ic.addPattern("apology", `^(对不起|抱歉|不好意思|sorry|excuse me|pardon)[！!。.]*$`)
	ic.addPattern("apology", `^(真的对不起|非常抱歉|十分抱歉|很抱歉|实在抱歉)$`)
	ic.addPattern("apology", `^(我道歉|向你道歉|给你道歉|请原谅|原谅我)$`)

	// 错误承认类
	ic.addPattern("error_acknowledgment", `^(我错了|是我错了|我的错|我弄错了|我搞错了)$`)
	ic.addPattern("error_acknowledgment", `^(我错了|是我错了|我的错|我弄错了|我搞错了)[！!。.]*$`)
	ic.addPattern("error_acknowledgment", `^(你错了|你弄错了|你搞错了|都错了|全错了|错了)$`)
	ic.addPattern("error_acknowledgment", `^(这是错的|这不对|这有问题|不是这样的|有误)$`)
}

// initializeKeywords 初始化关键词
func (ic *IntentClassifier) initializeKeywords() {
	ic.keywords["question"] = []string{"什么", "如何", "怎么", "为什么", "哪里", "谁", "何时"}
	ic.keywords["help"] = []string{"帮助", "求助", "指导", "协助", "支持"}
	ic.keywords["identity_inquiry"] = []string{
		"你是谁", "你叫什么", "你的名字", "你是什么", "你是哪个", "你到底是谁", "你究竟是什么",
		"告诉我你是谁", "说说你是谁", "你的真实身份", "你的身份是什么", "你的身份",
		"你是机器人吗", "你是AI吗", "你是人工智能吗", "你是真人吗", "你是程序吗", "你是软件吗",
		"你是什么系统", "你是什么助手", "你是什么机器人", "你从哪来", "你来自哪里",
		"你的创造者", "谁创造了你", "谁开发了你", "你的开发者", "你的制作者",
		"你的背景", "你的来历", "你的故事", "关于你", "你的简介", "你的资料",
		"认识一下", "自我介绍", "介绍下你自己", "说说你自己", "你自己介绍一下", "介绍一下自己",
		"你是", "你", "身份", "名字", "机器人", "AI", "人工智能", "真人", "程序", "软件",
		"系统", "助手", "创造者", "开发者", "制作者", "背景", "来历", "故事", "简介", "资料",
	}
	ic.keywords["technical"] = []string{"技术", "开发", "编程", "代码", "API", "数据库", "服务器"}
	ic.keywords["search"] = []string{"查找", "搜索", "寻找", "获取", "检索"}
	ic.keywords["compare"] = []string{"比较", "对比", "区别", "差异", "选择"}
	ic.keywords["command"] = []string{"创建", "删除", "修改", "执行", "运行", "操作"}
	ic.keywords["explain"] = []string{"解释", "说明", "阐述", "原理", "机制"}
	ic.keywords["issue"] = []string{"问题", "错误", "异常", "故障", "失败"}
	ic.keywords["advice"] = []string{"建议", "推荐", "意见", "应该", "最好"}
	ic.keywords["chat"] = []string{"你好", "谢谢", "再见", "感谢"}
	ic.keywords["feedback"] = []string{"不错", "很好", "好的", "棒", "赞", "厉害", "优秀", "完美", "满意", "好", "ok"}
	ic.keywords["greeting"] = []string{
		"你好", "您好", "hi", "hello", "嗨", "hey", "哈喽",
		"早上好", "上午好", "中午好", "下午好", "晚上好", "晚安",
		"good morning", "good afternoon", "good evening", "good night",
		"早", "早啊", "安", "大家好", "各位好", "朋友们好", "小伙伴们好",
		"注意身体", "保重", "多保重", "身体健康", "注意休息", "早点休息", "好好休息",
		"再见", "拜拜", "bye", "goodbye", "see you", "回见", "一会见", "明天见", "下次见", "88",
	}
	ic.keywords["confirmation"] = []string{"嗯", "嗯嗯", "是的", "是", "对", "没错", "同意", "好吧", "行", "知道了", "明白了"}
	ic.keywords["algorithm_request"] = []string{"计算", "求", "算", "等于", "多少", "结果", "答案", "数学", "算术", "运算", "加", "减", "乘", "除", "平方", "开方", "幂", "次方"}
	ic.keywords["apology"] = []string{
		"对不起", "抱歉", "不好意思", "sorry", "excuse me", "pardon",
		"真的对不起", "非常抱歉", "十分抱歉", "很抱歉", "实在抱歉",
		"道歉", "向你道歉", "给你道歉", "请原谅", "原谅我", "谅解", "宽恕",
	}
	ic.keywords["error_acknowledgment"] = []string{
		"我错了", "是我错了", "我的错", "我弄错了", "我搞错了",
		"你错了", "你弄错了", "你搞错了", "都错了", "全错了", "错了",
		"这是错的", "这不对", "这有问题", "不是这样的", "有误", "不正确", "不准确",
		"我理解错了", "我想错了", "我说错了", "我做错了", "失误", "疏忽",
	}
	ic.keywords["thanks"] = []string{
		"谢谢", "多谢", "感谢", "thank you", "thanks", "thx", "ty",
		"真的谢谢", "真的感谢", "非常感谢", "十分感谢", "万分感谢", "特别感谢",
		"太感谢了", "感谢不尽", "无比感谢", "深深感谢", "由衷感谢",
		"thank you so much", "thank you very much", "thanks a lot", "many thanks",
		"感谢您", "谢谢您", "多谢您", "致谢", "表示感谢", "深表感谢",
		"谢了", "谢啦", "3q", "3Q", "tks", "帮助", "回答", "解答", "指导", "支持", "服务",
	}
}

// addPattern 添加模式
func (ic *IntentClassifier) addPattern(intent string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	ic.patterns[intent] = append(ic.patterns[intent], regex)
}

// ClassifyIntent 分类意图
func (ic *IntentClassifier) ClassifyIntent(text string) IntentResult {
	text = strings.ToLower(strings.TrimSpace(text))

	if text == "" {
		return IntentResult{
			Intent:     "unknown",
			Category:   "general",
			Confidence: 0.0,
			Parameters: make(map[string]interface{}),
			Metadata:   make(map[string]interface{}),
		}
	}

	// 计算每个意图的得分
	scores := make(map[string]float64)

	// 1. 基于正则模式匹配
	for intent, patterns := range ic.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				scores[intent] += 0.8
			}
		}
	}

	// 2. 基于关键词匹配
	for intent, keywords := range ic.keywords {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				scores[intent] += 0.3
			}
		}
	}

	// 3. 特殊规则
	ic.applySpecialRules(text, scores)

	// 找到最高得分的意图
	bestIntent := "general"
	bestScore := 0.0

	for intent, score := range scores {
		if score > bestScore {
			bestIntent = intent
			bestScore = score
		}
	}

	// 如果得分太低，归类为一般问题
	if bestScore < 0.5 { // 🛡️ 提高意图分类的阈值
		bestIntent = "general"
		bestScore = 0.5
	}

	// 标准化置信度
	confidence := bestScore
	if confidence > 1.0 {
		confidence = 1.0
	}

	return IntentResult{
		Intent:     bestIntent,
		Category:   ic.getCategory(bestIntent),
		Confidence: confidence,
		Parameters: ic.extractParameters(text, bestIntent),
		Metadata: map[string]interface{}{
			"text_length": len(text),
			"word_count":  len(strings.Fields(text)),
			"all_scores":  scores,
		},
	}
}

// applySpecialRules 应用特殊规则
func (ic *IntentClassifier) applySpecialRules(text string, scores map[string]float64) {
	// 问号强烈表示问题
	if strings.Contains(text, "?") || strings.Contains(text, "？") {
		scores["question"] += 0.5
	}

	// 感叹号可能表示问题或命令
	if strings.Contains(text, "!") || strings.Contains(text, "！") {
		scores["issue"] += 0.2
		scores["command"] += 0.2
	}

	// 长文本更可能是解释请求
	if len(text) > 100 {
		scores["explain"] += 0.2
	}

	// 短文本更可能是简单查询
	if len(text) < 20 {
		scores["search"] += 0.1
	}

	// 包含技术词汇
	techWords := []string{"api", "json", "http", "sql", "html", "css", "js", "python", "go", "java"}
	for _, word := range techWords {
		if strings.Contains(text, word) {
			scores["technical"] += 0.3
			break
		}
	}

	// 数学表达式特殊规则
	if ic.containsMathExpression(text) {
		scores["algorithm_request"] += 1.0 // 高权重
	}

	// 身份询问特殊规则 - 智能识别身份相关问题
	ic.applyIdentityInquiryRules(text, scores)

	// 问候语特殊规则 - 智能识别问候语
	ic.applyGreetingRules(text, scores)

	// 道歉/错误特殊规则 - 智能识别道歉和错误
	ic.applyApologyRules(text, scores)

	// 感谢特殊规则 - 智能识别感谢
	ic.applyGratitudeRules(text, scores)
}

// containsMathExpression 检查是否包含数学表达式
func (ic *IntentClassifier) containsMathExpression(text string) bool {
	text = strings.TrimSpace(text)

	// 检查是否包含数字和运算符的组合
	hasNumber := false
	hasOperator := false

	for _, char := range text {
		if char >= '0' && char <= '9' {
			hasNumber = true
		}
		if strings.ContainsRune("+-*/=×÷", char) {
			hasOperator = true
		}
	}

	// 如果同时包含数字和运算符，很可能是数学表达式
	if hasNumber && hasOperator {
		return true
	}

	// 检查是否包含数学函数
	mathFunctions := []string{"sin", "cos", "tan", "log", "ln", "sqrt", "abs", "exp", "pow"}
	for _, fn := range mathFunctions {
		if strings.Contains(text, fn) {
			return true
		}
	}

	// 检查是否包含明确的数学计算词汇
	mathWords := []string{"计算", "求", "等于", "多少", "结果"}
	for _, word := range mathWords {
		if strings.Contains(text, word) && hasNumber {
			return true
		}
	}

	return false
}

// getCategory 获取分类
func (ic *IntentClassifier) getCategory(intent string) string {
	categories := map[string]string{
		"question":             "inquiry",
		"help":                 "support",
		"identity_inquiry":     "social",
		"technical":            "technical",
		"search":               "information",
		"compare":              "analysis",
		"command":              "action",
		"explain":              "education",
		"issue":                "support",
		"advice":               "consultation",
		"chat":                 "social",
		"feedback":             "social",
		"greeting":             "social",
		"confirmation":         "social",
		"algorithm_request":    "computation",
		"apology":              "social",
		"error_acknowledgment": "social",
		"thanks":               "social",
		"general":              "general",
	}

	if category, exists := categories[intent]; exists {
		return category
	}
	return "general"
}

// extractParameters 提取参数
func (ic *IntentClassifier) extractParameters(text string, intent string) map[string]interface{} {
	params := make(map[string]interface{})

	switch intent {
	case "question":
		params["question_type"] = ic.getQuestionType(text)
	case "technical":
		params["tech_domain"] = ic.getTechDomain(text)
	case "search":
		params["search_target"] = ic.getSearchTarget(text)
	case "compare":
		params["compare_items"] = ic.getCompareItems(text)
	}

	return params
}

// getQuestionType 获取问题类型
func (ic *IntentClassifier) getQuestionType(text string) string {
	if strings.Contains(text, "什么") || strings.Contains(text, "what") {
		return "what"
	} else if strings.Contains(text, "如何") || strings.Contains(text, "怎么") || strings.Contains(text, "how") {
		return "how"
	} else if strings.Contains(text, "为什么") || strings.Contains(text, "why") {
		return "why"
	} else if strings.Contains(text, "哪里") || strings.Contains(text, "where") {
		return "where"
	} else if strings.Contains(text, "何时") || strings.Contains(text, "when") {
		return "when"
	}
	return "general"
}

// getTechDomain 获取技术领域
func (ic *IntentClassifier) getTechDomain(text string) string {
	domains := map[string][]string{
		"web":      {"html", "css", "javascript", "react", "vue", "angular"},
		"backend":  {"api", "server", "database", "sql", "redis", "mongodb"},
		"mobile":   {"android", "ios", "flutter", "react native"},
		"devops":   {"docker", "kubernetes", "ci/cd", "jenkins", "git"},
		"ai":       {"machine learning", "deep learning", "nlp", "ai", "算法"},
		"language": {"python", "java", "go", "c++", "javascript", "php"},
	}

	text = strings.ToLower(text)
	for domain, keywords := range domains {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				return domain
			}
		}
	}
	return "general"
}

// getSearchTarget 获取搜索目标
func (ic *IntentClassifier) getSearchTarget(text string) string {
	if strings.Contains(text, "文档") || strings.Contains(text, "资料") {
		return "documentation"
	} else if strings.Contains(text, "代码") || strings.Contains(text, "示例") {
		return "code"
	} else if strings.Contains(text, "工具") || strings.Contains(text, "软件") {
		return "tools"
	}
	return "general"
}

// getCompareItems 获取比较项目
func (ic *IntentClassifier) getCompareItems(text string) []string {
	// 简单的比较项目提取
	words := strings.Fields(text)
	var items []string

	for i, word := range words {
		if (word == "和" || word == "与" || word == "vs") && i > 0 && i < len(words)-1 {
			if i > 0 {
				items = append(items, words[i-1])
			}
			if i < len(words)-1 {
				items = append(items, words[i+1])
			}
		}
	}

	return items
}

// applyIdentityInquiryRules 应用身份询问特殊规则
func (ic *IntentClassifier) applyIdentityInquiryRules(text string, scores map[string]float64) {
	textLower := strings.ToLower(text)

	// 1. 以"你"开头的问题，很可能是身份询问
	if strings.HasPrefix(textLower, "你") {
		scores["identity_inquiry"] += 0.8

		// 进一步细化判断
		if strings.HasPrefix(textLower, "你是") {
			scores["identity_inquiry"] += 1.0 // 非常高的权重
		}
		if strings.HasPrefix(textLower, "你叫") {
			scores["identity_inquiry"] += 1.0
		}
		if strings.HasPrefix(textLower, "你的") {
			scores["identity_inquiry"] += 0.6
		}
	}

	// 2. 包含身份相关核心词汇
	identityKeywords := []string{
		"身份", "名字", "叫什么", "是谁", "是什么",
		"机器人", "ai", "人工智能", "程序", "软件", "系统",
		"创造者", "开发者", "制作者", "来自", "背景",
		"介绍", "认识", "自我介绍",
	}

	for _, keyword := range identityKeywords {
		if strings.Contains(textLower, keyword) {
			scores["identity_inquiry"] += 0.5
		}
	}

	// 3. 特定的身份询问模式
	identityPatterns := []string{
		"谁创造了你", "谁开发了你", "你从哪来", "你来自哪里",
		"告诉我你是谁", "说说你是谁", "介绍一下自己",
		"你的真实身份", "你到底是谁", "你究竟是什么",
	}

	for _, pattern := range identityPatterns {
		if strings.Contains(textLower, pattern) {
			scores["identity_inquiry"] += 1.2 // 很高的权重
		}
	}

	// 4. 问句形式的身份询问
	if (strings.Contains(textLower, "你是") || strings.Contains(textLower, "你叫")) &&
		(strings.Contains(textLower, "吗") || strings.Contains(textLower, "？") || strings.Contains(textLower, "?")) {
		scores["identity_inquiry"] += 0.8
	}

	// 5. 排除技术问题的误判
	// 如果包含明显的技术词汇，降低身份询问的权重
	techKeywords := []string{
		"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置",
		"算法", "函数", "方法", "接口", "服务器", "网络", "协议",
	}

	hasTechKeyword := false
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			hasTechKeyword = true
			break
		}
	}

	if hasTechKeyword {
		scores["identity_inquiry"] *= 0.3 // 大幅降低权重
	}

	// 6. 长度过滤 - 身份询问通常比较简短
	if len(text) > 50 {
		scores["identity_inquiry"] *= 0.7 // 降低权重
	}

	// 7. 上下文相关性 - 如果文本很短且以"你"开头，很可能是身份询问
	if len(text) <= 10 && strings.HasPrefix(textLower, "你") {
		scores["identity_inquiry"] += 0.5
	}
}

// applyGreetingRules 应用问候语特殊规则
func (ic *IntentClassifier) applyGreetingRules(text string, scores map[string]float64) {
	textLower := strings.ToLower(text)

	// 1. 短文本更可能是问候语
	if len(text) <= 15 {
		scores["greeting"] += 0.3
	}

	// 2. 时间相关的问候语
	timeGreetings := []string{
		"早上好", "上午好", "中午好", "下午好", "晚上好", "晚安",
		"good morning", "good afternoon", "good evening", "good night",
		"早", "早啊", "安",
	}

	for _, greeting := range timeGreetings {
		if strings.Contains(textLower, greeting) {
			scores["greeting"] += 1.0 // 高权重
			break
		}
	}

	// 3. 群体问候语
	groupGreetings := []string{
		"大家好", "各位好", "朋友们好", "小伙伴们好", "同学们好", "老师好", "同事们好",
	}

	for _, greeting := range groupGreetings {
		if strings.Contains(textLower, greeting) {
			scores["greeting"] += 1.0
			break
		}
	}

	// 4. 关怀问候语
	careGreetings := []string{
		"注意身体", "保重", "多保重", "身体健康", "注意休息", "早点休息", "好好休息",
	}

	for _, greeting := range careGreetings {
		if strings.Contains(textLower, greeting) {
			scores["greeting"] += 0.8
			break
		}
	}

	// 5. 告别问候语
	farewellGreetings := []string{
		"再见", "拜拜", "bye", "goodbye", "see you", "回见", "一会见", "明天见", "下次见", "88",
	}

	for _, greeting := range farewellGreetings {
		if strings.Contains(textLower, greeting) {
			scores["greeting"] += 0.9
			break
		}
	}

	// 6. 包含感叹号表示热情
	if strings.Contains(text, "!") || strings.Contains(text, "！") {
		scores["greeting"] += 0.2
	}

	// 7. 表情符号增强问候语识别
	emojis := []string{"😊", "😄", "😃", "🙂", "👋", "🤝", "😁", "😆"}
	for _, emoji := range emojis {
		if strings.Contains(text, emoji) {
			scores["greeting"] += 0.4
			break
		}
	}

	// 8. 排除技术问题的误判
	techKeywords := []string{
		"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置",
		"算法", "函数", "方法", "接口", "服务器", "网络", "协议",
	}

	hasTechKeyword := false
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			hasTechKeyword = true
			break
		}
	}

	if hasTechKeyword {
		scores["greeting"] *= 0.2 // 大幅降低权重
	}

	// 9. 完全匹配基础问候语
	basicGreetings := []string{"你好", "您好", "hi", "hello", "嗨", "hey"}
	for _, greeting := range basicGreetings {
		if textLower == greeting || textLower == greeting+"!" || textLower == greeting+"！" {
			scores["greeting"] += 1.2 // 很高的权重
			break
		}
	}
}

// applyApologyRules 应用道歉/错误特殊规则
func (ic *IntentClassifier) applyApologyRules(text string, scores map[string]float64) {
	textLower := strings.ToLower(text)

	// 1. 短文本更可能是道歉
	if len(text) <= 15 {
		scores["apology"] += 0.2
		scores["error_acknowledgment"] += 0.2
	}

	// 2. 道歉相关词汇
	apologyWords := []string{
		"对不起", "抱歉", "不好意思", "sorry", "excuse me", "pardon",
		"真的对不起", "非常抱歉", "十分抱歉", "很抱歉", "实在抱歉",
		"道歉", "请原谅", "原谅我", "谅解", "宽恕",
	}

	for _, word := range apologyWords {
		if strings.Contains(textLower, word) {
			scores["apology"] += 1.0 // 高权重
			break
		}
	}

	// 3. 错误承认相关词汇
	errorWords := []string{
		"我错了", "是我错了", "我的错", "我弄错了", "我搞错了",
		"我理解错了", "我想错了", "我说错了", "我做错了", "我失误了",
	}

	for _, word := range errorWords {
		if strings.Contains(textLower, word) {
			scores["error_acknowledgment"] += 1.0
			break
		}
	}

	// 4. 指出他人错误的词汇
	otherErrorWords := []string{
		"你错了", "你弄错了", "你搞错了", "你理解错了",
		"这是错的", "这不对", "这有问题", "不是这样的",
	}

	for _, word := range otherErrorWords {
		if strings.Contains(textLower, word) {
			scores["error_acknowledgment"] += 0.8
			break
		}
	}

	// 5. 一般错误词汇
	generalErrorWords := []string{
		"都错了", "全错了", "错了", "有错", "出错了", "不对",
		"有误", "不正确", "不准确", "存在错误",
	}

	for _, word := range generalErrorWords {
		if strings.Contains(textLower, word) {
			scores["error_acknowledgment"] += 0.6
			break
		}
	}

	// 6. 包含感叹号表示强烈情感
	if strings.Contains(text, "!") || strings.Contains(text, "！") {
		scores["apology"] += 0.2
		scores["error_acknowledgment"] += 0.2
	}

	// 7. 强调词汇增强识别
	emphasisWords := []string{"真的", "非常", "十分", "实在", "深深", "万分"}
	for _, word := range emphasisWords {
		if strings.Contains(textLower, word) {
			scores["apology"] += 0.3
			scores["error_acknowledgment"] += 0.3
			break
		}
	}

	// 8. 表情符号增强识别
	apologeticEmojis := []string{"😔", "😞", "😢", "😭", "🙏", "😰", "😅", "😓"}
	for _, emoji := range apologeticEmojis {
		if strings.Contains(text, emoji) {
			scores["apology"] += 0.4
			scores["error_acknowledgment"] += 0.4
			break
		}
	}

	// 9. 排除技术问题的误判
	techKeywords := []string{
		"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置",
		"算法", "函数", "方法", "接口", "服务器", "网络", "协议",
	}

	hasTechKeyword := false
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			hasTechKeyword = true
			break
		}
	}

	if hasTechKeyword {
		scores["apology"] *= 0.2 // 大幅降低权重
		scores["error_acknowledgment"] *= 0.2
	}

	// 10. 完全匹配基础道歉词汇
	basicApologies := []string{"对不起", "抱歉", "不好意思", "sorry"}
	for _, apology := range basicApologies {
		if textLower == apology || textLower == apology+"!" || textLower == apology+"！" {
			scores["apology"] += 1.2 // 很高的权重
			break
		}
	}

	// 11. 完全匹配基础错误词汇
	basicErrors := []string{"我错了", "你错了", "都错了", "错了"}
	for _, error := range basicErrors {
		if textLower == error || textLower == error+"!" || textLower == error+"！" {
			scores["error_acknowledgment"] += 1.2 // 很高的权重
			break
		}
	}
}

// applyGratitudeRules 应用感谢特殊规则
func (ic *IntentClassifier) applyGratitudeRules(text string, scores map[string]float64) {
	textLower := strings.ToLower(text)

	// 1. 短文本更可能是感谢
	if len(text) <= 20 {
		scores["thanks"] += 0.3
	}

	// 2. 基础感谢词汇
	basicThanks := []string{
		"谢谢", "多谢", "感谢", "thank you", "thanks", "thx", "ty",
	}

	for _, word := range basicThanks {
		if strings.Contains(textLower, word) {
			scores["thanks"] += 1.0 // 高权重
			break
		}
	}

	// 3. 强调感谢词汇
	emphaticThanks := []string{
		"真的谢谢", "真的感谢", "非常感谢", "十分感谢", "万分感谢", "特别感谢",
		"太感谢了", "感谢不尽", "无比感谢", "深深感谢", "由衷感谢",
		"thank you so much", "thank you very much", "thanks a lot", "many thanks",
	}

	for _, word := range emphaticThanks {
		if strings.Contains(textLower, word) {
			scores["thanks"] += 1.3 // 很高权重
			break
		}
	}

	// 4. 正式感谢词汇
	formalThanks := []string{
		"感谢您", "谢谢您", "多谢您", "致谢", "表示感谢", "深表感谢",
		"衷心感谢", "诚挚感谢", "真诚感谢",
	}

	for _, word := range formalThanks {
		if strings.Contains(textLower, word) {
			scores["thanks"] += 1.1
			break
		}
	}

	// 5. 随意感谢词汇
	casualThanks := []string{
		"谢了", "谢啦", "3q", "3Q", "tks",
	}

	for _, word := range casualThanks {
		if strings.Contains(textLower, word) {
			scores["thanks"] += 0.9
			break
		}
	}

	// 6. 具体感谢对象
	specificObjects := []string{
		"帮助", "回答", "解答", "指导", "支持", "服务", "建议", "意见",
	}

	for _, obj := range specificObjects {
		if strings.Contains(textLower, obj) {
			scores["thanks"] += 0.4
			break
		}
	}

	// 7. 包含感叹号表示热情
	if strings.Contains(text, "!") || strings.Contains(text, "！") {
		scores["thanks"] += 0.3
	}

	// 8. 强调词汇增强识别
	emphasisWords := []string{"真的", "非常", "十分", "万分", "特别", "太", "深深", "由衷"}
	for _, word := range emphasisWords {
		if strings.Contains(textLower, word) {
			scores["thanks"] += 0.4
			break
		}
	}

	// 9. 表情符号增强识别
	gratefulEmojis := []string{"😊", "😄", "😃", "🙂", "🙏", "❤️", "💖", "👍", "🎉", "😘"}
	for _, emoji := range gratefulEmojis {
		if strings.Contains(text, emoji) {
			scores["thanks"] += 0.4
			break
		}
	}

	// 10. 重复感谢词表示强烈感谢
	thankWords := []string{"谢谢", "感谢", "thanks", "thank"}
	for _, word := range thankWords {
		count := strings.Count(textLower, word)
		if count > 1 {
			scores["thanks"] += 0.3 * float64(count-1)
			break
		}
	}

	// 11. 排除技术问题的误判
	techKeywords := []string{
		"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置",
		"算法", "函数", "方法", "接口", "服务器", "网络", "协议",
	}

	hasTechKeyword := false
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			hasTechKeyword = true
			break
		}
	}

	if hasTechKeyword {
		scores["thanks"] *= 0.3 // 降低权重
	}

	// 12. 完全匹配基础感谢词汇
	basicThanksList := []string{"谢谢", "多谢", "感谢", "thank you", "thanks"}
	for _, thanks := range basicThanksList {
		if textLower == thanks || textLower == thanks+"!" || textLower == thanks+"！" {
			scores["thanks"] += 1.2 // 很高的权重
			break
		}
	}
}
