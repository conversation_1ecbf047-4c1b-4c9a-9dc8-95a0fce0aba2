package nlp

import (
	"log"
	"regexp"
	"strings"
)

// ApologyRecognizer 道歉/错误识别器 - 专门用于识别道歉、错误承认等语句
type ApologyRecognizer struct {
	patterns    map[string][]*regexp.Regexp
	keywords    map[string][]string
	initialized bool
}

// ApologyResult 道歉/错误识别结果
type ApologyResult struct {
	IsApologyOrError bool                   `json:"is_apology_or_error"`
	Type             string                 `json:"type"`             // apology, self_error, other_error, general_error
	Severity         string                 `json:"severity"`         // mild, moderate, strong
	Target           string                 `json:"target"`           // self, other, general
	Confidence       float64                `json:"confidence"`
	MatchedPatterns  []string               `json:"matched_patterns"`
	MatchedKeywords  []string               `json:"matched_keywords"`
	Attributes       map[string]interface{} `json:"attributes"`
	Metadata         map[string]interface{} `json:"metadata"`
}

// NewApologyRecognizer 创建道歉/错误识别器
func NewApologyRecognizer() *ApologyRecognizer {
	ar := &ApologyRecognizer{
		patterns: make(map[string][]*regexp.Regexp),
		keywords: make(map[string][]string),
	}
	
	ar.initializePatterns()
	ar.initializeKeywords()
	ar.initialized = true
	
	log.Printf("✅ 道歉/错误识别器初始化完成")
	return ar
}

// initializePatterns 初始化模式
func (ar *ApologyRecognizer) initializePatterns() {
	// 道歉类
	ar.addPattern("apology", `^(对不起|抱歉|不好意思|sorry|excuse me|pardon)$`)
	ar.addPattern("apology", `^(对不起|抱歉|不好意思|sorry|excuse me|pardon)[！!。.]*$`)
	ar.addPattern("apology", `^(真的对不起|非常抱歉|十分抱歉|很抱歉|实在抱歉)$`)
	ar.addPattern("apology", `^(真的对不起|非常抱歉|十分抱歉|很抱歉|实在抱歉)[！!。.]*$`)
	ar.addPattern("apology", `^(我道歉|向你道歉|给你道歉|我向你道歉)$`)
	
	// 自我错误承认类
	ar.addPattern("self_error", `^(我错了|是我错了|我的错|我弄错了|我搞错了)$`)
	ar.addPattern("self_error", `^(我错了|是我错了|我的错|我弄错了|我搞错了)[！!。.]*$`)
	ar.addPattern("self_error", `^(我理解错了|我想错了|我说错了|我做错了|我判断错了)$`)
	ar.addPattern("self_error", `^(我不应该|我不该|我失误了|我疏忽了|我忽略了)$`)
	
	// 指出他人错误类
	ar.addPattern("other_error", `^(你错了|你弄错了|你搞错了|你理解错了|你想错了)$`)
	ar.addPattern("other_error", `^(你错了|你弄错了|你搞错了|你理解错了|你想错了)[！!。.]*$`)
	ar.addPattern("other_error", `^(这是错的|这不对|这有问题|这不正确|这不准确)$`)
	ar.addPattern("other_error", `^(不是这样的|不是这个意思|你误解了|你理解有误)$`)
	
	// 一般错误类
	ar.addPattern("general_error", `^(都错了|全错了|错了|有错|出错了|不对)$`)
	ar.addPattern("general_error", `^(都错了|全错了|错了|有错|出错了|不对)[！!。.]*$`)
	ar.addPattern("general_error", `^(这里有问题|存在错误|有误|不准确|不正确)$`)
	
	// 请求原谅类
	ar.addPattern("forgiveness", `^(请原谅|原谅我|饶恕我|宽恕我|请谅解|请理解)$`)
	ar.addPattern("forgiveness", `^(请原谅|原谅我|饶恕我|宽恕我|请谅解|请理解)[！!。.]*$`)
	
	// 承担责任类
	ar.addPattern("responsibility", `^(是我的责任|我负责|我承担|我来负责|责任在我)$`)
	ar.addPattern("responsibility", `^(是我的责任|我负责|我承担|我来负责|责任在我)[！!。.]*$`)
}

// initializeKeywords 初始化关键词
func (ar *ApologyRecognizer) initializeKeywords() {
	ar.keywords["apology"] = []string{
		"对不起", "抱歉", "不好意思", "sorry", "excuse me", "pardon",
		"真的对不起", "非常抱歉", "十分抱歉", "很抱歉", "实在抱歉",
		"道歉", "向你道歉", "给你道歉",
	}
	
	ar.keywords["self_error"] = []string{
		"我错了", "是我错了", "我的错", "我弄错了", "我搞错了",
		"我理解错了", "我想错了", "我说错了", "我做错了", "我判断错了",
		"我不应该", "我不该", "我失误了", "我疏忽了", "我忽略了",
	}
	
	ar.keywords["other_error"] = []string{
		"你错了", "你弄错了", "你搞错了", "你理解错了", "你想错了",
		"这是错的", "这不对", "这有问题", "这不正确", "这不准确",
		"不是这样的", "不是这个意思", "你误解了", "你理解有误",
	}
	
	ar.keywords["general_error"] = []string{
		"都错了", "全错了", "错了", "有错", "出错了", "不对",
		"这里有问题", "存在错误", "有误", "不准确", "不正确",
	}
	
	ar.keywords["forgiveness"] = []string{
		"请原谅", "原谅我", "饶恕我", "宽恕我", "请谅解", "请理解",
		"原谅", "谅解", "理解", "宽恕", "饶恕",
	}
	
	ar.keywords["responsibility"] = []string{
		"是我的责任", "我负责", "我承担", "我来负责", "责任在我",
		"责任", "负责", "承担", "担当",
	}
}

// addPattern 添加模式
func (ar *ApologyRecognizer) addPattern(category string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 道歉/错误识别器编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	ar.patterns[category] = append(ar.patterns[category], regex)
}

// RecognizeApology 识别道歉/错误
func (ar *ApologyRecognizer) RecognizeApology(text string) ApologyResult {
	if !ar.initialized {
		return ApologyResult{
			IsApologyOrError: false,
			Confidence:       0.0,
			Attributes:       make(map[string]interface{}),
			Metadata:         make(map[string]interface{}),
		}
	}
	
	textLower := strings.ToLower(strings.TrimSpace(text))
	textOriginal := strings.TrimSpace(text)
	
	result := ApologyResult{
		MatchedPatterns: []string{},
		MatchedKeywords: []string{},
		Attributes:      make(map[string]interface{}),
		Metadata: map[string]interface{}{
			"original_text": text,
			"text_length":   len(text),
			"word_count":    len(strings.Fields(text)),
		},
	}
	
	// 1. 模式匹配
	patternScore := ar.matchPatterns(textLower, &result)
	
	// 2. 关键词匹配
	keywordScore := ar.matchKeywords(textLower, &result)
	
	// 3. 特殊规则
	specialScore := ar.applySpecialRules(textOriginal, textLower, &result)
	
	// 4. 计算综合置信度
	totalScore := patternScore + keywordScore + specialScore
	result.Confidence = ar.normalizeScore(totalScore)
	
	// 5. 判断是否为道歉/错误
	result.IsApologyOrError = result.Confidence >= 0.5
	
	// 6. 确定类型、严重程度和目标
	if result.IsApologyOrError {
		result.Type = ar.determineType(&result)
		result.Severity = ar.determineSeverity(textOriginal, &result)
		result.Target = ar.determineTarget(textOriginal, &result)
	}
	
	log.Printf("🙏 道歉/错误识别: %s -> %t (%.2f) [%s/%s/%s]", 
		text, result.IsApologyOrError, result.Confidence, result.Type, result.Severity, result.Target)
	
	return result
}

// matchPatterns 模式匹配
func (ar *ApologyRecognizer) matchPatterns(text string, result *ApologyResult) float64 {
	score := 0.0
	
	for category, patterns := range ar.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				score += ar.getPatternWeight(category)
				result.MatchedPatterns = append(result.MatchedPatterns, category)
				result.Attributes[category+"_matched"] = true
			}
		}
	}
	
	return score
}

// matchKeywords 关键词匹配
func (ar *ApologyRecognizer) matchKeywords(text string, result *ApologyResult) float64 {
	score := 0.0
	
	for category, keywords := range ar.keywords {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				score += ar.getKeywordWeight(category)
				result.MatchedKeywords = append(result.MatchedKeywords, keyword)
			}
		}
	}
	
	return score
}

// applySpecialRules 应用特殊规则
func (ar *ApologyRecognizer) applySpecialRules(textOriginal, textLower string, result *ApologyResult) float64 {
	score := 0.0
	
	// 1. 短文本更可能是道歉
	if len(textOriginal) <= 15 {
		score += 0.3
		result.Attributes["short_text"] = true
	}
	
	// 2. 包含感叹号表示强烈情感
	if strings.Contains(textOriginal, "!") || strings.Contains(textOriginal, "！") {
		score += 0.2
		result.Attributes["emphatic"] = true
	}
	
	// 3. 重复词汇表示强调
	if strings.Contains(textLower, "真的") || strings.Contains(textLower, "非常") || strings.Contains(textLower, "十分") {
		score += 0.3
		result.Attributes["emphasized"] = true
	}
	
	// 4. 表情符号增强识别
	apologeticEmojis := []string{"😔", "😞", "😢", "😭", "🙏", "😰", "😅", "😓"}
	for _, emoji := range apologeticEmojis {
		if strings.Contains(textOriginal, emoji) {
			score += 0.4
			result.Attributes["has_emoji"] = true
			break
		}
	}
	
	// 5. 包含"我"字的自我相关表达
	if strings.Contains(textLower, "我") {
		score += 0.2
		result.Attributes["self_reference"] = true
	}
	
	// 6. 包含"你"字的他人相关表达
	if strings.Contains(textLower, "你") {
		score += 0.1
		result.Attributes["other_reference"] = true
	}
	
	// 7. 排除技术问题的误判
	techKeywords := []string{"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置", "算法", "函数"}
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			score *= 0.2 // 大幅降低分数
			result.Attributes["has_tech_keyword"] = true
			break
		}
	}
	
	return score
}

// getPatternWeight 获取模式权重
func (ar *ApologyRecognizer) getPatternWeight(category string) float64 {
	weights := map[string]float64{
		"apology":        1.2,
		"self_error":     1.0,
		"other_error":    0.9,
		"general_error":  0.8,
		"forgiveness":    1.1,
		"responsibility": 1.0,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.5
}

// getKeywordWeight 获取关键词权重
func (ar *ApologyRecognizer) getKeywordWeight(category string) float64 {
	weights := map[string]float64{
		"apology":        0.5,
		"self_error":     0.4,
		"other_error":    0.3,
		"general_error":  0.3,
		"forgiveness":    0.4,
		"responsibility": 0.4,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.2
}

// normalizeScore 标准化分数
func (ar *ApologyRecognizer) normalizeScore(score float64) float64 {
	if score <= 0 {
		return 0.0
	}
	normalized := 1.0 / (1.0 + 1.0/score)
	if normalized > 1.0 {
		normalized = 1.0
	}
	return normalized
}

// determineType 确定类型
func (ar *ApologyRecognizer) determineType(result *ApologyResult) string {
	// 根据匹配的模式确定类型
	for _, pattern := range result.MatchedPatterns {
		if pattern != "general_error" {
			return pattern
		}
	}
	
	// 根据关键词确定类型
	if ar.containsKeywords(result.MatchedKeywords, ar.keywords["apology"]) {
		return "apology"
	}
	if ar.containsKeywords(result.MatchedKeywords, ar.keywords["self_error"]) {
		return "self_error"
	}
	if ar.containsKeywords(result.MatchedKeywords, ar.keywords["other_error"]) {
		return "other_error"
	}
	if ar.containsKeywords(result.MatchedKeywords, ar.keywords["forgiveness"]) {
		return "forgiveness"
	}
	if ar.containsKeywords(result.MatchedKeywords, ar.keywords["responsibility"]) {
		return "responsibility"
	}
	
	return "general_error"
}

// determineSeverity 确定严重程度
func (ar *ApologyRecognizer) determineSeverity(text string, result *ApologyResult) string {
	// 强烈程度的词汇
	strongWords := []string{"非常", "十分", "真的", "实在", "深深", "万分"}
	for _, word := range strongWords {
		if strings.Contains(text, word) {
			return "strong"
		}
	}
	
	// 包含感叹号或表情符号
	if result.Attributes["emphatic"] == true || result.Attributes["has_emoji"] == true {
		return "moderate"
	}
	
	return "mild"
}

// determineTarget 确定目标
func (ar *ApologyRecognizer) determineTarget(text string, result *ApologyResult) string {
	if result.Attributes["self_reference"] == true {
		return "self"
	}
	if result.Attributes["other_reference"] == true {
		return "other"
	}
	return "general"
}

// containsKeywords 检查是否包含特定类别的关键词
func (ar *ApologyRecognizer) containsKeywords(matched []string, categoryKeywords []string) bool {
	for _, matched := range matched {
		for _, keyword := range categoryKeywords {
			if matched == keyword {
				return true
			}
		}
	}
	return false
}

// IsInitialized 检查是否已初始化
func (ar *ApologyRecognizer) IsInitialized() bool {
	return ar.initialized
}
