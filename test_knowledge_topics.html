<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试知识点标志功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea, .form-group select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .knowledge-item {
            background: #f8f9fa;
            padding: 10px;
            margin: 5px 0;
            border-radius: 4px;
            border-left: 3px solid #007bff;
        }
        .related-item {
            background: #fff3cd;
            padding: 8px;
            margin: 3px 0;
            border-radius: 4px;
            border-left: 3px solid #ffc107;
            font-size: 0.9em;
        }
        .topic-badge {
            background: #007bff;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: 10px;
        }
    </style>
</head>
<body>
    <h1>🏷️ 知识点标志功能测试</h1>
    
    <div class="test-section">
        <h2>📝 创建带知识点标志的知识</h2>
        <div class="form-group">
            <label for="testQuestion">问题:</label>
            <input type="text" id="testQuestion" value="什么是React Hooks？" placeholder="输入问题">
        </div>
        <div class="form-group">
            <label for="testAnswer">答案:</label>
            <textarea id="testAnswer" placeholder="输入答案">React Hooks是React 16.8引入的新特性，允许在函数组件中使用state和其他React特性。常用的Hooks包括useState、useEffect、useContext等。</textarea>
        </div>
        <div class="form-group">
            <label for="testTopic">知识点标志:</label>
            <select id="testTopic">
                <option value="">自动检测</option>
                <option value="React框架">React框架</option>
                <option value="Vue.js框架">Vue.js框架</option>
                <option value="JavaScript基础">JavaScript基础</option>
                <option value="Node.js">Node.js</option>
                <option value="Python基础">Python基础</option>
            </select>
        </div>
        <button class="test-button" onclick="createKnowledgeWithTopic()">创建知识</button>
        <div id="create-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 查看知识点列表</h2>
        <button class="test-button" onclick="getKnowledgeTopics()">获取所有知识点</button>
        <div id="topics-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔗 测试相关知识推荐</h2>
        <div class="form-group">
            <label for="knowledgeId">知识ID:</label>
            <input type="number" id="knowledgeId" placeholder="输入知识ID">
        </div>
        <button class="test-button" onclick="getRelatedKnowledge()">获取相关知识</button>
        <div id="related-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 按知识点分组显示</h2>
        <button class="test-button" onclick="getKnowledgeByTopics()">按知识点分组显示</button>
        <div id="grouped-result" class="result"></div>
    </div>

    <script>
        const baseURL = 'http://localhost:8082';
        let lastCreatedId = null;

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 创建带知识点标志的知识
        async function createKnowledgeWithTopic() {
            const resultDiv = 'create-result';
            const question = document.getElementById('testQuestion').value.trim();
            const answer = document.getElementById('testAnswer').value.trim();
            const topic = document.getElementById('testTopic').value.trim();
            
            if (!question || !answer) {
                showResult(resultDiv, '❌ 请填写问题和答案', 'error');
                return;
            }
            
            showResult(resultDiv, '正在创建知识...', 'info');
            
            const testData = {
                question: question,
                answer: answer,
                category: "technology",
                keywords: ["react", "hooks", "函数组件"],
                confidence: 0.9,
                source: "user_input",
                learned_from: "topic_test",
                knowledge_topic: topic
            };

            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    lastCreatedId = result.data.id;
                    showResult(resultDiv, `✅ 创建成功！\nID: ${lastCreatedId}\n\n完整响应:\n${JSON.stringify(result, null, 2)}`, 'success');
                    
                    // 自动填充知识ID输入框
                    document.getElementById('knowledgeId').value = lastCreatedId;
                } else {
                    showResult(resultDiv, `❌ 创建失败：${result.message || '未知错误'}\n\n响应:\n${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 获取知识点列表
        async function getKnowledgeTopics() {
            const resultDiv = 'topics-result';
            showResult(resultDiv, '正在获取知识点列表...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/topics`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    let message = `✅ 获取成功！共 ${result.total} 个知识点\n\n`;
                    result.data.forEach((topic, index) => {
                        message += `${index + 1}. ${topic}\n`;
                    });
                    message += '\n完整响应:\n' + JSON.stringify(result, null, 2);
                    showResult(resultDiv, message, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 获取相关知识
        async function getRelatedKnowledge() {
            const resultDiv = 'related-result';
            const knowledgeId = document.getElementById('knowledgeId').value.trim();
            
            if (!knowledgeId) {
                showResult(resultDiv, '❌ 请输入知识ID', 'error');
                return;
            }
            
            showResult(resultDiv, `正在获取知识 ${knowledgeId} 的相关知识...`, 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge/${knowledgeId}/related?limit=10`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    let message = `✅ 获取成功！\n`;
                    message += `当前问题: ${result.current_question}\n`;
                    message += `知识点标志: ${result.knowledge_topic || '无'}\n`;
                    message += `相关知识数量: ${result.total}\n\n`;
                    
                    if (result.data && result.data.length > 0) {
                        message += '相关知识列表:\n';
                        result.data.forEach((item, index) => {
                            message += `${index + 1}. ID:${item.id} - ${item.question}\n`;
                            message += `   置信度: ${item.confidence || 0}\n`;
                            message += `   创建时间: ${item.created_at ? new Date(item.created_at).toLocaleString() : '未知'}\n\n`;
                        });
                    } else {
                        message += '没有找到相关知识\n\n';
                    }
                    
                    message += '完整响应:\n' + JSON.stringify(result, null, 2);
                    showResult(resultDiv, message, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 按知识点分组显示知识
        async function getKnowledgeByTopics() {
            const resultDiv = 'grouped-result';
            showResult(resultDiv, '正在获取知识列表并按知识点分组...', 'info');
            
            try {
                // 先获取所有知识
                const knowledgeResponse = await fetch(`${baseURL}/api/learning/knowledge?limit=50`);
                const knowledgeResult = await knowledgeResponse.json();
                
                if (!knowledgeResponse.ok || !knowledgeResult.success) {
                    showResult(resultDiv, `❌ 获取知识列表失败：${knowledgeResult.message || '未知错误'}`, 'error');
                    return;
                }

                // 按知识点分组
                const groupedKnowledge = {};
                knowledgeResult.data.forEach(item => {
                    const topic = item.knowledge_topic || '未分类';
                    if (!groupedKnowledge[topic]) {
                        groupedKnowledge[topic] = [];
                    }
                    groupedKnowledge[topic].push(item);
                });

                // 生成显示内容
                let message = `✅ 按知识点分组显示 (共 ${knowledgeResult.total} 条知识)\n\n`;
                
                Object.keys(groupedKnowledge).sort().forEach(topic => {
                    const items = groupedKnowledge[topic];
                    message += `📚 ${topic} (${items.length} 条)\n`;
                    message += '─'.repeat(50) + '\n';
                    
                    items.forEach((item, index) => {
                        message += `  ${index + 1}. ID:${item.id} - ${item.question.substring(0, 50)}${item.question.length > 50 ? '...' : ''}\n`;
                        message += `     置信度: ${item.confidence || 0} | 状态: ${item.status}\n`;
                    });
                    message += '\n';
                });

                showResult(resultDiv, message, 'success');
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 页面加载时自动获取知识点列表
        window.addEventListener('load', function() {
            getKnowledgeTopics();
        });
    </script>
</body>
</html>
