package nlp

import (
	"log"
	"regexp"
	"strings"
)

// IdentityRecognizer 身份识别器 - 专门用于识别身份相关的询问
type IdentityRecognizer struct {
	patterns    map[string][]*regexp.Regexp
	keywords    map[string][]string
	initialized bool
}

// IdentityResult 身份识别结果
type IdentityResult struct {
	IsIdentityInquiry bool                   `json:"is_identity_inquiry"`
	InquiryType       string                 `json:"inquiry_type"`       // basic, detailed, technical, origin
	Confidence        float64                `json:"confidence"`
	MatchedPatterns   []string               `json:"matched_patterns"`
	MatchedKeywords   []string               `json:"matched_keywords"`
	Attributes        map[string]interface{} `json:"attributes"`
	Metadata          map[string]interface{} `json:"metadata"`
}

// NewIdentityRecognizer 创建身份识别器
func NewIdentityRecognizer() *IdentityRecognizer {
	ir := &IdentityRecognizer{
		patterns: make(map[string][]*regexp.Regexp),
		keywords: make(map[string][]string),
	}
	
	ir.initializePatterns()
	ir.initializeKeywords()
	ir.initialized = true
	
	log.Printf("✅ 身份识别器初始化完成")
	return ir
}

// initializePatterns 初始化模式
func (ir *IdentityRecognizer) initializePatterns() {
	// 基础身份询问
	ir.addPattern("basic", `^你是谁$`)
	ir.addPattern("basic", `^你叫什么$`)
	ir.addPattern("basic", `^你的名字$`)
	ir.addPattern("basic", `^你是什么$`)
	ir.addPattern("basic", `^你是$`)
	ir.addPattern("basic", `^你$`)
	
	// 详细身份询问
	ir.addPattern("detailed", `^(介绍一下自己|自我介绍|说说你自己|你自己介绍一下)`)
	ir.addPattern("detailed", `^(告诉我你是谁|说说你是谁|你的真实身份|你的身份是什么)`)
	ir.addPattern("detailed", `^(认识一下|关于你|你的简介|你的资料|你的背景|你的来历|你的故事)`)
	
	// 技术身份询问
	ir.addPattern("technical", `^(你是机器人吗|你是AI吗|你是人工智能吗|你是程序吗|你是软件吗)`)
	ir.addPattern("technical", `^(你是什么系统|你是什么助手|你是什么机器人|你是真人吗)`)
	
	// 起源身份询问
	ir.addPattern("origin", `^(你从哪来|你来自哪里|谁创造了你|谁开发了你|你的创造者|你的开发者|你的制作者)`)
	
	// 模糊身份询问 - 以"你"开头的短问题
	ir.addPattern("ambiguous", `^你.{0,8}$`)  // 以"你"开头，总长度不超过10个字符
}

// initializeKeywords 初始化关键词
func (ir *IdentityRecognizer) initializeKeywords() {
	ir.keywords["identity"] = []string{
		"身份", "名字", "叫什么", "是谁", "是什么", "你是", "你叫", "你的",
	}
	
	ir.keywords["technical"] = []string{
		"机器人", "ai", "人工智能", "程序", "软件", "系统", "助手", "真人", "人类",
	}
	
	ir.keywords["origin"] = []string{
		"创造者", "开发者", "制作者", "来自", "哪来", "背景", "来历",
	}
	
	ir.keywords["introduction"] = []string{
		"介绍", "认识", "自我介绍", "简介", "资料", "故事",
	}
}

// addPattern 添加模式
func (ir *IdentityRecognizer) addPattern(category string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 身份识别器编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	ir.patterns[category] = append(ir.patterns[category], regex)
}

// RecognizeIdentity 识别身份询问
func (ir *IdentityRecognizer) RecognizeIdentity(text string) IdentityResult {
	if !ir.initialized {
		return IdentityResult{
			IsIdentityInquiry: false,
			Confidence:        0.0,
			Attributes:        make(map[string]interface{}),
			Metadata:          make(map[string]interface{}),
		}
	}
	
	textLower := strings.ToLower(strings.TrimSpace(text))
	
	result := IdentityResult{
		MatchedPatterns: []string{},
		MatchedKeywords: []string{},
		Attributes:      make(map[string]interface{}),
		Metadata: map[string]interface{}{
			"original_text": text,
			"text_length":   len(text),
			"word_count":    len(strings.Fields(text)),
		},
	}
	
	// 1. 模式匹配
	patternScore := ir.matchPatterns(textLower, &result)
	
	// 2. 关键词匹配
	keywordScore := ir.matchKeywords(textLower, &result)
	
	// 3. 特殊规则
	specialScore := ir.applySpecialRules(textLower, &result)
	
	// 4. 计算综合置信度
	totalScore := patternScore + keywordScore + specialScore
	result.Confidence = ir.normalizeScore(totalScore)
	
	// 5. 判断是否为身份询问
	result.IsIdentityInquiry = result.Confidence >= 0.5
	
	// 6. 确定询问类型
	if result.IsIdentityInquiry {
		result.InquiryType = ir.determineInquiryType(&result)
	}
	
	log.Printf("🔍 身份识别: %s -> %t (%.2f) [%s]", 
		text, result.IsIdentityInquiry, result.Confidence, result.InquiryType)
	
	return result
}

// matchPatterns 模式匹配
func (ir *IdentityRecognizer) matchPatterns(text string, result *IdentityResult) float64 {
	score := 0.0
	
	for category, patterns := range ir.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				score += ir.getPatternWeight(category)
				result.MatchedPatterns = append(result.MatchedPatterns, category)
				result.Attributes[category+"_matched"] = true
			}
		}
	}
	
	return score
}

// matchKeywords 关键词匹配
func (ir *IdentityRecognizer) matchKeywords(text string, result *IdentityResult) float64 {
	score := 0.0
	
	for category, keywords := range ir.keywords {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				score += ir.getKeywordWeight(category)
				result.MatchedKeywords = append(result.MatchedKeywords, keyword)
			}
		}
	}
	
	return score
}

// applySpecialRules 应用特殊规则
func (ir *IdentityRecognizer) applySpecialRules(text string, result *IdentityResult) float64 {
	score := 0.0
	
	// 1. 以"你"开头的短问题
	if strings.HasPrefix(text, "你") && len(text) <= 10 {
		score += 0.8
		result.Attributes["starts_with_you"] = true
	}
	
	// 2. 以"你是"开头
	if strings.HasPrefix(text, "你是") {
		score += 1.0
		result.Attributes["starts_with_you_are"] = true
	}
	
	// 3. 以"你叫"开头
	if strings.HasPrefix(text, "你叫") {
		score += 1.0
		result.Attributes["starts_with_you_called"] = true
	}
	
	// 4. 包含问号
	if strings.Contains(text, "？") || strings.Contains(text, "?") {
		score += 0.3
		result.Attributes["has_question_mark"] = true
	}
	
	// 5. 排除技术问题
	techKeywords := []string{"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置"}
	for _, keyword := range techKeywords {
		if strings.Contains(text, keyword) {
			score *= 0.2 // 大幅降低分数
			result.Attributes["has_tech_keyword"] = true
			break
		}
	}
	
	return score
}

// getPatternWeight 获取模式权重
func (ir *IdentityRecognizer) getPatternWeight(category string) float64 {
	weights := map[string]float64{
		"basic":     1.0,
		"detailed":  1.2,
		"technical": 1.1,
		"origin":    1.1,
		"ambiguous": 0.6,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.5
}

// getKeywordWeight 获取关键词权重
func (ir *IdentityRecognizer) getKeywordWeight(category string) float64 {
	weights := map[string]float64{
		"identity":     0.5,
		"technical":    0.4,
		"origin":       0.4,
		"introduction": 0.3,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.2
}

// normalizeScore 标准化分数
func (ir *IdentityRecognizer) normalizeScore(score float64) float64 {
	// 使用sigmoid函数进行标准化
	normalized := 1.0 / (1.0 + 1.0/score)
	if normalized > 1.0 {
		normalized = 1.0
	}
	if normalized < 0.0 {
		normalized = 0.0
	}
	return normalized
}

// determineInquiryType 确定询问类型
func (ir *IdentityRecognizer) determineInquiryType(result *IdentityResult) string {
	// 根据匹配的模式确定类型
	for _, pattern := range result.MatchedPatterns {
		if pattern != "ambiguous" {
			return pattern
		}
	}
	
	// 根据关键词确定类型
	if ir.containsKeywords(result.MatchedKeywords, ir.keywords["technical"]) {
		return "technical"
	}
	if ir.containsKeywords(result.MatchedKeywords, ir.keywords["origin"]) {
		return "origin"
	}
	if ir.containsKeywords(result.MatchedKeywords, ir.keywords["introduction"]) {
		return "detailed"
	}
	
	return "basic"
}

// containsKeywords 检查是否包含特定类别的关键词
func (ir *IdentityRecognizer) containsKeywords(matched []string, categoryKeywords []string) bool {
	for _, matched := range matched {
		for _, keyword := range categoryKeywords {
			if matched == keyword {
				return true
			}
		}
	}
	return false
}

// IsInitialized 检查是否已初始化
func (ir *IdentityRecognizer) IsInitialized() bool {
	return ir.initialized
}
