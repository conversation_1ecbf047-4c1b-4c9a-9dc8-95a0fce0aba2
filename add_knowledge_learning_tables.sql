-- 添加知识学习和向量存储表
USE faq_system;

-- 1. 学习知识表 - 存储从用户交互中学习到的知识
CREATE TABLE IF NOT EXISTS learned_knowledge (
    id INT AUTO_INCREMENT PRIMARY KEY,
    question TEXT NOT NULL COMMENT '学习到的问题',
    answer TEXT NOT NULL COMMENT '学习到的答案',
    source VARCHAR(100) NOT NULL DEFAULT 'conversation' COMMENT '知识来源：conversation, correction, successful_qa等',
    confidence FLOAT NOT NULL DEFAULT 0.0 COMMENT '知识置信度',
    category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
    keywords JSON COMMENT '关键词列表',
    context TEXT COMMENT '学习上下文',
    learned_from VARCHAR(255) COMMENT '从哪个用户或系统学习',
    status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '知识状态',
    metadata JSON COMMENT '元数据信息',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    INDEX idx_source (source),
    INDEX idx_confidence (confidence),
    INDEX idx_category (category),
    INDEX idx_status (status),
    INDEX idx_created_at (created_at),
    INDEX idx_learned_from (learned_from),
    FULLTEXT idx_question (question),
    FULLTEXT idx_answer (answer)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习知识表';

-- 2. 知识向量表 - 存储知识的向量表示
CREATE TABLE IF NOT EXISTS knowledge_vectors (
    id INT AUTO_INCREMENT PRIMARY KEY,
    knowledge_id INT NOT NULL COMMENT '关联的知识ID',
    vector_data JSON NOT NULL COMMENT '向量数据',
    vector_dimension INT DEFAULT 128 COMMENT '向量维度',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_knowledge (knowledge_id),
    INDEX idx_created_at (created_at),
    FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识向量表';

-- 3. 更新用户反馈表，添加更多反馈类型
ALTER TABLE user_feedback 
MODIFY COLUMN feedback_type ENUM('helpful', 'not_helpful', 'partially_helpful', 'positive', 'negative', 'correction') NOT NULL;

-- 4. 更新系统响应表，添加response_content字段（如果不存在）
ALTER TABLE system_responses 
ADD COLUMN IF NOT EXISTS response_content TEXT COMMENT '响应内容' AFTER response_text;

-- 如果response_content为空，从response_text复制数据
UPDATE system_responses 
SET response_content = response_text 
WHERE response_content IS NULL OR response_content = '';

-- 5. 创建知识学习统计视图
CREATE OR REPLACE VIEW knowledge_learning_stats AS
SELECT 
    DATE(created_at) as learning_date,
    source,
    category,
    status,
    COUNT(*) as knowledge_count,
    AVG(confidence) as avg_confidence,
    MIN(confidence) as min_confidence,
    MAX(confidence) as max_confidence
FROM learned_knowledge
GROUP BY DATE(created_at), source, category, status
ORDER BY learning_date DESC, knowledge_count DESC;

-- 6. 创建知识向量统计视图
CREATE OR REPLACE VIEW knowledge_vector_stats AS
SELECT 
    DATE(kv.created_at) as vector_date,
    lk.category,
    lk.source,
    COUNT(*) as vector_count,
    AVG(kv.vector_dimension) as avg_dimension,
    AVG(JSON_LENGTH(kv.vector_data)) as avg_vector_size
FROM knowledge_vectors kv
JOIN learned_knowledge lk ON kv.knowledge_id = lk.id
GROUP BY DATE(kv.created_at), lk.category, lk.source
ORDER BY vector_date DESC, vector_count DESC;

-- 7. 插入一些示例学习配置
INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES
('knowledge_learning_enabled', 'true', 'boolean', '是否启用知识学习功能'),
('min_knowledge_confidence', '0.3', 'number', '知识学习最小置信度'),
('max_knowledge_per_session', '50', 'number', '每次学习会话最大知识数量'),
('knowledge_auto_approve_threshold', '0.8', 'number', '知识自动批准阈值'),
('vector_generation_enabled', 'true', 'boolean', '是否启用向量生成'),
('vector_dimension', '128', 'number', '向量维度'),
('knowledge_deduplication_threshold', '0.85', 'number', '知识去重相似度阈值'),
('content_safety_check_enabled', 'true', 'boolean', '是否启用内容安全检查');

-- 8. 创建知识学习触发器 - 自动更新统计信息
DELIMITER //

CREATE TRIGGER IF NOT EXISTS after_knowledge_insert
AFTER INSERT ON learned_knowledge
FOR EACH ROW
BEGIN
    -- 更新学习统计
    INSERT INTO learning_patterns (pattern_name, pattern_type, pattern_data, confidence, usage_count)
    VALUES (
        CONCAT('knowledge_learning_', NEW.category),
        'knowledge_category',
        JSON_OBJECT('category', NEW.category, 'source', NEW.source),
        NEW.confidence,
        1
    )
    ON DUPLICATE KEY UPDATE
        confidence = (confidence * usage_count + NEW.confidence) / (usage_count + 1),
        usage_count = usage_count + 1,
        last_updated = CURRENT_TIMESTAMP;
END//

DELIMITER ;

-- 9. 创建存储过程 - 清理过期的学习数据
DELIMITER //

CREATE PROCEDURE IF NOT EXISTS CleanupLearningData(IN retention_days INT)
BEGIN
    DECLARE done INT DEFAULT FALSE;
    DECLARE cleanup_date DATE;
    
    SET cleanup_date = DATE_SUB(CURDATE(), INTERVAL retention_days DAY);
    
    -- 删除过期的被拒绝知识
    DELETE FROM learned_knowledge 
    WHERE status = 'rejected' AND created_at < cleanup_date;
    
    -- 删除过期的低置信度待审核知识
    DELETE FROM learned_knowledge 
    WHERE status = 'pending' AND confidence < 0.3 AND created_at < cleanup_date;
    
    -- 记录清理结果
    SELECT ROW_COUNT() as cleaned_records, cleanup_date as cleanup_before_date;
END//

DELIMITER ;

-- 10. 创建函数 - 计算知识相似度
DELIMITER //

CREATE FUNCTION IF NOT EXISTS CalculateKnowledgeSimilarity(
    question1 TEXT,
    question2 TEXT
) RETURNS FLOAT
READS SQL DATA
DETERMINISTIC
BEGIN
    DECLARE similarity FLOAT DEFAULT 0.0;
    DECLARE common_words INT DEFAULT 0;
    DECLARE total_words INT DEFAULT 0;
    
    -- 简单的词汇重叠相似度计算
    -- 实际应用中应该使用更复杂的算法
    SET total_words = (
        CHAR_LENGTH(question1) - CHAR_LENGTH(REPLACE(question1, ' ', '')) + 1 +
        CHAR_LENGTH(question2) - CHAR_LENGTH(REPLACE(question2, ' ', '')) + 1
    );
    
    -- 这里使用简化的相似度计算
    IF LOCATE(SUBSTRING(question1, 1, 10), question2) > 0 THEN
        SET similarity = 0.8;
    ELSEIF LOCATE(SUBSTRING(question1, 1, 5), question2) > 0 THEN
        SET similarity = 0.5;
    ELSE
        SET similarity = 0.1;
    END IF;
    
    RETURN similarity;
END//

DELIMITER ;

-- 提交所有更改
COMMIT;

-- 显示创建的表和对象
SELECT 'Knowledge learning tables created successfully!' as status;

SHOW TABLES LIKE '%knowledge%';

SELECT 
    TABLE_NAME,
    TABLE_COMMENT,
    TABLE_ROWS
FROM INFORMATION_SCHEMA.TABLES 
WHERE TABLE_SCHEMA = 'faq_system' 
AND TABLE_NAME IN ('learned_knowledge', 'knowledge_vectors')
ORDER BY TABLE_NAME;
