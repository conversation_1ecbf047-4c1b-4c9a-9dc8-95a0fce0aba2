package main

import (
	"fmt"
	"faq-system/internal/rag"
)

func main() {
	fmt.Println("🎯 测试意图分类器...")
	
	// 创建意图分类器
	intentClassifier := rag.NewIntentClassifier()
	
	// 测试用例
	testCases := []string{
		"1+1",
		"1+1等于多少",
		"计算1+1",
		"如何部署FAQ系统？",
		"你好",
		"什么是Go语言",
	}
	
	fmt.Println("\n=== 意图分类测试 ===")
	for _, testCase := range testCases {
		intent := intentClassifier.ClassifyIntent(testCase)
		fmt.Printf("输入: %s -> 意图: %s\n", testCase, intent)
		
		// 测试数学表达式检测
		hasMath := intentClassifier.ContainsMathExpression(testCase)
		fmt.Printf("  数学表达式检测: %v\n", hasMath)
		fmt.Println()
	}
}
