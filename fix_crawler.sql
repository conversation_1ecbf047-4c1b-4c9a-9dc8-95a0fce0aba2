-- 修复爬虫目标配置

-- 1. 更新百度爬虫目标为测试页面
UPDATE crawl_targets 
SET name = '测试页面-Example.com', 
    url = 'https://example.com',
    keywords = '["测试", "示例", "内容"]',
    selectors = '{"title": "title", "content": "body"}',
    category = 'test'
WHERE name LIKE '%百度%' OR url LIKE '%baidu.com%';

-- 2. 添加一个简单的测试目标
INSERT IGNORE INTO crawl_targets 
(name, url, type, category, keywords, selectors, schedule, enabled, created_at, updated_at)
VALUES 
('HTTPBin测试', 'https://httpbin.org/html', 'website', 'test', 
 '["测试", "HTML", "内容"]', 
 '{"title": "title", "content": "body"}', 
 '0 */1 * * *', true, NOW(), NOW());

-- 3. 查看当前所有目标
SELECT id, name, url, category, enabled FROM crawl_targets ORDER BY id;
