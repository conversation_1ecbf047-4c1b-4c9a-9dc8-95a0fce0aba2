<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>知识管理 - FAQ系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .main-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            padding: 30px;
        }

        .form-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
        }

        .form-section h2 {
            color: #495057;
            margin-bottom: 20px;
            font-size: 1.5em;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #495057;
        }

        .form-group input,
        .form-group textarea,
        .form-group select {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            transition: border-color 0.3s ease;
        }

        .form-group input:focus,
        .form-group textarea:focus,
        .form-group select:focus {
            outline: none;
            border-color: #4facfe;
            box-shadow: 0 0 0 3px rgba(79, 172, 254, 0.1);
        }

        .form-group textarea {
            min-height: 120px;
            resize: vertical;
        }

        .btn {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 12px 30px;
            border-radius: 8px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            width: 100%;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(79, 172, 254, 0.3);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .knowledge-list {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            border: 1px solid #e9ecef;
            max-height: 80vh;
            overflow-y: auto;
        }

        .knowledge-item {
            background: white;
            padding: 15px;
            margin-bottom: 15px;
            border-radius: 8px;
            border-left: 4px solid #4facfe;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            position: relative;
        }

        .knowledge-item h4 {
            color: #495057;
            margin-bottom: 8px;
            font-size: 1.1em;
            padding-right: 100px; /* 为操作按钮留出空间 */
        }

        .knowledge-item p {
            color: #6c757d;
            margin-bottom: 8px;
            line-height: 1.5;
        }

        .knowledge-meta {
            display: flex;
            justify-content: space-between;
            align-items: center;
            font-size: 0.9em;
            color: #868e96;
            margin-top: 10px;
        }

        .knowledge-actions {
            position: absolute;
            top: 15px;
            right: 15px;
            display: flex;
            gap: 5px;
        }

        .action-btn {
            padding: 4px 8px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
            transition: all 0.2s ease;
        }

        .edit-btn {
            background: #17a2b8;
            color: white;
        }

        .edit-btn:hover {
            background: #138496;
        }

        .delete-btn {
            background: #dc3545;
            color: white;
        }

        .delete-btn:hover {
            background: #c82333;
        }

        .view-btn {
            background: #28a745;
            color: white;
        }

        .view-btn:hover {
            background: #218838;
        }

        .status-badge {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 0.8em;
            font-weight: 600;
        }

        .status-approved {
            background: #d4edda;
            color: #155724;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .message {
            padding: 15px;
            margin: 20px 0;
            border-radius: 8px;
            font-weight: 500;
        }

        .message.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }

        .message.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }

        .loading {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .spinner {
            border: 3px solid #f3f3f3;
            border-top: 3px solid #4facfe;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 1s linear infinite;
            margin: 0 auto 10px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        .stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: white;
            padding: 20px;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 4px 6px rgba(0,0,0,0.1);
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #4facfe;
        }

        .stat-label {
            color: #6c757d;
            margin-top: 5px;
        }

        /* 模态框样式 */
        .modal {
            display: none;
            position: fixed;
            z-index: 1000;
            left: 0;
            top: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0,0,0,0.5);
        }

        .modal-content {
            background-color: white;
            margin: 5% auto;
            padding: 30px;
            border-radius: 15px;
            width: 80%;
            max-width: 600px;
            max-height: 80vh;
            overflow-y: auto;
            box-shadow: 0 20px 40px rgba(0,0,0,0.3);
        }

        .modal-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
            padding-bottom: 15px;
            border-bottom: 2px solid #e9ecef;
        }

        .modal-header h2 {
            color: #495057;
            margin: 0;
        }

        .close {
            color: #aaa;
            font-size: 28px;
            font-weight: bold;
            cursor: pointer;
            transition: color 0.2s ease;
        }

        .close:hover {
            color: #000;
        }

        .modal-body {
            margin-bottom: 20px;
        }

        .modal-footer {
            display: flex;
            justify-content: flex-end;
            gap: 10px;
            padding-top: 15px;
            border-top: 1px solid #e9ecef;
        }

        .btn-secondary {
            background: #6c757d;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: background 0.2s ease;
        }

        .btn-secondary:hover {
            background: #5a6268;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            transition: transform 0.2s ease;
        }

        .btn-primary:hover {
            transform: translateY(-1px);
        }

        .detail-item {
            margin-bottom: 15px;
        }

        .detail-label {
            font-weight: 600;
            color: #495057;
            margin-bottom: 5px;
        }

        .detail-value {
            color: #6c757d;
            line-height: 1.5;
            background: #f8f9fa;
            padding: 10px;
            border-radius: 6px;
            border: 1px solid #e9ecef;
        }

        @media (max-width: 768px) {
            .main-content {
                grid-template-columns: 1fr;
            }

            .header h1 {
                font-size: 2em;
            }

            .modal-content {
                width: 95%;
                margin: 10% auto;
                padding: 20px;
            }

            .knowledge-item h4 {
                padding-right: 80px;
            }

            .knowledge-actions {
                flex-direction: column;
                gap: 3px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 知识管理中心</h1>
            <p>添加新知识并自动生成向量，提升FAQ系统的智能程度</p>
        </div>

        <div class="stats">
            <div class="stat-card">
                <div class="stat-number" id="totalKnowledge">-</div>
                <div class="stat-label">总知识数</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="vectorizedKnowledge">-</div>
                <div class="stat-label">已向量化</div>
            </div>
            <div class="stat-card">
                <div class="stat-number" id="approvedKnowledge">-</div>
                <div class="stat-label">已批准</div>
            </div>
        </div>

        <div class="main-content">
            <div class="form-section">
                <h2>📝 添加新知识</h2>
                
                <form id="knowledgeForm">
                    <div class="form-group">
                        <label for="question">问题 *</label>
                        <input type="text" id="question" name="question" required 
                               placeholder="请输入问题，例如：什么是机器学习？">
                    </div>

                    <div class="form-group">
                        <label for="answer">答案 *</label>
                        <textarea id="answer" name="answer" required 
                                  placeholder="请输入详细的答案..."></textarea>
                    </div>

                    <div class="form-group">
                        <label for="category">分类</label>
                        <select id="category" name="category">
                            <option value="general">通用</option>
                            <option value="technology">技术</option>
                            <option value="business">商业</option>
                            <option value="science">科学</option>
                            <option value="education">教育</option>
                            <option value="health">健康</option>
                            <option value="other">其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="knowledgeTopic">知识点标志</label>
                        <input type="text" id="knowledgeTopic" name="knowledge_topic"
                               placeholder="例如：JavaScript基础、React框架、Vue.js框架等">
                        <small>相同知识点标志的问题会被关联在一起，便于推荐相关问题</small>
                    </div>

                    <div class="form-group">
                        <label for="keywords">关键词</label>
                        <input type="text" id="keywords" name="keywords" 
                               placeholder="用逗号分隔，例如：机器学习,AI,算法">
                    </div>

                    <div class="form-group">
                        <label for="confidence">置信度</label>
                        <input type="range" id="confidence" name="confidence" 
                               min="0" max="1" step="0.1" value="0.8">
                        <span id="confidenceValue">0.8</span>
                    </div>

                    <button type="submit" class="btn" id="submitBtn">
                        💾 保存知识并生成向量
                    </button>
                </form>

                <div class="loading" id="loading">
                    <div class="spinner"></div>
                    <p>正在保存知识并生成向量...</p>
                </div>

                <div id="message"></div>
            </div>

            <div class="knowledge-list">
                <h2>📚 最近添加的知识</h2>
                <div id="knowledgeList">
                    <p style="text-align: center; color: #6c757d; padding: 20px;">
                        正在加载知识列表...
                    </p>
                </div>
            </div>
        </div>
    </div>

    <!-- 查看知识详情模态框 -->
    <div id="viewModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>📖 知识详情</h2>
                <span class="close" onclick="closeModal('viewModal')">&times;</span>
            </div>
            <div class="modal-body" id="viewModalBody">
                <!-- 详情内容将在这里动态加载 -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('viewModal')">关闭</button>
            </div>
        </div>
    </div>

    <!-- 编辑知识模态框 -->
    <div id="editModal" class="modal">
        <div class="modal-content">
            <div class="modal-header">
                <h2>✏️ 编辑知识</h2>
                <span class="close" onclick="closeModal('editModal')">&times;</span>
            </div>
            <div class="modal-body">
                <form id="editKnowledgeForm">
                    <input type="hidden" id="editKnowledgeId">

                    <div class="form-group">
                        <label for="editQuestion">问题 *</label>
                        <input type="text" id="editQuestion" name="question" required>
                    </div>

                    <div class="form-group">
                        <label for="editAnswer">答案 *</label>
                        <textarea id="editAnswer" name="answer" required></textarea>
                    </div>

                    <div class="form-group">
                        <label for="editCategory">分类</label>
                        <select id="editCategory" name="category">
                            <option value="general">通用</option>
                            <option value="technology">技术</option>
                            <option value="business">商业</option>
                            <option value="science">科学</option>
                            <option value="education">教育</option>
                            <option value="health">健康</option>
                            <option value="other">其他</option>
                        </select>
                    </div>

                    <div class="form-group">
                        <label for="editKnowledgeTopic">知识点标志</label>
                        <input type="text" id="editKnowledgeTopic" name="knowledge_topic"
                               placeholder="例如：JavaScript基础、React框架、Vue.js框架等">
                        <small>相同知识点标志的问题会被关联在一起，便于推荐相关问题</small>
                    </div>

                    <div class="form-group">
                        <label for="editKeywords">关键词</label>
                        <input type="text" id="editKeywords" name="keywords"
                               placeholder="用逗号分隔，例如：机器学习,AI,算法">
                    </div>

                    <div class="form-group">
                        <label for="editConfidence">置信度</label>
                        <input type="range" id="editConfidence" name="confidence"
                               min="0" max="1" step="0.1" value="0.8">
                        <span id="editConfidenceValue">0.8</span>
                    </div>
                </form>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn-secondary" onclick="closeModal('editModal')">取消</button>
                <button type="button" class="btn-primary" onclick="saveKnowledge()">保存</button>
            </div>
        </div>
    </div>

    <script>
        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            loadStatistics();
            loadKnowledgeList();
            setupEventListeners();
        });

        // 设置事件监听器
        function setupEventListeners() {
            // 表单提交
            document.getElementById('knowledgeForm').addEventListener('submit', handleSubmit);

            // 置信度滑块
            document.getElementById('confidence').addEventListener('input', function() {
                document.getElementById('confidenceValue').textContent = this.value;
            });

            // 编辑表单置信度滑块
            document.getElementById('editConfidence').addEventListener('input', function() {
                document.getElementById('editConfidenceValue').textContent = this.value;
            });

            // 模态框点击外部关闭
            window.addEventListener('click', function(event) {
                if (event.target.classList.contains('modal')) {
                    event.target.style.display = 'none';
                }
            });
        }

        // 处理表单提交
        async function handleSubmit(e) {
            e.preventDefault();
            
            const submitBtn = document.getElementById('submitBtn');
            const loading = document.getElementById('loading');
            const messageDiv = document.getElementById('message');
            
            // 获取表单数据
            const formData = {
                question: document.getElementById('question').value.trim(),
                answer: document.getElementById('answer').value.trim(),
                category: document.getElementById('category').value,
                keywords: document.getElementById('keywords').value.split(',').map(k => k.trim()).filter(k => k),
                confidence: parseFloat(document.getElementById('confidence').value),
                source: 'user_input',
                learned_from: 'knowledge_manager',
                knowledge_topic: document.getElementById('knowledgeTopic').value.trim()
            };

            // 验证数据
            if (!formData.question || !formData.answer) {
                showMessage('请填写问题和答案', 'error');
                return;
            }

            // 显示加载状态
            submitBtn.disabled = true;
            loading.style.display = 'block';
            messageDiv.innerHTML = '';

            try {
                const response = await fetch('/api/learning/knowledge', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showMessage('✅ 知识保存成功！向量已自动生成', 'success');
                    document.getElementById('knowledgeForm').reset();
                    document.getElementById('confidenceValue').textContent = '0.8';
                    
                    // 刷新列表和统计
                    loadKnowledgeList();
                    loadStatistics();
                } else {
                    showMessage('❌ 保存失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            } finally {
                submitBtn.disabled = false;
                loading.style.display = 'none';
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/learning/statistics');
                const data = await response.json();
                
                if (data.success) {
                    document.getElementById('totalKnowledge').textContent = data.data.total || 0;
                    document.getElementById('vectorizedKnowledge').textContent = data.data.vectorized || 0;
                    document.getElementById('approvedKnowledge').textContent = data.data.approved || 0;
                }
            } catch (error) {
                console.error('Failed to load statistics:', error);
            }
        }

        // 加载知识列表
        async function loadKnowledgeList() {
            try {
                const response = await fetch('/api/learning/knowledge?limit=10');
                const data = await response.json();
                
                const listContainer = document.getElementById('knowledgeList');
                
                if (data.success && data.data && data.data.length > 0) {
                    listContainer.innerHTML = data.data.map(item => `
                        <div class="knowledge-item">
                            <div class="knowledge-actions">
                                <button class="action-btn view-btn" onclick="viewKnowledge(${item.id})" title="查看详情">
                                    👁️
                                </button>
                                <button class="action-btn edit-btn" onclick="editKnowledge(${item.id})" title="编辑">
                                    ✏️
                                </button>
                                <button class="action-btn delete-btn" onclick="deleteKnowledge(${item.id})" title="删除">
                                    🗑️
                                </button>
                            </div>
                            <h4>${escapeHtml(item.question)}</h4>
                            <p>${escapeHtml(item.answer.substring(0, 150))}${item.answer.length > 150 ? '...' : ''}</p>
                            <div class="knowledge-meta">
                                <span>分类: ${item.category || '未分类'}</span>
                                ${item.knowledge_topic ? `<span>知识点: ${item.knowledge_topic}</span>` : ''}
                                <span class="status-badge ${item.status === 'approved' ? 'status-approved' : 'status-pending'}">
                                    ${item.status === 'approved' ? '已批准' : '待审核'}
                                </span>
                            </div>
                        </div>
                    `).join('');
                } else {
                    listContainer.innerHTML = '<p style="text-align: center; color: #6c757d; padding: 20px;">暂无知识记录</p>';
                }
            } catch (error) {
                console.error('Failed to load knowledge list:', error);
                document.getElementById('knowledgeList').innerHTML = 
                    '<p style="text-align: center; color: #dc3545; padding: 20px;">加载失败</p>';
            }
        }

        // 显示消息
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.innerHTML = `<div class="message ${type}">${message}</div>`;
            
            // 3秒后自动隐藏成功消息
            if (type === 'success') {
                setTimeout(() => {
                    messageDiv.innerHTML = '';
                }, 3000);
            }
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // ==================== 知识管理功能 ====================

        // 查看知识详情
        async function viewKnowledge(id) {
            try {
                const response = await fetch(`/api/learning/knowledge/${id}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const knowledge = data.data;
                    const modalBody = document.getElementById('viewModalBody');

                    modalBody.innerHTML = `
                        <div class="detail-item">
                            <div class="detail-label">问题</div>
                            <div class="detail-value">${escapeHtml(knowledge.question)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">答案</div>
                            <div class="detail-value">${escapeHtml(knowledge.answer)}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">分类</div>
                            <div class="detail-value">${knowledge.category || '未分类'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">知识点标志</div>
                            <div class="detail-value">${knowledge.knowledge_topic || '无'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">关键词</div>
                            <div class="detail-value">${knowledge.keywords ? knowledge.keywords.join(', ') : '无'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">置信度</div>
                            <div class="detail-value">${knowledge.confidence || 0}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">来源</div>
                            <div class="detail-value">${knowledge.source || '未知'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">学习来源</div>
                            <div class="detail-value">${knowledge.learned_from || '未知'}</div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">状态</div>
                            <div class="detail-value">
                                <span class="status-badge ${knowledge.status === 'approved' ? 'status-approved' : 'status-pending'}">
                                    ${knowledge.status === 'approved' ? '已批准' : '待审核'}
                                </span>
                            </div>
                        </div>
                        <div class="detail-item">
                            <div class="detail-label">创建时间</div>
                            <div class="detail-value">${knowledge.created_at ? new Date(knowledge.created_at).toLocaleString() : '未知'}</div>
                        </div>
                    `;

                    // 如果有知识点标志，加载相关知识
                    if (knowledge.knowledge_topic) {
                        loadRelatedKnowledge(knowledge.id, knowledge.knowledge_topic, modalBody);
                    }

                    document.getElementById('viewModal').style.display = 'block';
                } else {
                    showMessage('❌ 获取知识详情失败：' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            }
        }

        // 编辑知识
        async function editKnowledge(id) {
            try {
                const response = await fetch(`/api/learning/knowledge/${id}`);
                const data = await response.json();

                if (data.success && data.data) {
                    const knowledge = data.data;

                    // 填充编辑表单
                    document.getElementById('editKnowledgeId').value = knowledge.id;
                    document.getElementById('editQuestion').value = knowledge.question;
                    document.getElementById('editAnswer').value = knowledge.answer;
                    document.getElementById('editCategory').value = knowledge.category || 'general';
                    document.getElementById('editKnowledgeTopic').value = knowledge.knowledge_topic || '';
                    document.getElementById('editKeywords').value = knowledge.keywords ? knowledge.keywords.join(', ') : '';
                    document.getElementById('editConfidence').value = knowledge.confidence || 0.8;
                    document.getElementById('editConfidenceValue').textContent = knowledge.confidence || 0.8;

                    document.getElementById('editModal').style.display = 'block';
                } else {
                    showMessage('❌ 获取知识详情失败：' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            }
        }

        // 保存编辑的知识
        async function saveKnowledge() {
            const id = document.getElementById('editKnowledgeId').value;
            const formData = {
                question: document.getElementById('editQuestion').value.trim(),
                answer: document.getElementById('editAnswer').value.trim(),
                category: document.getElementById('editCategory').value,
                knowledge_topic: document.getElementById('editKnowledgeTopic').value.trim(),
                keywords: document.getElementById('editKeywords').value.split(',').map(k => k.trim()).filter(k => k),
                confidence: parseFloat(document.getElementById('editConfidence').value),
                source: 'user_input',
                learned_from: 'knowledge_manager'
            };

            // 验证数据
            if (!formData.question || !formData.answer) {
                showMessage('请填写问题和答案', 'error');
                return;
            }

            try {
                const response = await fetch(`/api/learning/knowledge/${id}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(formData)
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showMessage('✅ 知识更新成功！', 'success');
                    closeModal('editModal');

                    // 刷新列表和统计
                    loadKnowledgeList();
                    loadStatistics();
                } else {
                    showMessage('❌ 更新失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            }
        }

        // 删除知识
        async function deleteKnowledge(id) {
            if (!confirm('确定要删除这条知识吗？此操作不可撤销。')) {
                return;
            }

            try {
                const response = await fetch(`/api/learning/knowledge/${id}`, {
                    method: 'DELETE'
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showMessage('✅ 知识删除成功！', 'success');

                    // 刷新列表和统计
                    loadKnowledgeList();
                    loadStatistics();
                } else {
                    showMessage('❌ 删除失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            }
        }

        // 关闭模态框
        function closeModal(modalId) {
            document.getElementById(modalId).style.display = 'none';
        }

        // 批准知识（如果需要的话）
        async function approveKnowledge(id) {
            try {
                const response = await fetch(`/api/learning/knowledge/${id}/approve`, {
                    method: 'PUT'
                });

                const result = await response.json();

                if (response.ok && result.success) {
                    showMessage('✅ 知识批准成功！', 'success');

                    // 刷新列表和统计
                    loadKnowledgeList();
                    loadStatistics();
                } else {
                    showMessage('❌ 批准失败：' + (result.message || '未知错误'), 'error');
                }
            } catch (error) {
                console.error('Error:', error);
                showMessage('❌ 网络错误：' + error.message, 'error');
            }
        }

        // 加载相关知识
        async function loadRelatedKnowledge(currentId, knowledgeTopic, modalBody) {
            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge/${currentId}/related?limit=5`);
                const result = await response.json();

                if (response.ok && result.success && result.data.length > 0) {
                    const relatedSection = `
                        <div class="detail-item">
                            <div class="detail-label">🔗 相关问题 (${knowledgeTopic})</div>
                            <div class="detail-value">
                                ${result.data.map(item => `
                                    <div style="margin-bottom: 10px; padding: 8px; background: #f8f9fa; border-radius: 4px; border-left: 3px solid #007bff;">
                                        <strong>Q:</strong> ${escapeHtml(item.question)}<br>
                                        <small style="color: #6c757d;">置信度: ${item.confidence || 0} | 创建时间: ${item.created_at ? new Date(item.created_at).toLocaleDateString() : '未知'}</small>
                                        <div style="margin-top: 5px;">
                                            <button onclick="viewKnowledge(${item.id})" style="background: #007bff; color: white; border: none; padding: 2px 8px; border-radius: 3px; font-size: 12px; cursor: pointer;">查看详情</button>
                                        </div>
                                    </div>
                                `).join('')}
                            </div>
                        </div>
                    `;
                    modalBody.innerHTML += relatedSection;
                }
            } catch (error) {
                console.error('加载相关知识失败:', error);
            }
        }
    </script>
</body>
</html>
