package crawler

import (
	"database/sql"
	"encoding/json"
	"fmt"
	"log"
	"math"
	"strings"
	"time"

	"faq-system/internal/embedding"
	"faq-system/internal/logger"
	"faq-system/internal/nlp"
)

// ContentVectorizer 内容向量化器
type ContentVectorizer struct {
	db                  *sql.DB
	embedClient         *embedding.Client
	integratedProcessor *nlp.IntegratedProcessor
	vectorStore         VectorStoreInterface
}

// VectorStoreInterface 向量存储接口
type VectorStoreInterface interface {
	UpsertVector(id int, content string, embedding []float32) error
}

// VectorizedContent 向量化内容
type VectorizedContent struct {
	ID         int                    `json:"id"`
	Question   string                 `json:"question"`
	Answer     string                 `json:"answer"`
	Source     string                 `json:"source"`
	URL        string                 `json:"url"`
	Keywords   []string               `json:"keywords"`
	Entities   []nlp.Entity           `json:"entities"`
	Topics     []nlp.Topic            `json:"topics"`
	Sentiment  string                 `json:"sentiment"`
	Confidence float64                `json:"confidence"`
	Vector     []float32              `json:"vector,omitempty"`
	Metadata   map[string]interface{} `json:"metadata"`
	CreatedAt  time.Time              `json:"created_at"`
}

// NewContentVectorizer 创建内容向量化器
func NewContentVectorizer(db *sql.DB, embedClient *embedding.Client, integratedProcessor *nlp.IntegratedProcessor, vectorStore VectorStoreInterface) *ContentVectorizer {
	return &ContentVectorizer{
		db:                  db,
		embedClient:         embedClient,
		integratedProcessor: integratedProcessor,
		vectorStore:         vectorStore,
	}
}

// ProcessCrawledContent 处理爬取的内容
func (cv *ContentVectorizer) ProcessCrawledContent(result *CrawlResult) (*VectorizedContent, error) {
	log.Printf("🧠 开始NLP向量化处理: %s", result.URL)

	// 1. 使用集成NLP处理器分析内容
	nlpResult := cv.analyzeContentWithNLP(result)
	if nlpResult == nil {
		return nil, fmt.Errorf("NLP分析失败")
	}

	// 2. 生成问答对
	question, answer := cv.generateQuestionAnswer(result, nlpResult)
	if question == "" || answer == "" {
		return nil, fmt.Errorf("无法生成有效的问答对")
	}

	// 3. 生成向量
	vector, err := cv.generateVector(question, answer)
	if err != nil {
		log.Printf("⚠️ 向量生成失败: %v", err)
		// 不阻止保存，继续处理
	}

	// 4. 创建向量化内容
	vectorizedContent := &VectorizedContent{
		Question:   question,
		Answer:     answer,
		Source:     "crawler",
		URL:        result.URL,
		Keywords:   nlpResult.Keywords,
		Entities:   nlpResult.Entities,
		Topics:     nlpResult.Topics,
		Sentiment:  nlpResult.Sentiment,
		Confidence: nlpResult.Confidence,
		Vector:     vector,
		Metadata: map[string]interface{}{
			"title":           result.Title,
			"summary":         result.Summary,
			"content_length":  len(result.Content),
			"processing_time": nlpResult.ProcessingTime.String(),
			"nlp_method":      nlpResult.Method,
		},
		CreatedAt: time.Now(),
	}

	// 5. 保存到数据库
	err = cv.saveToDatabase(vectorizedContent)
	if err != nil {
		return nil, fmt.Errorf("保存到数据库失败: %v", err)
	}

	// 6. 保存向量到向量存储
	if len(vector) > 0 && cv.vectorStore != nil {
		err = cv.vectorStore.UpsertVector(vectorizedContent.ID, question, vector)
		if err != nil {
			log.Printf("⚠️ 向量存储失败: %v", err)
		}
	}

	log.Printf("✅ 内容向量化完成: ID=%d, Q=%s", vectorizedContent.ID, question)
	logger.CrawlerLogSuccess(fmt.Sprintf("内容向量化完成: %s", question), "🧠")

	return vectorizedContent, nil
}

// analyzeContentWithNLP 使用NLP分析内容
func (cv *ContentVectorizer) analyzeContentWithNLP(result *CrawlResult) *nlp.IntegratedResult {
	if cv.integratedProcessor == nil {
		log.Printf("⚠️ NLP处理器不可用，使用基础分析")
		return cv.basicAnalysis(result)
	}

	// 组合标题和内容进行分析
	combinedText := result.Title
	if result.Content != "" {
		combinedText += "\n" + result.Content
	}

	// 限制文本长度以提高处理效率
	if len(combinedText) > 2000 {
		combinedText = combinedText[:2000] + "..."
	}

	return cv.integratedProcessor.ProcessText(combinedText)
}

// basicAnalysis 基础分析（降级方案）
func (cv *ContentVectorizer) basicAnalysis(result *CrawlResult) *nlp.IntegratedResult {
	return &nlp.IntegratedResult{
		Tokens:     strings.Fields(result.Title + " " + result.Content),
		Keywords:   result.Keywords,
		Entities:   []nlp.Entity{},
		Topics:     []nlp.Topic{},
		Sentiment:  "中性",
		Confidence: 0.5,
		Method:     "basic",
	}
}

// generateQuestionAnswer 生成问答对
func (cv *ContentVectorizer) generateQuestionAnswer(result *CrawlResult, nlpResult *nlp.IntegratedResult) (string, string) {
	// 1. 生成问题
	question := cv.generateQuestion(result, nlpResult)

	// 2. 生成答案
	answer := cv.generateAnswer(result, nlpResult)

	return question, answer
}

// generateQuestion 生成问题
func (cv *ContentVectorizer) generateQuestion(result *CrawlResult, nlpResult *nlp.IntegratedResult) string {
	title := strings.TrimSpace(result.Title)

	// 如果标题已经是问句形式，直接使用
	if strings.HasSuffix(title, "?") || strings.HasSuffix(title, "？") {
		return title
	}

	// 如果标题包含疑问词，转换为问句
	if strings.Contains(title, "什么是") || strings.Contains(title, "如何") ||
		strings.Contains(title, "怎么") || strings.Contains(title, "为什么") {
		if !strings.HasSuffix(title, "？") {
			return title + "？"
		}
		return title
	}

	// 3. 基于NLP实体生成更精确的问题
	if len(nlpResult.Entities) > 0 {
		// 找到最重要的实体
		mainEntity := cv.findMostImportantEntity(nlpResult.Entities)
		if mainEntity != nil {
			switch mainEntity.Type {
			case "PERSON":
				return fmt.Sprintf("关于%s这个人有什么信息？", mainEntity.Text)
			case "ORGANIZATION":
				return fmt.Sprintf("关于%s这个组织有什么信息？", mainEntity.Text)
			case "LOCATION":
				return fmt.Sprintf("关于%s这个地方有什么信息？", mainEntity.Text)
			case "TECHNOLOGY":
				return fmt.Sprintf("%s是什么技术？", mainEntity.Text)
			case "PRODUCT":
				return fmt.Sprintf("%s是什么产品？", mainEntity.Text)
			default:
				return fmt.Sprintf("关于%s有什么信息？", mainEntity.Text)
			}
		}
	}

	// 4. 基于话题生成问题
	if len(nlpResult.Topics) > 0 {
		mainTopic := nlpResult.Topics[0].Title
		// 根据话题类型生成不同的问题
		if strings.Contains(strings.ToLower(mainTopic), "技术") ||
			strings.Contains(strings.ToLower(mainTopic), "科技") {
			return fmt.Sprintf("%s技术是什么？", mainTopic)
		}
		if strings.Contains(strings.ToLower(mainTopic), "新闻") ||
			strings.Contains(strings.ToLower(mainTopic), "资讯") {
			return fmt.Sprintf("关于%s的最新消息是什么？", mainTopic)
		}
		return fmt.Sprintf("关于%s有什么信息？", mainTopic)
	}

	// 5. 基于关键词生成问题
	if len(nlpResult.Keywords) > 0 {
		mainKeyword := nlpResult.Keywords[0]
		return fmt.Sprintf("关于%s有什么信息？", mainKeyword)
	}

	// 6. 基于内容类型生成问题
	if result.URL != "" {
		if strings.Contains(result.URL, "news") || strings.Contains(result.URL, "新闻") {
			return "这条新闻讲的是什么？"
		}
		if strings.Contains(result.URL, "tech") || strings.Contains(result.URL, "技术") {
			return "这个技术内容介绍了什么？"
		}
		if strings.Contains(result.URL, "blog") || strings.Contains(result.URL, "博客") {
			return "这篇博客讲的是什么？"
		}
	}

	// 7. 默认问题格式
	if title != "" {
		return fmt.Sprintf("关于%s有什么信息？", title)
	}

	return "这个内容讲的是什么？"
}

// findMostImportantEntity 找到最重要的实体
func (cv *ContentVectorizer) findMostImportantEntity(entities []nlp.Entity) *nlp.Entity {
	if len(entities) == 0 {
		return nil
	}

	// 按置信度排序，返回置信度最高的实体
	var bestEntity *nlp.Entity
	maxConfidence := 0.0

	for i := range entities {
		if entities[i].Confidence > maxConfidence {
			maxConfidence = entities[i].Confidence
			bestEntity = &entities[i]
		}
	}

	return bestEntity
}

// generateAnswer 使用NLP智能生成答案
func (cv *ContentVectorizer) generateAnswer(result *CrawlResult, nlpResult *nlp.IntegratedResult) string {
	var answerParts []string

	// 1. 使用摘要作为主要答案
	if result.Summary != "" {
		answerParts = append(answerParts, result.Summary)
	} else if len(result.Content) > 0 {
		// 如果没有摘要，使用智能内容提取
		content := cv.extractMainContent(result.Content)
		answerParts = append(answerParts, content)
	}

	// 2. 添加NLP实体信息（更结构化）
	if len(nlpResult.Entities) > 0 {
		entityInfo := cv.formatEntityInformation(nlpResult.Entities)
		if entityInfo != "" {
			answerParts = append(answerParts, "\n\n"+entityInfo)
		}
	}

	// 3. 添加情感分析结果
	if nlpResult.Sentiment != "" && nlpResult.Sentiment != "中性" {
		answerParts = append(answerParts, fmt.Sprintf("\n情感倾向：%s", nlpResult.Sentiment))
	}

	// 4. 添加话题信息（更详细）
	if len(nlpResult.Topics) > 0 {
		topicInfo := cv.formatTopicInformation(nlpResult.Topics)
		if topicInfo != "" {
			answerParts = append(answerParts, "\n\n"+topicInfo)
		}
	}

	// 5. 添加关键词信息
	if len(nlpResult.Keywords) > 0 {
		keywords := nlpResult.Keywords
		if len(keywords) > 8 {
			keywords = keywords[:8] // 限制关键词数量
		}
		answerParts = append(answerParts, fmt.Sprintf("\n\n关键词：%s", strings.Join(keywords, "、")))
	}

	// 6. 添加置信度信息
	if nlpResult.Confidence > 0 {
		answerParts = append(answerParts, fmt.Sprintf("\n\n分析置信度：%.1f%%", nlpResult.Confidence*100))
	}

	// 7. 添加来源信息
	answerParts = append(answerParts, fmt.Sprintf("\n\n来源：%s", result.URL))

	return strings.Join(answerParts, "")
}

// extractMainContent 提取主要内容
func (cv *ContentVectorizer) extractMainContent(content string) string {
	// 清理和截取内容
	content = strings.TrimSpace(content)
	if len(content) <= 300 {
		return content
	}

	// 尝试按段落分割
	paragraphs := strings.Split(content, "\n")
	var result strings.Builder
	totalLength := 0

	for _, paragraph := range paragraphs {
		paragraph = strings.TrimSpace(paragraph)
		if len(paragraph) < 10 { // 跳过太短的段落
			continue
		}

		if totalLength+len(paragraph) > 300 {
			break
		}

		if result.Len() > 0 {
			result.WriteString("\n")
		}
		result.WriteString(paragraph)
		totalLength += len(paragraph)
	}

	if result.Len() == 0 {
		// 降级到简单截取
		return content[:300] + "..."
	}

	return result.String()
}

// formatEntityInformation 格式化实体信息
func (cv *ContentVectorizer) formatEntityInformation(entities []nlp.Entity) string {
	if len(entities) == 0 {
		return ""
	}

	// 按类型分组实体
	entityGroups := make(map[string][]string)
	for _, entity := range entities {
		if entity.Confidence > 0.5 { // 只包含高置信度的实体
			entityGroups[entity.Type] = append(entityGroups[entity.Type], entity.Text)
		}
	}

	if len(entityGroups) == 0 {
		return ""
	}

	var parts []string
	typeNames := map[string]string{
		"PERSON":       "人物",
		"ORGANIZATION": "组织",
		"LOCATION":     "地点",
		"TECHNOLOGY":   "技术",
		"PRODUCT":      "产品",
		"EVENT":        "事件",
	}

	for entityType, entityList := range entityGroups {
		if len(entityList) > 0 {
			typeName := typeNames[entityType]
			if typeName == "" {
				typeName = entityType
			}

			// 去重
			uniqueEntities := cv.removeDuplicates(entityList)
			if len(uniqueEntities) > 3 {
				uniqueEntities = uniqueEntities[:3] // 限制数量
			}

			parts = append(parts, fmt.Sprintf("%s：%s", typeName, strings.Join(uniqueEntities, "、")))
		}
	}

	if len(parts) > 0 {
		return "相关实体：\n" + strings.Join(parts, "\n")
	}

	return ""
}

// formatTopicInformation 格式化话题信息
func (cv *ContentVectorizer) formatTopicInformation(topics []nlp.Topic) string {
	if len(topics) == 0 {
		return ""
	}

	var topicParts []string
	for i, topic := range topics {
		if i >= 5 { // 最多5个话题
			break
		}

		if topic.Confidence > 0.3 { // 只包含有一定置信度的话题
			topicParts = append(topicParts, topic.Title)
		}
	}

	if len(topicParts) > 0 {
		return "主要话题：" + strings.Join(topicParts, "、")
	}

	return ""
}

// removeDuplicates 去除重复项
func (cv *ContentVectorizer) removeDuplicates(items []string) []string {
	seen := make(map[string]bool)
	var result []string

	for _, item := range items {
		if !seen[item] {
			seen[item] = true
			result = append(result, item)
		}
	}

	return result
}

// generateVector 使用NLP生成向量
func (cv *ContentVectorizer) generateVector(question, answer string) ([]float32, error) {
	// 优先使用NLP处理器生成向量
	if cv.integratedProcessor != nil {
		log.Printf("🧮 使用NLP处理器生成向量")

		// 组合问题和答案
		combinedText := question + " " + answer

		// 使用集成NLP处理器分析文本
		nlpResult := cv.integratedProcessor.ProcessText(combinedText)
		if nlpResult != nil {
			// 从NLP结果生成向量
			vector := cv.generateVectorFromNLP(nlpResult, combinedText)
			if len(vector) > 0 {
				log.Printf("✅ NLP向量生成成功: 维度=%d", len(vector))
				return vector, nil
			}
		}
	}

	// 降级到嵌入客户端（如果可用）
	if cv.embedClient != nil {
		log.Printf("🔄 降级使用嵌入客户端生成向量")
		combinedText := question + " " + answer
		return cv.embedClient.EmbedText(combinedText)
	}

	// 最后降级到基础向量生成
	log.Printf("⚠️ 使用基础方法生成向量")
	return cv.generateBasicVector(question, answer), nil
}

// generateVectorFromNLP 从NLP结果生成向量
func (cv *ContentVectorizer) generateVectorFromNLP(nlpResult *nlp.IntegratedResult, text string) []float32 {
	// 基于NLP分析结果生成向量
	vectorDim := 128 // 与Spago处理器的维度保持一致
	vector := make([]float32, vectorDim)

	log.Printf("🔍 NLP结果分析: 关键词=%d, 实体=%d, 话题=%d, 置信度=%.3f",
		len(nlpResult.Keywords), len(nlpResult.Entities), len(nlpResult.Topics), nlpResult.Confidence)

	// 1. 基于关键词生成向量特征
	if len(nlpResult.Keywords) > 0 {
		for i, keyword := range nlpResult.Keywords {
			if i >= vectorDim/4 { // 使用前1/4的维度存储关键词特征
				break
			}
			// 简单的哈希映射到向量维度
			hash := cv.simpleHash(keyword) % (vectorDim / 4)
			vector[hash] += 1.0 / float32(len(nlpResult.Keywords))
		}
	}

	// 2. 基于实体生成向量特征
	if len(nlpResult.Entities) > 0 {
		offset := vectorDim / 4
		for i, entity := range nlpResult.Entities {
			if i >= vectorDim/4 {
				break
			}
			hash := cv.simpleHash(entity.Text) % (vectorDim / 4)
			vector[offset+hash] += float32(entity.Confidence) / float32(len(nlpResult.Entities))
		}
	}

	// 3. 基于话题生成向量特征
	if len(nlpResult.Topics) > 0 {
		offset := vectorDim / 2
		for i, topic := range nlpResult.Topics {
			if i >= vectorDim/4 {
				break
			}
			hash := cv.simpleHash(topic.Title) % (vectorDim / 4)
			vector[offset+hash] += float32(topic.Confidence) / float32(len(nlpResult.Topics))
		}
	}

	// 4. 基于文本长度和置信度生成特征
	textLength := float32(len(text))
	confidence := float32(nlpResult.Confidence)

	// 使用最后1/4的维度存储统计特征
	offset := vectorDim * 3 / 4
	vector[offset] = textLength / 1000.0 // 归一化文本长度
	vector[offset+1] = confidence
	vector[offset+2] = float32(len(nlpResult.Keywords)) / 10.0
	vector[offset+3] = float32(len(nlpResult.Entities)) / 5.0

	// 5. 归一化向量
	normalizedVector := cv.normalizeVector(vector)

	// 计算向量的非零元素数量
	nonZeroCount := 0
	for _, val := range normalizedVector {
		if val != 0 {
			nonZeroCount++
		}
	}

	log.Printf("🔍 向量生成完成: 维度=%d, 非零元素=%d", len(normalizedVector), nonZeroCount)
	return normalizedVector
}

// generateBasicVector 生成基础向量（降级方案）
func (cv *ContentVectorizer) generateBasicVector(question, answer string) []float32 {
	vectorDim := 128
	vector := make([]float32, vectorDim)

	// 基于文本内容生成简单向量
	combinedText := question + " " + answer

	// 使用字符频率生成向量
	charCount := make(map[rune]int)
	for _, char := range combinedText {
		charCount[char]++
	}

	// 将字符频率映射到向量维度
	i := 0
	for char, count := range charCount {
		if i >= vectorDim {
			break
		}
		hash := int(char) % vectorDim
		vector[hash] += float32(count) / float32(len(combinedText))
		i++
	}

	// 添加文本统计特征
	if vectorDim > 4 {
		vector[vectorDim-4] = float32(len(question)) / 100.0
		vector[vectorDim-3] = float32(len(answer)) / 500.0
		vector[vectorDim-2] = float32(len(combinedText)) / 600.0
		vector[vectorDim-1] = 1.0 // 标识这是基础向量
	}

	return cv.normalizeVector(vector)
}

// simpleHash 简单哈希函数
func (cv *ContentVectorizer) simpleHash(text string) int {
	hash := 0
	for _, char := range text {
		hash = hash*31 + int(char)
	}
	if hash < 0 {
		hash = -hash
	}
	return hash
}

// normalizeVector 归一化向量
func (cv *ContentVectorizer) normalizeVector(vector []float32) []float32 {
	// 计算向量的L2范数
	var norm float32
	for _, val := range vector {
		norm += val * val
	}

	if norm == 0 {
		return vector
	}

	// 计算L2范数的平方根
	norm = float32(math.Sqrt(float64(norm)))

	if norm == 0 {
		return vector
	}

	// 归一化
	for i := range vector {
		vector[i] /= norm
	}

	return vector
}

// cleanText 清理文本，移除有问题的字符
func (cv *ContentVectorizer) cleanText(text string) string {
	// 移除控制字符和无效UTF-8字符
	cleaned := strings.Map(func(r rune) rune {
		// 保留基本的空白字符
		if r == '\n' || r == '\r' || r == '\t' || r == ' ' {
			return r
		}
		// 移除其他控制字符
		if r < 32 {
			return -1
		}
		// 移除一些可能有问题的Unicode字符
		if r == 0xFEFF || r == 0x200B || r == 0x200C || r == 0x200D {
			return -1
		}
		return r
	}, text)

	// 规范化换行符
	cleaned = strings.ReplaceAll(cleaned, "\r\n", "\n")
	cleaned = strings.ReplaceAll(cleaned, "\r", "\n")

	// 移除多余的空白
	cleaned = strings.TrimSpace(cleaned)

	// 限制连续换行符
	for strings.Contains(cleaned, "\n\n\n") {
		cleaned = strings.ReplaceAll(cleaned, "\n\n\n", "\n\n")
	}

	return cleaned
}

// ultraCleanText 超级清理文本，移除所有可能有问题的字符
func (cv *ContentVectorizer) ultraCleanText(text string) string {
	// 首先尝试修复常见的编码问题
	text = cv.fixCommonEncodingIssues(text)

	// 只保留基本的ASCII字符和常用中文字符
	cleaned := strings.Map(func(r rune) rune {
		// 保留基本ASCII字符
		if r >= 32 && r <= 126 {
			return r
		}
		// 保留基本空白字符
		if r == '\n' || r == '\t' || r == ' ' {
			return r
		}
		// 保留常用中文字符范围
		if r >= 0x4E00 && r <= 0x9FFF {
			return r
		}
		// 保留中文标点符号
		if r >= 0x3000 && r <= 0x303F {
			return r
		}
		// 保留常用符号
		if r >= 0xFF00 && r <= 0xFFEF {
			return r
		}
		// 其他字符替换为空格或移除
		if r > 127 {
			return ' '
		}
		return -1
	}, text)

	// 清理多余空格
	for strings.Contains(cleaned, "  ") {
		cleaned = strings.ReplaceAll(cleaned, "  ", " ")
	}
	cleaned = strings.TrimSpace(cleaned)

	return cleaned
}

// fixCommonEncodingIssues 修复常见的编码问题
func (cv *ContentVectorizer) fixCommonEncodingIssues(text string) string {
	// 移除或替换常见的有问题字符
	replacements := map[string]string{
		"\xE6":         "e",  // 常见的编码错误
		"\x0A":         "\n", // 换行符
		"\x0D":         "\r", // 回车符
		"\x09":         "\t", // 制表符
		"\x00":         "",   // 空字符
		"\xEF\xBF\xBD": "?",  // UTF-8替换字符
	}

	result := text
	for old, new := range replacements {
		result = strings.ReplaceAll(result, old, new)
	}

	return result
}

// saveToDatabase 保存到数据库
func (cv *ContentVectorizer) saveToDatabase(content *VectorizedContent) error {
	// 清理和验证文本内容
	cleanQuestion := cv.cleanText(content.Question)
	cleanAnswer := cv.cleanText(content.Answer)

	// 验证文本长度
	if len(cleanQuestion) > 1000 {
		cleanQuestion = cleanQuestion[:1000] + "..."
	}
	if len(cleanAnswer) > 5000 {
		cleanAnswer = cleanAnswer[:5000] + "..."
	}

	log.Printf("🧹 文本清理完成: Q长度=%d, A长度=%d", len(cleanQuestion), len(cleanAnswer))

	// 序列化复杂字段
	keywordsJSON, _ := json.Marshal(content.Keywords)
	metadataJSON, _ := json.Marshal(content.Metadata)

	// 构建上下文信息（包含NLP分析结果）
	contextInfo := fmt.Sprintf("爬虫来源: %s\n情感倾向: %s\n实体数量: %d\n话题数量: %d",
		content.URL, content.Sentiment, len(content.Entities), len(content.Topics))

	// 插入到learned_knowledge表
	query := `
		INSERT INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status, metadata)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	result, err := cv.db.Exec(query,
		cleanQuestion,
		cleanAnswer,
		"crawler", // source
		content.Confidence,
		cv.determineCategory(content), // category
		keywordsJSON,
		contextInfo,      // context
		"crawler_system", // learned_from
		"approved",       // status - 爬虫内容直接批准
		metadataJSON)

	if err != nil {
		log.Printf("❌ 数据库插入失败: %v", err)
		log.Printf("🔍 问题文本预览: Q=%s..., A=%s...",
			cleanQuestion[:min(len(cleanQuestion), 50)],
			cleanAnswer[:min(len(cleanAnswer), 50)])

		// 尝试进一步清理文本
		if strings.Contains(err.Error(), "Incorrect string value") {
			log.Printf("🔧 检测到字符编码问题，尝试进一步清理...")
			ultraCleanQuestion := cv.ultraCleanText(cleanQuestion)
			ultraCleanAnswer := cv.ultraCleanText(cleanAnswer)

			// 重试插入
			result, retryErr := cv.db.Exec(query,
				ultraCleanQuestion,
				ultraCleanAnswer,
				"crawler", // source
				content.Confidence,
				cv.determineCategory(content), // category
				keywordsJSON,
				contextInfo,      // context
				"crawler_system", // learned_from
				"approved",       // status - 爬虫内容直接批准
				metadataJSON)

			if retryErr != nil {
				return fmt.Errorf("插入learned_knowledge失败（重试后）: %v", retryErr)
			}

			// 获取插入的ID
			knowledgeID, err := result.LastInsertId()
			if err != nil {
				return fmt.Errorf("获取knowledge ID失败: %v", err)
			}
			content.ID = int(knowledgeID)
			log.Printf("✅ 重试插入成功: ID=%d", content.ID)

		} else {
			return fmt.Errorf("插入learned_knowledge失败: %v", err)
		}
	} else {
		// 获取插入的ID
		knowledgeID, err := result.LastInsertId()
		if err != nil {
			return fmt.Errorf("获取knowledge ID失败: %v", err)
		}
		content.ID = int(knowledgeID)
		log.Printf("✅ 数据库插入成功: ID=%d", content.ID)
	}

	// 如果有向量数据，保存到knowledge_vectors表
	if len(content.Vector) > 0 {
		vectorJSON, _ := json.Marshal(content.Vector)
		vectorQuery := `
			INSERT INTO knowledge_vectors (knowledge_id, vector_data, vector_type)
			VALUES (?, ?, 'combined')
			ON DUPLICATE KEY UPDATE
			vector_data = VALUES(vector_data)
		`

		_, err = cv.db.Exec(vectorQuery, content.ID, vectorJSON)
		if err != nil {
			log.Printf("⚠️ 插入向量数据失败: %v", err)
			// 不阻止主要功能，继续执行
		} else {
			log.Printf("✅ 向量存储成功: ID=%d, 维度=%d", content.ID, len(content.Vector))
		}
	}

	return nil
}

// determineCategory 确定知识分类
func (cv *ContentVectorizer) determineCategory(content *VectorizedContent) string {
	// 基于URL和关键词确定分类
	url := strings.ToLower(content.URL)

	// 基于URL域名分类
	if strings.Contains(url, "baidu.com") {
		return "热点资讯"
	}
	if strings.Contains(url, "zhihu.com") {
		return "知识问答"
	}
	if strings.Contains(url, "github.com") {
		return "技术开发"
	}
	if strings.Contains(url, "stackoverflow.com") {
		return "编程技术"
	}

	// 基于关键词分类
	if len(content.Keywords) > 0 {
		firstKeyword := strings.ToLower(content.Keywords[0])
		if strings.Contains(firstKeyword, "技术") || strings.Contains(firstKeyword, "编程") ||
			strings.Contains(firstKeyword, "开发") || strings.Contains(firstKeyword, "代码") {
			return "技术开发"
		}
		if strings.Contains(firstKeyword, "新闻") || strings.Contains(firstKeyword, "热点") ||
			strings.Contains(firstKeyword, "资讯") {
			return "热点资讯"
		}
		if strings.Contains(firstKeyword, "教育") || strings.Contains(firstKeyword, "学习") {
			return "教育学习"
		}
	}

	// 基于话题分类
	if len(content.Topics) > 0 {
		topicTitle := strings.ToLower(content.Topics[0].Title)
		if strings.Contains(topicTitle, "科技") {
			return "科技资讯"
		}
		if strings.Contains(topicTitle, "娱乐") {
			return "娱乐资讯"
		}
	}

	// 默认分类
	return "网络资讯"
}

// GetVectorizedContentByID 根据ID获取向量化内容
func (cv *ContentVectorizer) GetVectorizedContentByID(id int) (*VectorizedContent, error) {
	query := `
		SELECT lk.id, lk.question, lk.answer, lk.source, lk.confidence,
		       lk.category, lk.keywords, lk.context, lk.learned_from,
		       lk.status, lk.metadata, lk.created_at,
		       COALESCE(kv.vector_data, '[]') as vector_data
		FROM learned_knowledge lk
		LEFT JOIN knowledge_vectors kv ON lk.id = kv.knowledge_id
		WHERE lk.id = ? AND lk.source = 'crawler'
	`

	var content VectorizedContent
	var keywordsJSON, metadataJSON, vectorJSON string
	var category, context, learnedFrom, status string

	err := cv.db.QueryRow(query, id).Scan(
		&content.ID, &content.Question, &content.Answer,
		&content.Source, &content.Confidence,
		&category, &keywordsJSON, &context, &learnedFrom,
		&status, &metadataJSON, &content.CreatedAt, &vectorJSON)

	if err != nil {
		return nil, err
	}

	// 反序列化JSON字段
	json.Unmarshal([]byte(keywordsJSON), &content.Keywords)
	json.Unmarshal([]byte(vectorJSON), &content.Vector)
	json.Unmarshal([]byte(metadataJSON), &content.Metadata)

	// 从context中提取URL（如果存在）
	if strings.Contains(context, "爬虫来源: ") {
		lines := strings.Split(context, "\n")
		for _, line := range lines {
			if strings.HasPrefix(line, "爬虫来源: ") {
				content.URL = strings.TrimPrefix(line, "爬虫来源: ")
				break
			}
			if strings.HasPrefix(line, "情感倾向: ") {
				content.Sentiment = strings.TrimPrefix(line, "情感倾向: ")
			}
		}
	}

	// 设置默认值
	if content.Sentiment == "" {
		content.Sentiment = "中性"
	}

	return &content, nil
}

// GetCrawlerKnowledgeStats 获取爬虫知识统计
func (cv *ContentVectorizer) GetCrawlerKnowledgeStats() (map[string]interface{}, error) {
	stats := make(map[string]interface{})

	// 总数统计
	var totalCount int
	err := cv.db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE source = 'crawler'").Scan(&totalCount)
	if err != nil {
		return nil, err
	}
	stats["total_count"] = totalCount

	// 按分类统计
	categoryQuery := `
		SELECT category, COUNT(*) as count
		FROM learned_knowledge
		WHERE source = 'crawler'
		GROUP BY category
		ORDER BY count DESC
	`
	rows, err := cv.db.Query(categoryQuery)
	if err != nil {
		return nil, err
	}
	defer rows.Close()

	categories := make(map[string]int)
	for rows.Next() {
		var category string
		var count int
		if err := rows.Scan(&category, &count); err == nil {
			categories[category] = count
		}
	}
	stats["categories"] = categories

	// 平均置信度
	var avgConfidence float64
	err = cv.db.QueryRow("SELECT AVG(confidence) FROM learned_knowledge WHERE source = 'crawler'").Scan(&avgConfidence)
	if err != nil {
		avgConfidence = 0
	}
	stats["avg_confidence"] = avgConfidence

	// 最近7天的数量
	var recentCount int
	err = cv.db.QueryRow(`
		SELECT COUNT(*) FROM learned_knowledge
		WHERE source = 'crawler' AND created_at >= DATE_SUB(NOW(), INTERVAL 7 DAY)
	`).Scan(&recentCount)
	if err != nil {
		recentCount = 0
	}
	stats["recent_7days"] = recentCount

	return stats, nil
}
