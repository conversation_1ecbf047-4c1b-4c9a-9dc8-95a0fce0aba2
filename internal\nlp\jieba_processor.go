package nlp

import (
	"log"
	"sort"
	"strings"
	"sync"
	"unicode"

	"github.com/yanyiwu/gojieba"
)

// 全局单例变量
var (
	globalJieba          *gojieba.Jieba
	globalJiebaOnce      sync.Once
	globalJiebaMux       sync.RWMutex
	globalJiebaProcessor *JiebaProcessor
	globalProcessorOnce  sync.Once
)

// JiebaProcessor 基于gojieba的中文NLP处理器
type JiebaProcessor struct {
	stopWords map[string]bool
}

// WordInfo 词汇信息
type WordInfo struct {
	Word      string  `json:"word"`      // 词汇
	Frequency int     `json:"frequency"` // 词频
	Weight    float64 `json:"weight"`    // 权重
	POS       string  `json:"pos"`       // 词性
}

// Entity 命名实体
type Entity struct {
	Text       string  `json:"text"`       // 实体文本
	Type       string  `json:"type"`       // 实体类型：PERSON, ORG, LOC, MISC
	Confidence float64 `json:"confidence"` // 置信度
	Position   int     `json:"position"`   // 在文本中的位置
}

// Topic 主题话题
type Topic struct {
	Title      string   `json:"title"`      // 话题标题
	Keywords   []string `json:"keywords"`   // 相关关键词
	Rank       int      `json:"rank"`       // 排名
	HotValue   string   `json:"hot_value"`  // 热度值
	Category   string   `json:"category"`   // 分类
	Confidence float64  `json:"confidence"` // 置信度
}

// NewJiebaProcessor 创建jieba处理器（全局单例模式）
func NewJiebaProcessor() *JiebaProcessor {
	globalProcessorOnce.Do(func() {
		log.Printf("🔧 初始化全局JiebaProcessor单例")

		globalJiebaProcessor = &JiebaProcessor{
			stopWords: make(map[string]bool),
		}

		globalJiebaProcessor.initializeStopWords()

		log.Printf("✅ 全局JiebaProcessor单例初始化完成")
	})

	return globalJiebaProcessor
}

// getGlobalJieba 获取全局jieba实例（线程安全，稳定初始化）
func (jp *JiebaProcessor) getGlobalJieba() *gojieba.Jieba {
	// 使用更安全的初始化策略
	globalJiebaOnce.Do(func() {
		defer func() {
			if r := recover(); r != nil {
				log.Printf("⚠️ gojieba初始化失败，使用简单分词: %v", r)
				globalJiebaMux.Lock()
				globalJieba = nil
				globalJiebaMux.Unlock()
			}
		}()

		log.Printf("🔧 初始化gojieba分词器...")

		// 使用与test_gojieba.go完全相同的初始化方式
		jieba := gojieba.NewJieba()

		if jieba != nil {
			log.Printf("✅ gojieba初始化成功")

			// 测试gojieba功能
			testWords := jieba.Cut("测试gojieba分词功能", true)
			if len(testWords) > 0 {
				log.Printf("✅ gojieba测试成功: %v", testWords)
				globalJiebaMux.Lock()
				globalJieba = jieba
				globalJiebaMux.Unlock()
			} else {
				log.Printf("⚠️ gojieba测试失败，切换到简单分词")
				jieba.Free()
				globalJiebaMux.Lock()
				globalJieba = nil
				globalJiebaMux.Unlock()
			}
		} else {
			log.Printf("⚠️ gojieba初始化失败，使用简单分词")
			globalJiebaMux.Lock()
			globalJieba = nil
			globalJiebaMux.Unlock()
		}
	})

	globalJiebaMux.RLock()
	defer globalJiebaMux.RUnlock()
	return globalJieba
}

// CleanupGlobalJieba 清理全局jieba实例（程序退出时调用）
func CleanupGlobalJieba() {
	globalJiebaMux.Lock()
	defer globalJiebaMux.Unlock()

	if globalJieba != nil {
		log.Printf("🔧 清理全局gojieba实例...")
		globalJieba.Free()
		globalJieba = nil
		log.Printf("✅ 全局gojieba实例已清理")
	}
}

// Close 关闭jieba处理器（全局单例不需要关闭）
func (jp *JiebaProcessor) Close() {
	// 全局单例模式下，不在这里释放jieba资源
	// 程序退出时会自动释放
	log.Printf("🔧 JiebaProcessor关闭（全局单例模式）")
}

// SegmentText 中文分词
func (jp *JiebaProcessor) SegmentText(text string) []string {
	jieba := jp.getGlobalJieba()
	if jieba == nil {
		return jp.simpleSegment(text)
	}

	// 使用gojieba进行精确分词
	words := jieba.CutForSearch(text, true)
	if len(words) == 0 {
		log.Printf("⚠️ gojieba分词返回空结果，使用简单分词")
		return jp.simpleSegment(text)
	}

	// 过滤和清理
	cleanWords := []string{}
	for _, word := range words {
		word = strings.TrimSpace(word)
		if jp.isValidWord(word) {
			cleanWords = append(cleanWords, word)
		}
	}

	// 如果过滤后没有有效词汇，使用简单分词
	if len(cleanWords) == 0 {
		return jp.simpleSegment(text)
	}

	return cleanWords
}

// ExtractKeywords 提取关键词（基于TF-IDF）
func (jp *JiebaProcessor) ExtractKeywords(text string, topK int) []WordInfo {
	jieba := jp.getGlobalJieba()
	if jieba == nil {
		return jp.simpleExtractKeywords(text, topK)
	}

	// 使用gojieba的TF-IDF提取关键词
	keywords := jieba.ExtractWithWeight(text, topK)
	if len(keywords) == 0 {
		log.Printf("⚠️ gojieba关键词提取返回空结果，使用简单提取")
		return jp.simpleExtractKeywords(text, topK)
	}

	wordInfos := []WordInfo{}
	for _, kw := range keywords {
		if jp.isValidWord(kw.Word) {
			wordInfos = append(wordInfos, WordInfo{
				Word:      kw.Word,
				Weight:    kw.Weight,
				Frequency: 1, // gojieba不提供频率，设为1
				POS:       jp.guessWordPOS(kw.Word),
			})
		}
	}

	// 如果没有有效关键词，使用简单提取
	if len(wordInfos) == 0 {
		return jp.simpleExtractKeywords(text, topK)
	}

	return wordInfos
}

// ExtractKeywordsWithPOS 提取关键词（包含词性）
func (jp *JiebaProcessor) ExtractKeywordsWithPOS(text string, topK int) []WordInfo {
	jieba := jp.getGlobalJieba()
	if jieba == nil {
		return jp.simpleExtractKeywords(text, topK)
	}

	// 先进行分词
	words := jieba.Cut(text, true)

	// 计算词频
	wordFreq := make(map[string]int)
	wordPOS := make(map[string]string)

	for _, word := range words {
		if jp.isValidWord(word) {
			wordFreq[word]++
			wordPOS[word] = "n" // 简化处理，默认为名词
		}
	}

	// 转换为WordInfo并排序
	wordInfos := []WordInfo{}
	for word, freq := range wordFreq {
		wordInfos = append(wordInfos, WordInfo{
			Word:      word,
			Frequency: freq,
			Weight:    float64(freq), // 简单使用频率作为权重
			POS:       wordPOS[word],
		})
	}

	// 按权重排序
	sort.Slice(wordInfos, func(i, j int) bool {
		return wordInfos[i].Weight > wordInfos[j].Weight
	})

	// 返回前topK个
	if len(wordInfos) > topK {
		wordInfos = wordInfos[:topK]
	}

	return wordInfos
}

// ExtractEntities 提取命名实体
func (jp *JiebaProcessor) ExtractEntities(text string) []Entity {
	entities := []Entity{}

	jieba := jp.getGlobalJieba()
	if jieba == nil {
		return jp.simpleExtractEntities(text)
	}

	// 使用分词识别实体
	words := jieba.Cut(text, true)

	for i, word := range words {
		// 简化的实体识别逻辑
		entityType := jp.posToEntityType("n") // 简化处理
		if entityType != "" && len(word) >= 2 {
			entity := Entity{
				Text:       word,
				Type:       entityType,
				Confidence: jp.calculateEntityConfidence(word, "n"),
				Position:   i,
			}
			entities = append(entities, entity)
		}
	}

	// 去重和过滤
	entities = jp.filterAndDeduplicateEntities(entities)

	return entities
}

// AnalyzeSentiment 情感分析（基于情感词典）
func (jp *JiebaProcessor) AnalyzeSentiment(text string) (string, float64) {
	words := jp.SegmentText(text)

	positiveScore := 0.0
	negativeScore := 0.0

	for _, word := range words {
		if score := jp.getSentimentScore(word); score != 0 {
			if score > 0 {
				positiveScore += score
			} else {
				negativeScore += -score
			}
		}
	}

	totalScore := positiveScore + negativeScore
	if totalScore == 0 {
		return "中性", 0.5
	}

	positiveRatio := positiveScore / totalScore

	if positiveRatio > 0.6 {
		return "积极", positiveRatio
	} else if positiveRatio < 0.4 {
		return "消极", 1.0 - positiveRatio
	} else {
		return "中性", 0.5
	}
}

// ExtractTopics 提取主题话题
func (jp *JiebaProcessor) ExtractTopics(text string, maxTopics int) []Topic {
	topics := []Topic{}

	// 分句处理
	sentences := jp.splitSentences(text)

	rank := 1
	for _, sentence := range sentences {
		if len(sentence) < 10 || len(sentence) > 100 {
			continue
		}

		// 提取句子关键词
		keywords := jp.ExtractKeywords(sentence, 3)
		if len(keywords) == 0 {
			continue
		}

		// 计算话题置信度
		confidence := jp.calculateTopicConfidence(sentence, keywords)
		if confidence < 0.4 {
			continue
		}

		// 提取热度值
		hotValue := jp.extractHotValue(sentence)

		// 分类话题
		category := jp.classifyTopicByKeywords(keywords)

		topic := Topic{
			Title:      jp.cleanTopicTitle(sentence),
			Keywords:   jp.extractKeywordStrings(keywords),
			Rank:       rank,
			HotValue:   hotValue,
			Category:   category,
			Confidence: confidence,
		}

		topics = append(topics, topic)
		rank++

		if len(topics) >= maxTopics {
			break
		}
	}

	return topics
}

// isValidWord 判断是否为有效词汇
func (jp *JiebaProcessor) isValidWord(word string) bool {
	// 特殊处理：日常用语和确认词汇
	dailyWords := map[string]bool{
		"嗯":   true,
		"嗯嗯":  true,
		"嗯嗯嗯": true,
		"不错":  true,
		"很好":  true,
		"好的":  true,
		"谢谢":  true,
		"感谢":  true,
		"你好":  true,
		"再见":  true,
		"ok":  true,
		"OK":  true,
		"好":   true,
		"行":   true,
		"可以":  true,
		"知道了": true,
		"明白了": true,
	}

	// 如果是日常用语，直接返回true
	if dailyWords[word] {
		return true
	}

	if len(word) < 2 {
		return false
	}

	// 过滤停用词
	if jp.stopWords[word] {
		return false
	}

	// 过滤纯数字和纯英文
	if jp.isPureNumber(word) || jp.isPureEnglish(word) {
		return false
	}

	// 过滤特殊字符
	if jp.hasSpecialChars(word) {
		return false
	}

	return true
}

// isImportantPOS 判断是否为重要词性
func (jp *JiebaProcessor) isImportantPOS(pos string) bool {
	importantPOS := map[string]bool{
		"n":  true, // 名词
		"nr": true, // 人名
		"ns": true, // 地名
		"nt": true, // 机构名
		"nz": true, // 其他专名
		"v":  true, // 动词
		"vn": true, // 名动词
		"a":  true, // 形容词
		"an": true, // 名形词
		"i":  true, // 成语
		"l":  true, // 习用语
	}

	return importantPOS[pos]
}

// posToEntityType 词性转实体类型
func (jp *JiebaProcessor) posToEntityType(pos string) string {
	switch pos {
	case "nr":
		return "PERSON" // 人名
	case "ns":
		return "LOC" // 地名
	case "nt":
		return "ORG" // 机构名
	case "nz":
		return "MISC" // 其他专名
	default:
		return ""
	}
}

// calculateEntityConfidence 计算实体置信度
func (jp *JiebaProcessor) calculateEntityConfidence(word, pos string) float64 {
	confidence := 0.5

	// 基于词性的置信度
	switch pos {
	case "nr":
		confidence = 0.9 // 人名置信度高
	case "ns":
		confidence = 0.8 // 地名置信度较高
	case "nt":
		confidence = 0.7 // 机构名置信度中等
	case "nz":
		confidence = 0.6 // 其他专名置信度一般
	}

	// 基于长度的调整
	if len(word) >= 3 {
		confidence += 0.1
	}

	return confidence
}

// initializeStopWords 初始化停用词
func (jp *JiebaProcessor) initializeStopWords() {
	stopWordsList := []string{
		// 基础停用词
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人",
		"都", "一", "一个", "上", "也", "很", "到", "说", "要", "去",
		"你", "会", "着", "没有", "看", "好", "自己", "这", "那", "什么",
		"为", "以", "时", "来", "用", "们", "生", "对", "作", "地",
		"于", "出", "分", "可", "下", "又", "后", "同", "现", "或",

		// 网页相关停用词
		"百度", "搜索", "首页", "登录", "注册", "更多", "展开", "收起",
		"点击", "链接", "跳转", "网站", "页面", "浏览器", "加载",
		"javascript", "function", "var", "document", "window",

		// 标点和符号
		"，", "。", "！", "？", "；", "：", "\"", "\"", "'", "'",
		"（", "）", "【", "】", "《", "》", "、", "·",
	}

	for _, word := range stopWordsList {
		jp.stopWords[word] = true
	}
}

// simpleSegment 优化的简单分词（稳定备用方案）
func (jp *JiebaProcessor) simpleSegment(text string) []string {
	// 预定义常见词汇，提高分词准确性
	commonWords := map[string]bool{
		"等于": true, "多少": true, "什么": true, "怎么": true, "为什么": true,
		"如何": true, "哪里": true, "什么时候": true, "谁": true, "哪个": true,
		"加法": true, "减法": true, "乘法": true, "除法": true, "计算": true,
		"数学": true, "问题": true, "答案": true, "结果": true, "方法": true,
		"系统": true, "程序": true, "代码": true, "开发": true, "技术": true,
	}

	words := []string{}

	// 先匹配常见词汇
	remainingText := text
	for word := range commonWords {
		if strings.Contains(remainingText, word) {
			words = append(words, word)
			remainingText = strings.ReplaceAll(remainingText, word, " ")
		}
	}

	// 处理剩余文本
	currentWord := ""
	for _, r := range remainingText {
		if unicode.Is(unicode.Han, r) {
			// 中文字符
			if currentWord != "" && !unicode.Is(unicode.Han, []rune(currentWord)[0]) {
				// 当前词是非中文，先保存
				if jp.isValidWord(currentWord) {
					words = append(words, currentWord)
				}
				currentWord = ""
			}
			currentWord += string(r)
			// 中文词汇长度控制
			if len([]rune(currentWord)) >= 2 {
				if jp.isValidWord(currentWord) {
					words = append(words, currentWord)
				}
				currentWord = ""
			}
		} else if unicode.IsLetter(r) || unicode.IsDigit(r) {
			// 英文字符或数字
			if currentWord != "" && unicode.Is(unicode.Han, []rune(currentWord)[0]) {
				// 当前词是中文，先保存
				if jp.isValidWord(currentWord) {
					words = append(words, currentWord)
				}
				currentWord = ""
			}
			currentWord += string(r)
		} else if r == '+' || r == '-' || r == '*' || r == '/' || r == '=' {
			// 数学运算符
			if currentWord != "" {
				if jp.isValidWord(currentWord) {
					words = append(words, currentWord)
				}
				currentWord = ""
			}
			words = append(words, string(r))
		} else {
			// 其他字符（标点符号等）
			if currentWord != "" {
				if jp.isValidWord(currentWord) {
					words = append(words, currentWord)
				}
				currentWord = ""
			}
		}
	}

	// 处理最后一个词
	if currentWord != "" && jp.isValidWord(currentWord) {
		words = append(words, currentWord)
	}

	// 去重并过滤
	seen := make(map[string]bool)
	result := []string{}
	for _, word := range words {
		word = strings.TrimSpace(word)
		if word != "" && !seen[word] && jp.isValidWord(word) {
			seen[word] = true
			result = append(result, word)
		}
	}

	return result
}

// simpleExtractKeywords 简单关键词提取（备用方案）
func (jp *JiebaProcessor) simpleExtractKeywords(text string, topK int) []WordInfo {
	words := jp.simpleSegment(text)
	wordFreq := make(map[string]int)

	for _, word := range words {
		if jp.isValidWord(word) {
			wordFreq[word]++
		}
	}

	wordInfos := []WordInfo{}
	for word, freq := range wordFreq {
		wordInfos = append(wordInfos, WordInfo{
			Word:      word,
			Frequency: freq,
			Weight:    float64(freq),
			POS:       "unknown",
		})
	}

	sort.Slice(wordInfos, func(i, j int) bool {
		return wordInfos[i].Weight > wordInfos[j].Weight
	})

	if len(wordInfos) > topK {
		wordInfos = wordInfos[:topK]
	}

	return wordInfos
}

// simpleExtractEntities 简单实体提取（备用方案）
func (jp *JiebaProcessor) simpleExtractEntities(text string) []Entity {
	entities := []Entity{}

	// 基于规则的简单实体识别
	personPatterns := []string{"先生", "女士", "教授", "博士", "主席", "总统", "部长"}
	orgPatterns := []string{"公司", "集团", "大学", "学院", "医院", "银行", "政府"}
	locPatterns := []string{"市", "省", "县", "区", "镇", "村", "国", "州"}

	words := jp.simpleSegment(text)

	for i, word := range words {
		entityType := ""
		confidence := 0.5

		for _, pattern := range personPatterns {
			if strings.Contains(word, pattern) {
				entityType = "PERSON"
				confidence = 0.6
				break
			}
		}

		if entityType == "" {
			for _, pattern := range orgPatterns {
				if strings.Contains(word, pattern) {
					entityType = "ORG"
					confidence = 0.6
					break
				}
			}
		}

		if entityType == "" {
			for _, pattern := range locPatterns {
				if strings.Contains(word, pattern) {
					entityType = "LOC"
					confidence = 0.6
					break
				}
			}
		}

		if entityType != "" {
			entities = append(entities, Entity{
				Text:       word,
				Type:       entityType,
				Confidence: confidence,
				Position:   i,
			})
		}
	}

	return entities
}

// filterAndDeduplicateEntities 过滤和去重实体
func (jp *JiebaProcessor) filterAndDeduplicateEntities(entities []Entity) []Entity {
	seen := make(map[string]bool)
	filtered := []Entity{}

	for _, entity := range entities {
		key := entity.Text + "|" + entity.Type
		if !seen[key] && entity.Confidence > 0.5 {
			seen[key] = true
			filtered = append(filtered, entity)
		}
	}

	return filtered
}

// getSentimentScore 获取情感分数
func (jp *JiebaProcessor) getSentimentScore(word string) float64 {
	positiveWords := map[string]float64{
		// 基础积极词汇
		"好": 1.0, "棒": 1.0, "优秀": 1.2, "精彩": 1.1, "成功": 1.0,
		"胜利": 1.0, "喜欢": 0.8, "爱": 1.2, "开心": 1.0, "高兴": 1.0,
		"满意": 0.9, "赞": 0.8, "支持": 0.7, "推荐": 0.8, "优质": 1.0,
		"完美": 1.2, "出色": 1.1, "卓越": 1.2, "杰出": 1.1, "优异": 1.0,

		// 日常用语和确认词汇
		"不错": 1.2, "很好": 1.5, "好的": 0.8, "谢谢": 1.2, "感谢": 1.3,
		"嗯": 0.6, "嗯嗯": 0.7, "嗯嗯嗯": 0.8, "是的": 0.8, "对": 0.7,
		"没错": 0.9, "正确": 1.0, "同意": 0.9, "认同": 0.9, "可以": 0.8,
		"行": 0.7, "知道了": 0.6, "明白了": 0.7, "了解": 0.6, "好吧": 0.5,
		"你好": 0.8, "您好": 0.9, "早上好": 1.0, "下午好": 1.0, "晚上好": 1.0,
	}

	negativeWords := map[string]float64{
		"坏": -1.0, "差": -1.0, "失败": -1.1, "糟糕": -1.2, "讨厌": -1.0,
		"恨": -1.2, "愤怒": -1.1, "生气": -0.9, "失望": -0.8, "不满": -0.8,
		"批评": -0.7, "反对": -0.7, "抗议": -0.8, "问题": -0.5, "错误": -0.6,
		"垃圾": -1.2, "恶心": -1.1, "可怕": -1.0, "糟": -1.0, "烂": -1.1,
	}

	if score, exists := positiveWords[word]; exists {
		return score
	}

	if score, exists := negativeWords[word]; exists {
		return score
	}

	return 0.0
}

// splitSentences 分句
func (jp *JiebaProcessor) splitSentences(text string) []string {
	sentences := []string{}

	// 按标点符号分句
	delimiters := []string{"。", "！", "？", ".", "!", "?", "\n", "；"}

	current := text
	for _, delimiter := range delimiters {
		if strings.Contains(current, delimiter) {
			parts := strings.Split(current, delimiter)
			for _, part := range parts {
				part = strings.TrimSpace(part)
				if len(part) > 5 {
					sentences = append(sentences, part)
				}
			}
			break
		}
	}

	// 如果没有找到分隔符，按长度分割
	if len(sentences) == 0 {
		maxLen := 50
		for i := 0; i < len(current); i += maxLen {
			end := i + maxLen
			if end > len(current) {
				end = len(current)
			}
			sentence := strings.TrimSpace(current[i:end])
			if len(sentence) > 5 {
				sentences = append(sentences, sentence)
			}
		}
	}

	return sentences
}

// guessWordPOS 简单的词性猜测
func (jp *JiebaProcessor) guessWordPOS(word string) string {
	if len(word) == 0 {
		return "unknown"
	}

	// 简单的词性判断规则
	firstChar := []rune(word)[0]

	// 数字
	if unicode.IsDigit(firstChar) {
		return "m" // 数词
	}

	// 英文
	if unicode.IsLetter(firstChar) && firstChar < 128 {
		return "eng" // 英文
	}

	// 中文词汇长度判断
	wordLen := len([]rune(word))
	if wordLen == 1 {
		return "n" // 单字多为名词
	} else if wordLen == 2 {
		return "n" // 双字词多为名词
	} else {
		return "n" // 默认名词
	}
}

// calculateTopicConfidence 计算话题置信度
func (jp *JiebaProcessor) calculateTopicConfidence(sentence string, keywords []WordInfo) float64 {
	confidence := 0.0

	// 基于长度
	if len(sentence) >= 10 && len(sentence) <= 50 {
		confidence += 0.3
	} else if len(sentence) >= 5 && len(sentence) <= 80 {
		confidence += 0.2
	}

	// 基于关键词权重
	totalWeight := 0.0
	for _, kw := range keywords {
		totalWeight += kw.Weight
	}
	if totalWeight > 2.0 {
		confidence += 0.3
	} else if totalWeight > 1.0 {
		confidence += 0.2
	}

	// 基于中文比例
	chineseRatio := jp.calculateChineseRatio(sentence)
	confidence += chineseRatio * 0.2

	// 基于特殊特征
	if jp.hasTopicFeatures(sentence) {
		confidence += 0.2
	}

	return confidence
}

// extractHotValue 提取热度值
func (jp *JiebaProcessor) extractHotValue(text string) string {
	// 简化处理，直接查找包含"万"的词
	if strings.Contains(text, "万") {
		// 简单提取包含"万"的数字
		words := strings.Fields(text)
		for _, word := range words {
			if strings.Contains(word, "万") {
				return word
			}
		}
	}

	return ""
}

// classifyTopicByKeywords 基于关键词分类话题
func (jp *JiebaProcessor) classifyTopicByKeywords(keywords []WordInfo) string {
	categories := map[string][]string{
		"娱乐": {"明星", "演员", "歌手", "综艺", "娱乐", "偶像", "艺人"},
		"影视": {"电影", "电视剧", "导演", "主演", "上映", "播出", "影片", "剧集"},
		"文学": {"小说", "作者", "作品", "文学", "书籍", "写作", "创作"},
		"科技": {"科技", "技术", "AI", "互联网", "数码", "手机", "电脑", "软件"},
		"体育": {"体育", "比赛", "运动", "球员", "赛事", "奥运", "足球", "篮球"},
		"社会": {"社会", "新闻", "事件", "政策", "民生", "公共", "国家"},
		"财经": {"经济", "股票", "金融", "投资", "市场", "公司", "企业", "商业"},
	}

	for _, kw := range keywords {
		for category, categoryKeywords := range categories {
			for _, ckw := range categoryKeywords {
				if strings.Contains(kw.Word, ckw) {
					return category
				}
			}
		}
	}

	return "综合"
}

// extractKeywordStrings 提取关键词字符串
func (jp *JiebaProcessor) extractKeywordStrings(keywords []WordInfo) []string {
	result := []string{}
	for _, kw := range keywords {
		result = append(result, kw.Word)
	}
	return result
}

// cleanTopicTitle 清理话题标题
func (jp *JiebaProcessor) cleanTopicTitle(title string) string {
	// 移除热度值
	title = jp.removeHotValue(title)

	// 移除排名信息
	title = jp.removeRankInfo(title)

	// 移除特殊字符
	cleanChars := []string{"热", "新", "沸", "爆", "火"}
	for _, char := range cleanChars {
		title = strings.ReplaceAll(title, char, "")
	}

	return strings.TrimSpace(title)
}

// 辅助方法
func (jp *JiebaProcessor) isPureNumber(word string) bool {
	for _, r := range word {
		if !unicode.IsDigit(r) && r != '.' {
			return false
		}
	}
	return true
}

func (jp *JiebaProcessor) isPureEnglish(word string) bool {
	for _, r := range word {
		if !unicode.IsLetter(r) {
			return false
		}
	}
	return len(word) > 0 && word[0] >= 'a' && word[0] <= 'z' || word[0] >= 'A' && word[0] <= 'Z'
}

func (jp *JiebaProcessor) hasSpecialChars(word string) bool {
	specialChars := "!@#$%^&*()_+-=[]{}|;':\",./<>?"
	for _, r := range word {
		if strings.ContainsRune(specialChars, r) {
			return true
		}
	}
	return false
}

func (jp *JiebaProcessor) calculateChineseRatio(text string) float64 {
	if len(text) == 0 {
		return 0.0
	}

	chineseCount := 0
	totalCount := 0

	for _, r := range text {
		totalCount++
		if unicode.Is(unicode.Han, r) {
			chineseCount++
		}
	}

	return float64(chineseCount) / float64(totalCount)
}

func (jp *JiebaProcessor) hasTopicFeatures(text string) bool {
	features := []string{
		"热搜", "排行", "榜单", "话题", "事件", "新闻", "消息",
		"明星", "电影", "小说", "游戏", "科技", "体育",
	}

	for _, feature := range features {
		if strings.Contains(text, feature) {
			return true
		}
	}

	return false
}

func (jp *JiebaProcessor) removeHotValue(text string) string {
	// 简单移除包含数字和"万"的部分
	words := strings.Fields(text)
	cleanWords := []string{}

	for _, word := range words {
		if !strings.Contains(word, "万") && !jp.isPureNumber(word) {
			cleanWords = append(cleanWords, word)
		}
	}

	return strings.Join(cleanWords, " ")
}

func (jp *JiebaProcessor) removeRankInfo(text string) string {
	// 移除排名信息
	rankWords := []string{"第", "名", "位", "排名", "榜"}

	for _, rankWord := range rankWords {
		text = strings.ReplaceAll(text, rankWord, "")
	}

	return text
}
