-- 爬虫FAQ元数据表
CREATE TABLE IF NOT EXISTS crawler_faq_metadata (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    faq_id INT NOT NULL COMMENT 'FAQ ID，关联faq表',
    source VARCHAR(50) NOT NULL DEFAULT 'crawler' COMMENT '来源标识',
    url TEXT COMMENT '原始URL',
    
    -- NLP分析结果
    keywords JSON COMMENT '关键词列表',
    entities JSON COMMENT '实体列表',
    topics JSON COMMENT '话题列表',
    sentiment VARCHAR(20) DEFAULT '中性' COMMENT '情感倾向',
    confidence DECIMAL(5,4) DEFAULT 0.5 COMMENT 'NLP分析置信度',
    
    -- 向量数据
    vector_data JSON COMMENT '向量数据',
    
    -- 元数据
    metadata JSON COMMENT '其他元数据',
    
    -- 时间戳
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
    
    -- 索引
    UNIQUE KEY unique_faq_id (faq_id),
    INDEX idx_source (source),
    INDEX idx_confidence (confidence),
    INDEX idx_created_at (created_at),
    
    -- 外键约束
    FOREIGN KEY (faq_id) REFERENCES faq(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬虫FAQ元数据表';

-- 爬虫内容处理统计表
CREATE TABLE IF NOT EXISTS crawler_processing_stats (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    date DATE NOT NULL COMMENT '统计日期',
    
    -- 处理统计
    total_processed INT DEFAULT 0 COMMENT '总处理数量',
    successful_vectorized INT DEFAULT 0 COMMENT '成功向量化数量',
    failed_vectorized INT DEFAULT 0 COMMENT '向量化失败数量',
    
    -- FAQ生成统计
    faqs_generated INT DEFAULT 0 COMMENT '生成FAQ数量',
    avg_confidence DECIMAL(5,4) DEFAULT 0 COMMENT '平均置信度',
    
    -- NLP处理统计
    nlp_jieba_count INT DEFAULT 0 COMMENT 'Jieba处理数量',
    nlp_spago_count INT DEFAULT 0 COMMENT 'Spago处理数量',
    nlp_integrated_count INT DEFAULT 0 COMMENT '集成处理数量',
    nlp_basic_count INT DEFAULT 0 COMMENT '基础处理数量',
    
    -- 时间统计
    avg_processing_time_ms INT DEFAULT 0 COMMENT '平均处理时间(毫秒)',
    total_processing_time_ms BIGINT DEFAULT 0 COMMENT '总处理时间(毫秒)',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_date (date),
    INDEX idx_date (date)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬虫内容处理统计表';

-- 向量质量评估表
CREATE TABLE IF NOT EXISTS vector_quality_metrics (
    id BIGINT AUTO_INCREMENT PRIMARY KEY,
    faq_id INT NOT NULL COMMENT 'FAQ ID',
    
    -- 向量质量指标
    vector_dimension INT COMMENT '向量维度',
    vector_norm DECIMAL(10,6) COMMENT '向量范数',
    vector_sparsity DECIMAL(5,4) COMMENT '向量稀疏度',
    
    -- 语义质量指标
    semantic_coherence DECIMAL(5,4) COMMENT '语义连贯性',
    keyword_coverage DECIMAL(5,4) COMMENT '关键词覆盖度',
    topic_relevance DECIMAL(5,4) COMMENT '话题相关性',
    
    -- 生成质量指标
    question_quality DECIMAL(5,4) COMMENT '问题质量分数',
    answer_quality DECIMAL(5,4) COMMENT '答案质量分数',
    qa_coherence DECIMAL(5,4) COMMENT '问答连贯性',
    
    -- 评估元数据
    evaluation_method VARCHAR(50) COMMENT '评估方法',
    evaluation_version VARCHAR(20) COMMENT '评估版本',
    
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    
    UNIQUE KEY unique_faq_id (faq_id),
    INDEX idx_semantic_coherence (semantic_coherence),
    INDEX idx_question_quality (question_quality),
    INDEX idx_answer_quality (answer_quality),
    
    FOREIGN KEY (faq_id) REFERENCES faq(id) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='向量质量评估表';

-- 创建视图：爬虫FAQ完整信息
CREATE OR REPLACE VIEW crawler_faq_complete AS
SELECT 
    f.id,
    f.question,
    f.answer,
    f.created_at as faq_created_at,
    
    -- 元数据信息
    m.source,
    m.url,
    m.keywords,
    m.entities,
    m.topics,
    m.sentiment,
    m.confidence,
    m.metadata,
    m.created_at as metadata_created_at,
    
    -- 质量指标
    q.semantic_coherence,
    q.keyword_coverage,
    q.topic_relevance,
    q.question_quality,
    q.answer_quality,
    q.qa_coherence
    
FROM faq f
LEFT JOIN crawler_faq_metadata m ON f.id = m.faq_id
LEFT JOIN vector_quality_metrics q ON f.id = q.faq_id
WHERE m.source = 'crawler';

-- 创建存储过程：更新处理统计
DELIMITER //
CREATE PROCEDURE UpdateCrawlerProcessingStats(
    IN p_date DATE,
    IN p_processed_count INT,
    IN p_vectorized_count INT,
    IN p_failed_count INT,
    IN p_faq_count INT,
    IN p_confidence DECIMAL(5,4),
    IN p_nlp_method VARCHAR(20),
    IN p_processing_time_ms INT
)
BEGIN
    INSERT INTO crawler_processing_stats (
        date, total_processed, successful_vectorized, failed_vectorized,
        faqs_generated, avg_confidence, avg_processing_time_ms, total_processing_time_ms
    ) VALUES (
        p_date, p_processed_count, p_vectorized_count, p_failed_count,
        p_faq_count, p_confidence, p_processing_time_ms, p_processing_time_ms
    )
    ON DUPLICATE KEY UPDATE
        total_processed = total_processed + p_processed_count,
        successful_vectorized = successful_vectorized + p_vectorized_count,
        failed_vectorized = failed_vectorized + p_failed_count,
        faqs_generated = faqs_generated + p_faq_count,
        avg_confidence = (avg_confidence * faqs_generated + p_confidence * p_faq_count) / (faqs_generated + p_faq_count),
        total_processing_time_ms = total_processing_time_ms + p_processing_time_ms,
        avg_processing_time_ms = total_processing_time_ms / total_processed,
        updated_at = CURRENT_TIMESTAMP;
    
    -- 更新NLP方法统计
    CASE p_nlp_method
        WHEN 'jieba' THEN
            UPDATE crawler_processing_stats SET nlp_jieba_count = nlp_jieba_count + 1 WHERE date = p_date;
        WHEN 'spago' THEN
            UPDATE crawler_processing_stats SET nlp_spago_count = nlp_spago_count + 1 WHERE date = p_date;
        WHEN 'integrated' THEN
            UPDATE crawler_processing_stats SET nlp_integrated_count = nlp_integrated_count + 1 WHERE date = p_date;
        WHEN 'basic' THEN
            UPDATE crawler_processing_stats SET nlp_basic_count = nlp_basic_count + 1 WHERE date = p_date;
    END CASE;
END //
DELIMITER ;

-- 创建触发器：自动计算向量质量指标
DELIMITER //
CREATE TRIGGER calculate_vector_quality 
AFTER INSERT ON crawler_faq_metadata
FOR EACH ROW
BEGIN
    DECLARE v_dimension INT DEFAULT 0;
    DECLARE v_norm DECIMAL(10,6) DEFAULT 0;
    DECLARE v_keyword_count INT DEFAULT 0;
    DECLARE v_topic_count INT DEFAULT 0;
    
    -- 计算向量维度（如果有向量数据）
    IF NEW.vector_data IS NOT NULL THEN
        SET v_dimension = JSON_LENGTH(NEW.vector_data);
    END IF;
    
    -- 计算关键词数量
    IF NEW.keywords IS NOT NULL THEN
        SET v_keyword_count = JSON_LENGTH(NEW.keywords);
    END IF;
    
    -- 计算话题数量
    IF NEW.topics IS NOT NULL THEN
        SET v_topic_count = JSON_LENGTH(NEW.topics);
    END IF;
    
    -- 插入质量评估记录
    INSERT INTO vector_quality_metrics (
        faq_id, vector_dimension, keyword_coverage, topic_relevance,
        question_quality, answer_quality, evaluation_method, evaluation_version
    ) VALUES (
        NEW.faq_id, 
        v_dimension,
        LEAST(v_keyword_count / 10.0, 1.0), -- 关键词覆盖度（最多10个关键词为满分）
        LEAST(v_topic_count / 5.0, 1.0),   -- 话题相关性（最多5个话题为满分）
        NEW.confidence,                     -- 使用NLP置信度作为问题质量
        NEW.confidence * 0.9,               -- 答案质量稍低于问题质量
        'auto_trigger',
        '1.0'
    )
    ON DUPLICATE KEY UPDATE
        vector_dimension = VALUES(vector_dimension),
        keyword_coverage = VALUES(keyword_coverage),
        topic_relevance = VALUES(topic_relevance),
        question_quality = VALUES(question_quality),
        answer_quality = VALUES(answer_quality);
END //
DELIMITER ;
