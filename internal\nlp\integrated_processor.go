package nlp

import (
	"log"
	"strings"
	"sync"
	"time"
)

// IntegratedProcessor 集成的NLP处理器，结合jieba和spago
type IntegratedProcessor struct {
	jiebaProcessor      *JiebaProcessor
	spagoProcessor      *SpagoProcessor
	intentClassifier    *IntentClassifier
	sentimentAnalyzer   *SentimentAnalyzer
	entityExtractor     *EntityExtractor
	contextAnalyzer     *ContextAnalyzer
	relationExtractor   *RelationExtractor
	identityRecognizer  *IdentityRecognizer
	greetingRecognizer  *GreetingRecognizer
	apologyRecognizer   *ApologyRecognizer
	gratitudeRecognizer *GratitudeRecognizer
	initialized         bool
	mutex               sync.RWMutex
}

// IntegratedResult 集成处理结果
type IntegratedResult struct {
	// 基础分词结果
	Tokens    []string `json:"tokens"`
	Keywords  []string `json:"keywords"`
	Entities  []Entity `json:"entities"`
	Topics    []Topic  `json:"topics"`
	Sentiment string   `json:"sentiment"`

	// 高级NLP结果
	Intent            *IntentResult     `json:"intent,omitempty"`
	SentimentDetail   *SentimentResult  `json:"sentiment_detail,omitempty"`
	ExtractedEntities []ExtractedEntity `json:"extracted_entities"`
	Context           *ContextResult    `json:"context,omitempty"`
	Relations         []Relation        `json:"relations"`
	Identity          *IdentityResult   `json:"identity,omitempty"`
	Greeting          *GreetingResult   `json:"greeting,omitempty"`
	Apology           *ApologyResult    `json:"apology,omitempty"`
	Gratitude         *GratitudeResult  `json:"gratitude,omitempty"`

	// Spago高级结果
	SpagoResult *SpagoResult `json:"spago_result,omitempty"`

	// 语义向量分析结果
	SemanticEntities   []SemanticEntity `json:"semantic_entities,omitempty"`
	ConceptVectors     []ConceptVector  `json:"concept_vectors,omitempty"`
	SemanticSimilarity float64          `json:"semantic_similarity,omitempty"`

	// 元数据
	ProcessingTime time.Duration `json:"processing_time"`
	Confidence     float64       `json:"confidence"`
	Method         string        `json:"method"` // "jieba", "spago", "integrated", "enhanced"
	QualityScore   float64       `json:"quality_score"`
}

// SemanticEntity 语义实体
type SemanticEntity struct {
	Text       string            `json:"text"`
	Type       string            `json:"type"`             // technology, person, concept, organization, etc.
	Category   string            `json:"category"`         // programming_language, framework, database, etc.
	Confidence float64           `json:"confidence"`       // 识别置信度
	Context    string            `json:"context"`          // 上下文信息
	Vector     []float32         `json:"vector,omitempty"` // 语义向量
	Attributes map[string]string `json:"attributes"`       // 额外属性
}

// ConceptVector 概念向量
type ConceptVector struct {
	Concept    string    `json:"concept"`    // 概念名称
	Vector     []float32 `json:"vector"`     // 向量表示
	Confidence float64   `json:"confidence"` // 置信度
	Source     string    `json:"source"`     // 来源：jieba, spago, hybrid
}

// NewIntegratedProcessor 创建集成处理器
func NewIntegratedProcessor() *IntegratedProcessor {
	log.Printf("🚀 初始化增强集成NLP处理器 (Jieba + Spago + 高级NLP)")

	processor := &IntegratedProcessor{
		initialized: false,
	}

	// 同步初始化jieba（避免CGO并发问题），其他组件异步初始化
	processor.initializeJiebaSync()

	// 异步初始化其他组件
	go processor.initializeAsync()

	return processor
}

// initializeJiebaSync 同步初始化jieba（避免CGO并发问题）
func (ip *IntegratedProcessor) initializeJiebaSync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ Jieba同步初始化失败: %v", r)
		}
	}()

	log.Printf("🔧 同步初始化Jieba处理器...")
	ip.jiebaProcessor = NewJiebaProcessor()
	log.Printf("   ✅ Jieba处理器同步初始化完成")
}

// initializeAsync 异步初始化
func (ip *IntegratedProcessor) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 集成处理器初始化失败: %v", r)
			ip.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化增强集成处理器组件...")

	// 1. Jieba处理器已在同步初始化中完成
	log.Printf("   ✅ Jieba处理器已同步初始化")

	// 2. 初始化spago处理器
	ip.spagoProcessor = NewSpagoProcessor()
	log.Printf("   ✅ Spago处理器初始化完成")

	// 3. 初始化高级NLP组件
	ip.intentClassifier = NewIntentClassifier()
	log.Printf("   ✅ 意图分类器初始化完成")

	ip.sentimentAnalyzer = NewSentimentAnalyzer()
	log.Printf("   ✅ 情感分析器初始化完成")

	ip.entityExtractor = NewEntityExtractor()
	log.Printf("   ✅ 实体抽取器初始化完成")

	ip.contextAnalyzer = NewContextAnalyzer()
	log.Printf("   ✅ 上下文分析器初始化完成")

	ip.relationExtractor = NewRelationExtractor()
	log.Printf("   ✅ 关系抽取器初始化完成")

	ip.identityRecognizer = NewIdentityRecognizer()
	log.Printf("   ✅ 身份识别器初始化完成")

	ip.greetingRecognizer = NewGreetingRecognizer()
	log.Printf("   ✅ 问候语识别器初始化完成")

	ip.apologyRecognizer = NewApologyRecognizer()
	log.Printf("   ✅ 道歉/错误识别器初始化完成")

	ip.gratitudeRecognizer = NewGratitudeRecognizer()
	log.Printf("   ✅ 感谢识别器初始化完成")

	// 等待所有组件完全初始化
	time.Sleep(time.Second * 3)

	ip.mutex.Lock()
	ip.initialized = true
	ip.mutex.Unlock()

	log.Printf("✅ 增强集成NLP处理器初始化完成")
}

// ConversationHistory 对话历史结构
type ConversationHistory struct {
	Messages []HistoryMessage `json:"messages"`
	UserID   string           `json:"user_id"`
}

// HistoryMessage 历史消息
type HistoryMessage struct {
	Role      string    `json:"role"`      // "user" or "assistant"
	Content   string    `json:"content"`   // 消息内容
	Intent    string    `json:"intent"`    // 意图
	Timestamp time.Time `json:"timestamp"` // 时间戳
}

// ContextEnhancedResult 上下文增强的处理结果
type ContextEnhancedResult struct {
	*IntegratedResult
	ContextAnalysis   *ContextAnalysisResult `json:"context_analysis,omitempty"`
	EnhancedKeywords  []string               `json:"enhanced_keywords"`  // 结合上下文的关键词
	EnhancedIntent    *IntentResult          `json:"enhanced_intent"`    // 结合上下文的意图
	ContextConfidence float64                `json:"context_confidence"` // 上下文理解置信度
}

// ContextAnalysisResult 上下文分析结果
type ContextAnalysisResult struct {
	MainTopic         string   `json:"main_topic"`          // 主要话题
	TechDomain        string   `json:"tech_domain"`         // 技术领域
	ConversationFlow  string   `json:"conversation_flow"`   // 对话流向
	RelatedKeywords   []string `json:"related_keywords"`    // 相关关键词
	PreviousIntents   []string `json:"previous_intents"`    // 之前的意图
	IsFollowUp        bool     `json:"is_follow_up"`        // 是否是追问
	IsIncompleteInput bool     `json:"is_incomplete_input"` // 是否是不完整输入
}

// ProcessText 处理文本
func (ip *IntegratedProcessor) ProcessText(text string) *IntegratedResult {
	startTime := time.Now()

	ip.mutex.RLock()
	isInitialized := ip.initialized
	ip.mutex.RUnlock()

	if !isInitialized {
		log.Printf("⚠️ 增强集成处理器未完全初始化，使用基础处理")
		return ip.basicProcess(text)
	}

	// 检查空文本
	if strings.TrimSpace(text) == "" {
		log.Printf("⚠️ 输入文本为空，返回默认结果")
		return &IntegratedResult{
			Tokens:         []string{},
			Keywords:       []string{},
			Entities:       []Entity{},
			Topics:         []Topic{},
			Sentiment:      "中性",
			Confidence:     0.0,
			Method:         "empty_input",
			ProcessingTime: time.Since(startTime),
		}
	}

	// 安全地截取文本用于日志
	logText := text
	if len(text) > 50 {
		logText = text[:50] + "..."
	}
	log.Printf("🧠 增强集成NLP处理: %s", logText)

	result := &IntegratedResult{
		ProcessingTime: time.Since(startTime),
	}

	// 1. 使用jieba进行基础中文处理
	if ip.jiebaProcessor != nil {
		result.Tokens = ip.jiebaProcessor.SegmentText(text)

		keywords := ip.jiebaProcessor.ExtractKeywords(text, 10)
		result.Keywords = ip.convertKeywordsToStrings(keywords)

		result.Entities = ip.jiebaProcessor.ExtractEntities(text)
		result.Topics = ip.jiebaProcessor.ExtractTopics(text, 5)

		sentiment, _ := ip.jiebaProcessor.AnalyzeSentiment(text)
		result.Sentiment = sentiment

		result.Method = "jieba"
		log.Printf("   ✅ Jieba处理完成: %d tokens, %d keywords", len(result.Tokens), len(result.Keywords))
	}

	// 2. 使用spago进行高级处理
	if ip.spagoProcessor != nil {
		spagoResult := ip.spagoProcessor.ProcessText(text)
		result.SpagoResult = spagoResult

		// 如果jieba没有处理，使用spago的结果
		if result.Tokens == nil || len(result.Tokens) == 0 {
			result.Tokens = spagoResult.Tokens
		}

		// 融合置信度
		if result.Method == "jieba" {
			result.Method = "integrated"
			result.Confidence = (0.6 + spagoResult.Confidence*0.4) // jieba权重0.6，spago权重0.4
		} else {
			result.Method = "spago"
			result.Confidence = spagoResult.Confidence
		}

		log.Printf("   ✅ Spago处理完成: 置信度=%.3f", spagoResult.Confidence)
	}

	// 3. 语义向量分析和智能实体识别
	ip.performSemanticVectorAnalysis(text, result)

	// 4. 执行高级NLP分析
	ip.performAdvancedNLPAnalysis(text, result)

	// 4. 如果都没有处理成功，使用基础处理
	if result.Tokens == nil || len(result.Tokens) == 0 {
		return ip.basicProcess(text)
	}

	result.ProcessingTime = time.Since(startTime)
	log.Printf("✅ 增强集成处理完成: 方法=%s, 置信度=%.3f, 质量=%.3f, 耗时=%v",
		result.Method, result.Confidence, result.QualityScore, result.ProcessingTime)

	return result
}

// basicProcess 基础处理
func (ip *IntegratedProcessor) basicProcess(text string) *IntegratedResult {
	startTime := time.Now()

	result := &IntegratedResult{
		Tokens:            strings.Fields(text),
		Keywords:          []string{},
		Entities:          []Entity{},
		Topics:            []Topic{},
		Sentiment:         "中性",
		ExtractedEntities: []ExtractedEntity{},
		Relations:         []Relation{},
		SpagoResult:       nil,
		ProcessingTime:    time.Millisecond * 5,
		Confidence:        0.3,
		Method:            "basic",
		QualityScore:      0.2,
	}

	// 即使在基础处理中，也尝试执行高级NLP分析
	if ip.initialized {
		ip.performAdvancedNLPAnalysis(text, result)
		result.Method = "basic_enhanced"
		result.ProcessingTime = time.Since(startTime)
	}

	return result
}

// convertKeywordsToStrings 转换关键词为字符串数组
func (ip *IntegratedProcessor) convertKeywordsToStrings(keywords []WordInfo) []string {
	result := make([]string, len(keywords))
	for i, kw := range keywords {
		result[i] = kw.Word
	}
	return result
}

// Close 关闭处理器
func (ip *IntegratedProcessor) Close() {
	if ip.jiebaProcessor != nil {
		ip.jiebaProcessor.Close()
	}
	if ip.spagoProcessor != nil {
		ip.spagoProcessor.Close()
	}
}

// IsInitialized 检查是否已初始化
func (ip *IntegratedProcessor) IsInitialized() bool {
	ip.mutex.RLock()
	defer ip.mutex.RUnlock()
	return ip.initialized
}

// GetProcessorStatus 获取处理器状态
func (ip *IntegratedProcessor) GetProcessorStatus() map[string]interface{} {
	status := map[string]interface{}{
		"initialized":     ip.IsInitialized(),
		"jieba_available": ip.jiebaProcessor != nil && ip.jiebaProcessor.getGlobalJieba() != nil,
		"spago_available": ip.spagoProcessor != nil,
	}

	if ip.spagoProcessor != nil {
		ip.spagoProcessor.mutex.RLock()
		status["spago_initialized"] = ip.spagoProcessor.initialized
		ip.spagoProcessor.mutex.RUnlock()
	}

	return status
}

// performAdvancedNLPAnalysis 执行高级NLP分析
func (ip *IntegratedProcessor) performAdvancedNLPAnalysis(text string, result *IntegratedResult) {
	log.Printf("   🧠 开始高级NLP分析...")

	// 1. 意图识别
	if ip.intentClassifier != nil {
		intentResult := ip.intentClassifier.ClassifyIntent(text)
		result.Intent = &intentResult
		log.Printf("     意图: %s (%.2f)", result.Intent.Intent, result.Intent.Confidence)
	}

	// 2. 详细情感分析
	if ip.sentimentAnalyzer != nil {
		sentimentResult := ip.sentimentAnalyzer.AnalyzeSentiment(text)
		result.SentimentDetail = &sentimentResult
		result.Sentiment = result.SentimentDetail.Label // 保持向后兼容
		log.Printf("     情感: %s (%.2f)", result.SentimentDetail.Label, result.SentimentDetail.Score)
	}

	// 3. 实体抽取
	if ip.entityExtractor != nil {
		result.ExtractedEntities = ip.entityExtractor.ExtractEntities(text)
		log.Printf("     实体: %d 个", len(result.ExtractedEntities))
	}

	// 4. 上下文分析
	if ip.contextAnalyzer != nil {
		var intentForContext IntentResult
		if result.Intent != nil {
			intentForContext = *result.Intent
		}
		contextResult := ip.contextAnalyzer.AnalyzeContext(text, result.Entities, intentForContext)
		result.Context = &contextResult
		log.Printf("     上下文: %s/%s", result.Context.Domain, result.Context.Topic)
	}

	// 5. 关系抽取
	if ip.relationExtractor != nil {
		result.Relations = ip.relationExtractor.ExtractRelations(text, result.Entities)
		log.Printf("     关系: %d 个", len(result.Relations))
	}

	// 6. 身份识别
	if ip.identityRecognizer != nil {
		identityResult := ip.identityRecognizer.RecognizeIdentity(text)
		result.Identity = &identityResult
		log.Printf("     身份识别: %t (%.2f) [%s]",
			result.Identity.IsIdentityInquiry, result.Identity.Confidence, result.Identity.InquiryType)

		// 如果身份识别置信度很高，可能需要调整意图识别结果
		if result.Identity.IsIdentityInquiry && result.Identity.Confidence > 0.8 && result.Intent != nil {
			if result.Intent.Intent != "identity_inquiry" {
				log.Printf("     🔄 基于身份识别结果调整意图: %s -> identity_inquiry", result.Intent.Intent)
				result.Intent.Intent = "identity_inquiry"
				result.Intent.Category = "social"
				result.Intent.Confidence = result.Identity.Confidence
			}
		}
	}

	// 7. 问候语识别
	if ip.greetingRecognizer != nil {
		greetingResult := ip.greetingRecognizer.RecognizeGreeting(text)
		result.Greeting = &greetingResult
		log.Printf("     问候语识别: %t (%.2f) [%s/%s]",
			result.Greeting.IsGreeting, result.Greeting.Confidence,
			result.Greeting.GreetingType, result.Greeting.TimeContext)

		// 如果问候语识别置信度很高，可能需要调整意图识别结果
		if result.Greeting.IsGreeting && result.Greeting.Confidence > 0.8 && result.Intent != nil {
			if result.Intent.Intent != "greeting" {
				log.Printf("     🔄 基于问候语识别结果调整意图: %s -> greeting", result.Intent.Intent)
				result.Intent.Intent = "greeting"
				result.Intent.Category = "social"
				result.Intent.Confidence = result.Greeting.Confidence
				// 添加问候语特定的参数
				result.Intent.Parameters["greeting_type"] = result.Greeting.GreetingType
				result.Intent.Parameters["time_context"] = result.Greeting.TimeContext
				result.Intent.Parameters["formality"] = result.Greeting.Formality
			}
		}
	}

	// 8. 道歉/错误识别
	if ip.apologyRecognizer != nil {
		apologyResult := ip.apologyRecognizer.RecognizeApology(text)
		result.Apology = &apologyResult
		log.Printf("     道歉/错误识别: %t (%.2f) [%s/%s/%s]",
			result.Apology.IsApologyOrError, result.Apology.Confidence,
			result.Apology.Type, result.Apology.Severity, result.Apology.Target)

		// 如果道歉/错误识别置信度很高，可能需要调整意图识别结果
		if result.Apology.IsApologyOrError && result.Apology.Confidence > 0.8 && result.Intent != nil {
			// 根据不同类型调整意图
			var newIntent string
			switch result.Apology.Type {
			case "apology", "forgiveness":
				newIntent = "apology"
			case "self_error":
				newIntent = "self_correction"
			case "other_error":
				newIntent = "correction"
			case "responsibility":
				newIntent = "responsibility"
			default:
				newIntent = "error_acknowledgment"
			}

			if result.Intent.Intent != newIntent {
				log.Printf("     🔄 基于道歉/错误识别结果调整意图: %s -> %s", result.Intent.Intent, newIntent)
				result.Intent.Intent = newIntent
				result.Intent.Category = "social"
				result.Intent.Confidence = result.Apology.Confidence
				// 添加道歉/错误特定的参数
				result.Intent.Parameters["apology_type"] = result.Apology.Type
				result.Intent.Parameters["severity"] = result.Apology.Severity
				result.Intent.Parameters["target"] = result.Apology.Target
			}
		}
	}

	// 9. 感谢识别
	if ip.gratitudeRecognizer != nil {
		gratitudeResult := ip.gratitudeRecognizer.RecognizeGratitude(text)
		result.Gratitude = &gratitudeResult
		log.Printf("     感谢识别: %t (%.2f) [%s/%s/%s]",
			result.Gratitude.IsGratitude, result.Gratitude.Confidence,
			result.Gratitude.GratitudeType, result.Gratitude.Intensity, result.Gratitude.Formality)

		// 如果感谢识别置信度很高，可能需要调整意图识别结果
		if result.Gratitude.IsGratitude && result.Gratitude.Confidence > 0.8 && result.Intent != nil {
			if result.Intent.Intent != "thanks" {
				log.Printf("     🔄 基于感谢识别结果调整意图: %s -> thanks", result.Intent.Intent)
				result.Intent.Intent = "thanks"
				result.Intent.Category = "social"
				result.Intent.Confidence = result.Gratitude.Confidence
				// 添加感谢特定的参数
				result.Intent.Parameters["gratitude_type"] = result.Gratitude.GratitudeType
				result.Intent.Parameters["intensity"] = result.Gratitude.Intensity
				result.Intent.Parameters["formality"] = result.Gratitude.Formality
			}
		}
	}

	// 10. 计算质量分数
	result.QualityScore = ip.calculateQualityScore(result)

	// 11. 更新处理方法
	if result.Method == "integrated" {
		result.Method = "enhanced"
	} else if result.Method == "jieba" {
		result.Method = "enhanced_jieba"
	} else if result.Method == "spago" {
		result.Method = "enhanced_spago"
	}

	log.Printf("   ✅ 高级NLP分析完成: 质量分数=%.2f", result.QualityScore)
}

// calculateQualityScore 计算质量分数
func (ip *IntegratedProcessor) calculateQualityScore(result *IntegratedResult) float64 {
	score := 0.0

	// 基础分词质量
	if len(result.Tokens) > 0 {
		score += 0.2
	}

	// 关键词提取质量
	if len(result.Keywords) > 0 {
		score += 0.15
	}

	// 实体识别质量
	if len(result.ExtractedEntities) > 0 {
		avgEntityConfidence := 0.0
		for _, entity := range result.ExtractedEntities {
			avgEntityConfidence += entity.Confidence
		}
		avgEntityConfidence /= float64(len(result.ExtractedEntities))
		score += 0.2 * avgEntityConfidence
	}

	// 意图识别质量
	if result.Intent != nil && result.Intent.Confidence > 0 {
		score += 0.15 * result.Intent.Confidence
	}

	// 情感分析质量
	if result.SentimentDetail != nil && result.SentimentDetail.Confidence > 0 {
		score += 0.1 * result.SentimentDetail.Confidence
	}

	// 上下文理解质量
	contextScore := 0.0
	if result.Context != nil {
		if result.Context.Domain != "unknown" {
			contextScore += 0.5
		}
		if result.Context.Topic != "unknown" {
			contextScore += 0.5
		}
	}
	score += 0.08 * contextScore

	// 关系抽取质量
	if len(result.Relations) > 0 {
		avgRelationConfidence := 0.0
		for _, relation := range result.Relations {
			avgRelationConfidence += relation.Confidence
		}
		avgRelationConfidence /= float64(len(result.Relations))
		score += 0.07 * avgRelationConfidence
	}

	// Spago处理质量
	if result.SpagoResult != nil && result.SpagoResult.Confidence > 0 {
		score += 0.1 * result.SpagoResult.Confidence
	}

	return score
}

// performSemanticVectorAnalysis 执行语义向量分析
func (ip *IntegratedProcessor) performSemanticVectorAnalysis(text string, result *IntegratedResult) {
	log.Printf("🔍 执行语义向量分析...")

	// 如果Spago处理器可用，使用其语义向量分析功能
	if ip.spagoProcessor != nil && result.SpagoResult != nil {
		// 从Spago结果中提取语义实体和概念向量
		if len(result.SpagoResult.SemanticEntities) > 0 {
			log.Printf("   提取到 %d 个语义实体", len(result.SpagoResult.SemanticEntities))

			// 将Spago的语义实体转换为集成结果格式
			for _, entity := range result.SpagoResult.SemanticEntities {
				semanticEntity := SemanticEntity{
					Text:       entity.Text,
					Type:       entity.Type,
					Category:   entity.Category,
					Confidence: entity.Confidence,
					Context:    text,
					Vector:     convertFloat64ToFloat32(entity.Vector),
					Attributes: make(map[string]string),
				}

				// 添加属性
				if len(entity.SimilarEntities) > 0 {
					semanticEntity.Attributes["similar_entities"] = strings.Join(entity.SimilarEntities, ",")
				}

				result.SemanticEntities = append(result.SemanticEntities, semanticEntity)
			}
		}

		if len(result.SpagoResult.ConceptVectors) > 0 {
			log.Printf("   提取到 %d 个概念向量", len(result.SpagoResult.ConceptVectors))

			// 将Spago的概念向量转换为集成结果格式
			for _, concept := range result.SpagoResult.ConceptVectors {
				conceptVector := ConceptVector{
					Concept:    concept.Concept,
					Vector:     convertFloat64ToFloat32(concept.Vector),
					Confidence: concept.Confidence,
					Source:     "spago",
				}

				result.ConceptVectors = append(result.ConceptVectors, conceptVector)
			}
		}

		// 计算语义相似度
		if len(result.SpagoResult.SimilarityScores) > 0 {
			maxSimilarity := 0.0
			for _, score := range result.SpagoResult.SimilarityScores {
				if score > maxSimilarity {
					maxSimilarity = score
				}
			}
			result.SemanticSimilarity = maxSimilarity
			log.Printf("   最高语义相似度: %.3f", maxSimilarity)
		}
	} else {
		// 如果Spago不可用，使用基础的语义分析
		ip.performBasicSemanticAnalysis(text, result)
	}

	log.Printf("✅ 语义向量分析完成")
}

// performBasicSemanticAnalysis 执行基础语义分析
func (ip *IntegratedProcessor) performBasicSemanticAnalysis(text string, result *IntegratedResult) {
	log.Printf("   使用基础语义分析...")

	// 基于关键词的简单实体识别
	entities := ip.extractBasicSemanticEntities(text)
	result.SemanticEntities = append(result.SemanticEntities, entities...)

	// 生成基础概念向量
	concepts := ip.generateBasicConceptVectors(text)
	result.ConceptVectors = append(result.ConceptVectors, concepts...)

	result.SemanticSimilarity = 0.5 // 默认相似度
}

// extractBasicSemanticEntities 提取基础语义实体
func (ip *IntegratedProcessor) extractBasicSemanticEntities(text string) []SemanticEntity {
	var entities []SemanticEntity
	textLower := strings.ToLower(text)

	// 预定义的技术实体
	techEntities := map[string]string{
		"go语言":       "programming_language",
		"golang":     "programming_language",
		"python":     "programming_language",
		"java":       "programming_language",
		"javascript": "programming_language",
		"localai":    "ai_framework",
		"mysql":      "database",
		"redis":      "database",
	}

	for entity, category := range techEntities {
		if strings.Contains(textLower, entity) {
			semanticEntity := SemanticEntity{
				Text:       entity,
				Type:       "technology",
				Category:   category,
				Confidence: 0.8,
				Context:    text,
				Vector:     ip.generateBasicVector(entity),
				Attributes: make(map[string]string),
			}
			entities = append(entities, semanticEntity)
		}
	}

	return entities
}

// generateBasicConceptVectors 生成基础概念向量
func (ip *IntegratedProcessor) generateBasicConceptVectors(text string) []ConceptVector {
	var concepts []ConceptVector

	// 基于文本内容生成主要概念
	mainConcept := ip.identifyMainConcept(text)
	if mainConcept != "" {
		conceptVector := ConceptVector{
			Concept:    mainConcept,
			Vector:     ip.generateBasicVector(mainConcept),
			Confidence: 0.7,
			Source:     "basic",
		}
		concepts = append(concepts, conceptVector)
	}

	return concepts
}

// identifyMainConcept 识别主要概念
func (ip *IntegratedProcessor) identifyMainConcept(text string) string {
	textLower := strings.ToLower(text)

	conceptMap := map[string]string{
		"go语言":       "编程语言",
		"golang":     "编程语言",
		"python":     "编程语言",
		"java":       "编程语言",
		"javascript": "编程语言",
		"localai":    "AI框架",
		"mysql":      "数据库",
		"redis":      "数据库",
	}

	for keyword, concept := range conceptMap {
		if strings.Contains(textLower, keyword) {
			return concept
		}
	}

	return "通用概念"
}

// generateBasicVector 生成基础向量
func (ip *IntegratedProcessor) generateBasicVector(text string) []float32 {
	vector := make([]float32, 128)

	// 基于文本哈希生成向量
	hash := 0
	for _, r := range text {
		hash = hash*31 + int(r)
	}

	for i := range vector {
		vector[i] = float32((hash*31+i)%1000) / 1000.0
	}

	return vector
}

// convertFloat64ToFloat32 转换float64切片为float32切片
func convertFloat64ToFloat32(input []float64) []float32 {
	output := make([]float32, len(input))
	for i, v := range input {
		output[i] = float32(v)
	}
	return output
}

// ProcessTextWithContext 处理带上下文的文本
func (ip *IntegratedProcessor) ProcessTextWithContext(text string, history *ConversationHistory) *ContextEnhancedResult {
	startTime := time.Now()

	// 首先进行基础的文本处理
	baseResult := ip.ProcessText(text)

	// 创建上下文增强结果
	result := &ContextEnhancedResult{
		IntegratedResult:  baseResult,
		EnhancedKeywords:  baseResult.Keywords, // 初始化为基础关键词
		EnhancedIntent:    baseResult.Intent,   // 初始化为基础意图
		ContextConfidence: 0.0,
	}

	// 如果没有历史记录，返回基础结果
	if history == nil || len(history.Messages) == 0 {
		log.Printf("🔍 无上下文历史，使用基础NLP处理结果")
		return result
	}

	log.Printf("🧠 开始上下文感知NLP处理，历史消息数: %d", len(history.Messages))

	// 分析对话上下文
	contextAnalysis := ip.analyzeConversationContext(history, text)
	result.ContextAnalysis = contextAnalysis

	// 基于上下文增强关键词提取
	result.EnhancedKeywords = ip.enhanceKeywordsWithContext(baseResult.Keywords, contextAnalysis)

	// 基于上下文增强意图识别
	result.EnhancedIntent = ip.enhanceIntentWithContext(baseResult.Intent, contextAnalysis, text)

	// 计算上下文理解置信度
	result.ContextConfidence = ip.calculateContextConfidence(contextAnalysis, text)

	// 更新处理时间
	result.ProcessingTime = time.Since(startTime)

	log.Printf("✅ 上下文感知NLP处理完成，置信度: %.3f", result.ContextConfidence)

	return result
}

// analyzeConversationContext 分析对话上下文
func (ip *IntegratedProcessor) analyzeConversationContext(history *ConversationHistory, currentText string) *ContextAnalysisResult {
	analysis := &ContextAnalysisResult{
		RelatedKeywords:   []string{},
		PreviousIntents:   []string{},
		IsFollowUp:        false,
		IsIncompleteInput: false,
	}

	// 分析最近的对话消息（最多分析最近5轮对话）
	messageCount := 0
	recentMessages := []HistoryMessage{}

	for i := len(history.Messages) - 1; i >= 0 && messageCount < 10; i-- {
		msg := history.Messages[i]
		recentMessages = append([]HistoryMessage{msg}, recentMessages...)
		messageCount++
	}

	// 提取主要话题和技术领域
	for _, msg := range recentMessages {
		if msg.Role == "user" && len(msg.Content) > 10 {
			// 设置主要话题（使用最长的用户输入）
			if analysis.MainTopic == "" || len(msg.Content) > len(analysis.MainTopic) {
				analysis.MainTopic = msg.Content
			}

			// 检测技术领域
			if analysis.TechDomain == "" {
				analysis.TechDomain = ip.detectTechDomainFromText(msg.Content)
			}

			// 提取关键词
			if ip.jiebaProcessor != nil {
				keywords := ip.jiebaProcessor.ExtractKeywords(msg.Content, 5)
				for _, kw := range keywords {
					analysis.RelatedKeywords = append(analysis.RelatedKeywords, kw.Word)
				}
			}
		}

		// 收集之前的意图
		if msg.Intent != "" {
			analysis.PreviousIntents = append(analysis.PreviousIntents, msg.Intent)
		}
	}

	// 去重关键词
	analysis.RelatedKeywords = ip.deduplicateStrings(analysis.RelatedKeywords)

	// 判断是否是追问
	analysis.IsFollowUp = ip.isFollowUpQuery(currentText, analysis)

	// 判断是否是不完整输入
	analysis.IsIncompleteInput = ip.isIncompleteInput(currentText)

	// 分析对话流向
	analysis.ConversationFlow = ip.analyzeConversationFlow(recentMessages, currentText)

	return analysis
}

// detectTechDomainFromText 从文本中检测技术领域
func (ip *IntegratedProcessor) detectTechDomainFromText(text string) string {
	textLower := strings.ToLower(text)

	// 技术领域关键词映射
	techDomains := map[string][]string{
		"前端开发": {"react", "vue", "javascript", "html", "css", "前端", "组件", "dom", "ui", "界面"},
		"后端开发": {"go", "python", "java", "node", "服务器", "api", "后端", "微服务", "golang"},
		"数据库":  {"mysql", "redis", "数据库", "sql", "查询", "索引", "表", "mongodb", "postgresql"},
		"AI技术": {"ai", "机器学习", "深度学习", "nlp", "向量", "模型", "算法", "神经网络"},
		"运维部署": {"docker", "kubernetes", "部署", "运维", "服务器", "云服务", "k8s", "容器"},
		"移动开发": {"android", "ios", "flutter", "react native", "移动", "app", "应用"},
		"数据科学": {"数据分析", "pandas", "numpy", "matplotlib", "数据挖掘", "统计"},
	}

	for domain, keywords := range techDomains {
		for _, keyword := range keywords {
			if strings.Contains(textLower, keyword) {
				return domain
			}
		}
	}

	return ""
}

// enhanceKeywordsWithContext 基于上下文增强关键词
func (ip *IntegratedProcessor) enhanceKeywordsWithContext(baseKeywords []string, context *ContextAnalysisResult) []string {
	enhanced := make([]string, len(baseKeywords))
	copy(enhanced, baseKeywords)

	// 添加上下文相关的关键词
	for _, keyword := range context.RelatedKeywords {
		if !ip.containsString(enhanced, keyword) {
			enhanced = append(enhanced, keyword)
		}
	}

	// 如果有技术领域，添加相关术语
	if context.TechDomain != "" {
		domainKeywords := ip.getTechDomainKeywords(context.TechDomain)
		for _, keyword := range domainKeywords {
			if !ip.containsString(enhanced, keyword) {
				enhanced = append(enhanced, keyword)
			}
		}
	}

	return enhanced
}

// enhanceIntentWithContext 基于上下文增强意图识别
func (ip *IntegratedProcessor) enhanceIntentWithContext(baseIntent *IntentResult, context *ContextAnalysisResult, text string) *IntentResult {
	if baseIntent == nil {
		baseIntent = &IntentResult{
			Intent:     "unknown",
			Confidence: 0.0,
		}
	}

	enhanced := &IntentResult{
		Intent:     baseIntent.Intent,
		Confidence: baseIntent.Confidence,
		Metadata:   make(map[string]interface{}),
	}

	// 复制原有元数据
	if baseIntent.Metadata != nil {
		for k, v := range baseIntent.Metadata {
			enhanced.Metadata[k] = v
		}
	}

	// 基于上下文调整意图
	if context.IsFollowUp {
		enhanced.Intent = "follow_up_question"
		enhanced.Confidence = enhanced.Confidence*0.8 + 0.2 // 提高置信度
		enhanced.Metadata["is_follow_up"] = true
	}

	if context.IsIncompleteInput {
		enhanced.Intent = "incomplete_input"
		enhanced.Confidence = enhanced.Confidence*0.7 + 0.3
		enhanced.Metadata["is_incomplete"] = true
	}

	// 基于技术领域调整意图
	if context.TechDomain != "" {
		enhanced.Intent = "technical_question"
		enhanced.Confidence = enhanced.Confidence*0.9 + 0.1
		enhanced.Metadata["tech_domain"] = context.TechDomain
	}

	return enhanced
}

// calculateContextConfidence 计算上下文理解置信度
func (ip *IntegratedProcessor) calculateContextConfidence(context *ContextAnalysisResult, text string) float64 {
	confidence := 0.0

	// 基础置信度
	if context.MainTopic != "" {
		confidence += 0.3
	}

	if context.TechDomain != "" {
		confidence += 0.2
	}

	if len(context.RelatedKeywords) > 0 {
		confidence += 0.2
	}

	if len(context.PreviousIntents) > 0 {
		confidence += 0.1
	}

	// 特殊情况加分
	if context.IsFollowUp {
		confidence += 0.1
	}

	if context.IsIncompleteInput {
		confidence += 0.1
	}

	// 确保置信度在0-1之间
	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// deduplicateStrings 去重字符串切片
func (ip *IntegratedProcessor) deduplicateStrings(input []string) []string {
	seen := make(map[string]bool)
	result := []string{}

	for _, str := range input {
		str = strings.TrimSpace(str)
		if str != "" && !seen[str] {
			seen[str] = true
			result = append(result, str)
		}
	}

	return result
}

// isFollowUpQuery 判断是否是追问
func (ip *IntegratedProcessor) isFollowUpQuery(text string, context *ContextAnalysisResult) bool {
	textLower := strings.ToLower(text)

	// 检查追问关键词
	followUpKeywords := []string{
		"怎么", "如何", "为什么", "什么", "哪个", "哪些",
		"详细", "具体", "更多", "还有", "另外", "其他",
		"例子", "示例", "代码", "实现", "配置", "设置",
		"继续", "然后", "接下来", "下一步",
	}

	for _, keyword := range followUpKeywords {
		if strings.Contains(textLower, keyword) {
			return true
		}
	}

	// 检查是否与上下文关键词相关
	for _, keyword := range context.RelatedKeywords {
		if strings.Contains(textLower, strings.ToLower(keyword)) {
			return true
		}
	}

	return false
}

// isIncompleteInput 判断是否是不完整输入
func (ip *IntegratedProcessor) isIncompleteInput(text string) bool {
	text = strings.TrimSpace(text)

	// 长度检查
	if len(text) <= 3 {
		return true
	}

	// 只包含字母数字的短输入
	if len(text) <= 15 && isAlphanumericOnly(text) {
		return true
	}

	// 检查是否是技术缩写或版本号
	if ip.isTechnicalAbbreviation(text) {
		return true
	}

	return false
}

// analyzeConversationFlow 分析对话流向
func (ip *IntegratedProcessor) analyzeConversationFlow(messages []HistoryMessage, currentText string) string {
	if len(messages) == 0 {
		return "initial"
	}

	// 分析最近的消息模式
	userMessages := []string{}
	for _, msg := range messages {
		if msg.Role == "user" {
			userMessages = append(userMessages, msg.Content)
		}
	}

	if len(userMessages) == 0 {
		return "initial"
	}

	// 检查是否是深入探讨
	if len(userMessages) >= 3 {
		return "deep_discussion"
	}

	// 检查是否是快速问答
	if len(userMessages) >= 2 {
		lastMsg := userMessages[len(userMessages)-1]
		if len(lastMsg) < 20 && len(currentText) < 20 {
			return "quick_qa"
		}
	}

	return "normal_conversation"
}

// getTechDomainKeywords 获取技术领域相关关键词
func (ip *IntegratedProcessor) getTechDomainKeywords(domain string) []string {
	domainKeywords := map[string][]string{
		"前端开发": {"组件", "状态", "路由", "样式", "事件"},
		"后端开发": {"接口", "服务", "数据库", "缓存", "认证"},
		"数据库":  {"查询", "索引", "事务", "优化", "备份"},
		"AI技术": {"训练", "推理", "特征", "数据集", "评估"},
		"运维部署": {"监控", "日志", "性能", "扩容", "备份"},
		"移动开发": {"界面", "性能", "适配", "发布", "测试"},
		"数据科学": {"清洗", "可视化", "建模", "预测", "分析"},
	}

	if keywords, exists := domainKeywords[domain]; exists {
		return keywords
	}

	return []string{}
}

// containsString 检查字符串切片是否包含指定字符串
func (ip *IntegratedProcessor) containsString(slice []string, item string) bool {
	for _, s := range slice {
		if s == item {
			return true
		}
	}
	return false
}

// isTechnicalAbbreviation 检查是否是技术缩写
func (ip *IntegratedProcessor) isTechnicalAbbreviation(text string) bool {
	textLower := strings.ToLower(text)

	// 常见技术缩写
	abbreviations := []string{
		"js", "ts", "py", "go", "db", "api", "ui", "css", "html",
		"sql", "orm", "mvc", "spa", "pwa", "cdn", "dns", "http",
		"tcp", "udp", "ssh", "ftp", "git", "svn", "ide", "sdk",
	}

	for _, abbr := range abbreviations {
		if textLower == abbr {
			return true
		}
	}

	return false
}

// isAlphanumericOnly 检查字符串是否只包含字母和数字
func isAlphanumericOnly(s string) bool {
	for _, r := range s {
		if !((r >= 'a' && r <= 'z') || (r >= 'A' && r <= 'Z') || (r >= '0' && r <= '9')) {
			return false
		}
	}
	return true
}
