package logger

import (
	"log"
	"sync"
	"time"
)

// LogEntry 日志条目
type LogEntry struct {
	Timestamp time.Time              `json:"timestamp"`
	Level     string                 `json:"level"`
	Icon      string                 `json:"icon"`
	Message   string                 `json:"message"`
	Metadata  map[string]interface{} `json:"metadata,omitempty"`
}

// CrawlerLogger 爬虫日志收集器
type CrawlerLogger struct {
	logs      []LogEntry
	maxLogs   int
	mutex     sync.RWMutex
	broadcast func(*LogEntry) // WebSocket广播函数
}

// 全局日志收集器实例
var globalCrawlerLogger *CrawlerLogger
var once sync.Once

// GetCrawlerLogger 获取全局爬虫日志收集器
func GetCrawlerLogger() *CrawlerLogger {
	once.Do(func() {
		globalCrawlerLogger = &CrawlerLogger{
			logs:    make([]LogEntry, 0),
			maxLogs: 1000,
		}
	})
	return globalCrawlerLogger
}

// SetBroadcastFunc 设置WebSocket广播函数
func (cl *CrawlerLogger) SetBroadcastFunc(broadcast func(*LogEntry)) {
	cl.mutex.Lock()
	defer cl.mutex.Unlock()
	cl.broadcast = broadcast
}

// LogInfo 记录信息日志
func (cl *CrawlerLogger) LogInfo(message, icon string) {
	cl.addLog("info", icon, message, nil)
}

// LogSuccess 记录成功日志
func (cl *CrawlerLogger) LogSuccess(message, icon string) {
	cl.addLog("success", icon, message, nil)
}

// LogWarning 记录警告日志
func (cl *CrawlerLogger) LogWarning(message, icon string) {
	cl.addLog("warning", icon, message, nil)
}

// LogError 记录错误日志
func (cl *CrawlerLogger) LogError(message, icon string) {
	cl.addLog("error", icon, message, nil)
}

// LogDebug 记录调试日志
func (cl *CrawlerLogger) LogDebug(message, icon string) {
	cl.addLog("debug", icon, message, nil)
}

// addLog 添加日志条目
func (cl *CrawlerLogger) addLog(level, icon, message string, metadata map[string]interface{}) {
	cl.mutex.Lock()

	entry := LogEntry{
		Timestamp: time.Now(),
		Level:     level,
		Icon:      icon,
		Message:   message,
		Metadata:  metadata,
	}

	cl.logs = append(cl.logs, entry)

	// 限制日志数量
	if len(cl.logs) > cl.maxLogs {
		cl.logs = cl.logs[len(cl.logs)-cl.maxLogs:]
	}

	// 获取广播函数的副本
	broadcast := cl.broadcast
	cl.mutex.Unlock()

	// 同时输出到标准日志
	log.Printf("%s %s", icon, message)

	// 通过WebSocket广播日志（在锁外执行避免阻塞）
	if broadcast != nil {
		go broadcast(&entry)
	}
}

// GetRecentLogs 获取最近的日志
func (cl *CrawlerLogger) GetRecentLogs(limit int) []LogEntry {
	cl.mutex.RLock()
	defer cl.mutex.RUnlock()

	if limit <= 0 || limit > len(cl.logs) {
		limit = len(cl.logs)
	}

	// 返回最近的日志
	start := len(cl.logs) - limit
	if start < 0 {
		start = 0
	}

	result := make([]LogEntry, limit)
	copy(result, cl.logs[start:])
	return result
}

// Clear 清空日志
func (cl *CrawlerLogger) Clear() {
	cl.mutex.Lock()
	defer cl.mutex.Unlock()
	cl.logs = cl.logs[:0]
}

// Count 获取日志总数
func (cl *CrawlerLogger) Count() int {
	cl.mutex.RLock()
	defer cl.mutex.RUnlock()
	return len(cl.logs)
}

// 便捷函数，直接使用全局实例
func CrawlerLogInfo(message, icon string) {
	GetCrawlerLogger().LogInfo(message, icon)
}

func CrawlerLogSuccess(message, icon string) {
	GetCrawlerLogger().LogSuccess(message, icon)
}

func CrawlerLogWarning(message, icon string) {
	GetCrawlerLogger().LogWarning(message, icon)
}

func CrawlerLogError(message, icon string) {
	GetCrawlerLogger().LogError(message, icon)
}

func CrawlerLogDebug(message, icon string) {
	GetCrawlerLogger().LogDebug(message, icon)
}
