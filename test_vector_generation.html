<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试向量生成功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
    </style>
</head>
<body>
    <h1>🧮 向量生成功能测试</h1>
    
    <div class="test-section">
        <h2>📝 创建知识并测试向量生成</h2>
        <div class="form-group">
            <label for="testQuestion">测试问题:</label>
            <input type="text" id="testQuestion" value="什么是React？" placeholder="输入问题">
        </div>
        <div class="form-group">
            <label for="testAnswer">测试答案:</label>
            <textarea id="testAnswer" placeholder="输入答案">React是一个用于构建用户界面的JavaScript库，由Facebook开发。它采用组件化的开发方式，使用虚拟DOM来提高性能。</textarea>
        </div>
        <button class="test-button" onclick="testCreateKnowledgeWithVector()">创建知识并生成向量</button>
        <div id="create-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 检查向量存储</h2>
        <button class="test-button" onclick="checkVectorStorage()">检查向量存储情况</button>
        <div id="vector-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 统计信息</h2>
        <button class="test-button" onclick="getStatistics()">获取统计信息</button>
        <div id="stats-result" class="result"></div>
    </div>

    <script>
        const baseURL = 'http://localhost:8082';
        let lastCreatedId = null;

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 测试创建知识并生成向量
        async function testCreateKnowledgeWithVector() {
            const resultDiv = 'create-result';
            const question = document.getElementById('testQuestion').value.trim();
            const answer = document.getElementById('testAnswer').value.trim();
            
            if (!question || !answer) {
                showResult(resultDiv, '❌ 请填写问题和答案', 'error');
                return;
            }
            
            showResult(resultDiv, '正在创建知识并生成向量...', 'info');
            
            const testData = {
                question: question,
                answer: answer,
                category: "technology",
                keywords: ["React", "JavaScript", "前端", "组件"],
                confidence: 0.9,
                source: "user_input",
                learned_from: "vector_test"
            };

            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    lastCreatedId = result.data.id;
                    showResult(resultDiv, `✅ 创建成功！\nID: ${lastCreatedId}\n\n完整响应:\n${JSON.stringify(result, null, 2)}`, 'success');
                    
                    // 等待一秒后检查向量是否生成
                    setTimeout(() => {
                        checkSpecificVector(lastCreatedId);
                    }, 1000);
                } else {
                    showResult(resultDiv, `❌ 创建失败：${result.message || '未知错误'}\n\n响应:\n${JSON.stringify(result, null, 2)}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 检查特定知识的向量
        async function checkSpecificVector(knowledgeId) {
            const resultDiv = 'create-result';
            const currentResult = document.getElementById(resultDiv).textContent;
            
            try {
                // 这里我们需要一个API来检查向量，暂时使用数据库查询的方式
                showResult(resultDiv, currentResult + '\n\n🔍 正在检查向量生成情况...', 'info');
                
                // 由于没有直接的向量查询API，我们通过获取知识详情来间接验证
                const response = await fetch(`${baseURL}/api/learning/knowledge/${knowledgeId}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(resultDiv, currentResult + '\n\n✅ 知识创建成功，向量应该已经在后台生成\n请查看服务器日志确认向量生成状态', 'success');
                } else {
                    showResult(resultDiv, currentResult + '\n\n❌ 无法验证向量生成状态', 'error');
                }
            } catch (error) {
                showResult(resultDiv, currentResult + '\n\n❌ 检查向量时出错：' + error.message, 'error');
            }
        }

        // 检查向量存储情况
        async function checkVectorStorage() {
            const resultDiv = 'vector-result';
            showResult(resultDiv, '正在检查向量存储情况...', 'info');
            
            try {
                // 获取最近的知识列表
                const response = await fetch(`${baseURL}/api/learning/knowledge?limit=5`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    let message = `✅ 获取到 ${result.data.length} 条知识记录\n\n`;
                    
                    result.data.forEach((item, index) => {
                        message += `${index + 1}. ID: ${item.id}\n`;
                        message += `   问题: ${item.question}\n`;
                        message += `   状态: ${item.status}\n`;
                        message += `   创建时间: ${item.created_at}\n\n`;
                    });
                    
                    message += '\n📝 注意: 向量数据存储在 knowledge_vectors 表中\n';
                    message += '请查看服务器日志确认向量生成和存储的详细信息';
                    
                    showResult(resultDiv, message, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取知识列表失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 获取统计信息
        async function getStatistics() {
            const resultDiv = 'stats-result';
            showResult(resultDiv, '正在获取统计信息...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/statistics`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    let message = '✅ 统计信息获取成功！\n\n';
                    message += `总知识数: ${result.data.total || 0}\n`;
                    message += `已批准: ${result.data.approved || 0}\n`;
                    message += `已向量化: ${result.data.vectorized || 0}\n`;
                    message += `待审核: ${result.data.pending || 0}\n\n`;
                    
                    if (result.data.categories) {
                        message += '按分类统计:\n';
                        Object.entries(result.data.categories).forEach(([category, count]) => {
                            message += `  ${category}: ${count}\n`;
                        });
                    }
                    
                    message += '\n完整响应:\n' + JSON.stringify(result, null, 2);
                    
                    showResult(resultDiv, message, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取统计信息失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 页面加载时自动获取统计信息
        window.addEventListener('load', function() {
            getStatistics();
        });
    </script>
</body>
</html>
