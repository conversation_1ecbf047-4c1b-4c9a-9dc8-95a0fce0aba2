# 数据模板功能实现说明

## 🎯 功能概述

基于NCP处理器的强大能力，新增了数据模板功能，通过数据模板匹配识别出用户想实现什么，并通过对话完成模板任务。

## 🏗️ 核心组件

### 1. 数据模板系统 (`internal/template/data_template.go`)

#### 主要结构体：
- **DataTemplateSystem**: 数据模板系统主类
- **DataTemplate**: 模板定义，包含字段、SQL模板、数据库配置等
- **TemplateField**: 模板字段定义，支持多种数据类型
- **TemplateSession**: 模板会话，跟踪数据收集进度
- **TemplateResponse**: 模板响应，包含不同类型的响应

#### 核心功能：
- **模板匹配**: 基于关键词和NLP分析匹配合适的模板
- **数据收集**: 通过对话逐步收集所需字段数据
- **SQL生成**: 根据收集的数据生成相应的SQL语句
- **多数据库支持**: 可配置连接到不同的MySQL数据库

### 2. 内置模板

#### 车场配置模板 (`parking_config`)
用于配置停车场的区域、车道和相机信息：
- 车场名称
- 区域类型（仅外场/内外场都有）
- 区域数量
- 每个区域的车道数
- 每车道相机数
- 附加信息

**示例对话流程：**
```
用户: "我想配置车场"
系统: "开始配置：车场配置。请输入车场名称（例如：万达广场停车场）"
用户: "万达广场停车场"
系统: "已记录车场名称: 万达广场停车场。请选择车场区域类型：1) 仅外场  2) 内外场都有"
用户: "2"
系统: "已记录区域类型: 内外场都有。请输入车场总共有多少个区域（数字）"
...
```

#### 用户管理模板 (`user_management`)
用于创建和管理系统用户信息：
- 用户名
- 真实姓名
- 邮箱
- 用户角色（管理员/操作员/查看者/审核员）
- 部门

#### 设备配置模板 (`device_config`)
用于配置各种设备信息：
- 设备名称
- 设备类型（摄像头/传感器/服务器等）
- 安装位置
- IP地址

### 3. 集成到聊天系统

在 `internal/rag/chat.go` 中集成了数据模板功能：
- 添加了 `templateSystem` 字段到 `ChatSystem`
- 在意图处理中添加了 `data_template`、`config_request`、`setup_request` 等意图
- 实现了 `handleDataTemplate` 方法处理模板请求

### 4. 数据库支持

在 `internal/database/init.go` 中添加了相关表结构：
- `data_templates`: 模板定义表
- `template_sessions`: 模板会话表
- `template_executions`: 模板执行历史表
- `parking_config`, `parking_areas`, `parking_lanes`: 车场相关业务表
- `users`: 用户表
- `devices`: 设备表

## 🚀 使用方式

### 1. 车场配置示例
```
用户输入: "我想新增车场配置"
系统响应: 识别为车场配置模板，开始引导用户输入相关信息
```

### 2. 用户管理示例
```
用户输入: "新增用户"
系统响应: 识别为用户管理模板，开始收集用户信息
```

### 3. 设备配置示例
```
用户输入: "设备配置"
系统响应: 识别为设备配置模板，开始收集设备信息
```

## 🔧 技术特性

### 1. 智能匹配
- 基于关键词匹配
- 结合NLP语义分析
- 支持模糊匹配和意图识别

### 2. 类型验证
- 字符串、整数、浮点数、布尔值
- 选择类型（单选/多选）
- 自定义验证规则

### 3. 会话管理
- 支持会话暂停和恢复
- 数据收集进度跟踪
- 错误处理和重试机制

### 4. SQL生成
- 动态SQL模板
- 参数化查询防止注入
- 支持复杂的关联插入

### 5. 多数据库支持
- 可配置不同的数据库连接
- 支持跨数据库操作
- 事务支持

## 📊 执行流程

1. **意图识别**: 用户输入 → NLP分析 → 模板匹配
2. **会话创建**: 创建模板会话，开始数据收集
3. **数据收集**: 逐步收集所需字段，支持验证和重试
4. **SQL生成**: 根据模板和数据生成SQL语句
5. **执行结果**: 返回执行结果和生成的SQL

## 🎨 扩展性

### 添加新模板
1. 在 `initializeBuiltinTemplates` 中定义新模板
2. 在 `generateSQL` 中添加对应的SQL生成逻辑
3. 在数据库中创建相应的业务表

### 自定义字段类型
1. 在 `TemplateField` 中添加新的类型
2. 在 `validateAndProcessInput` 中添加验证逻辑

### 集成外部系统
1. 修改 `executeTemplate` 方法
2. 添加外部API调用
3. 支持更复杂的业务逻辑

## ✅ 实现状态

- ✅ 数据模板系统核心框架
- ✅ 三个内置模板（车场、用户、设备）
- ✅ 智能匹配和数据收集
- ✅ SQL生成和数据库集成
- ✅ 聊天系统集成
- ✅ 数据库表结构
- 🔄 实际SQL执行（当前为模拟）
- 🔄 Web界面集成测试

## 🔮 后续优化

1. **实际数据库执行**: 连接到真实的业务数据库执行SQL
2. **模板管理界面**: 提供可视化的模板管理功能
3. **更多业务模板**: 根据实际需求添加更多模板
4. **高级验证**: 支持更复杂的数据验证规则
5. **批量操作**: 支持批量数据处理
6. **审批流程**: 集成审批机制
7. **数据导入导出**: 支持Excel等格式的数据导入导出

这个数据模板功能为FAQ系统提供了强大的数据配置能力，用户可以通过自然语言对话的方式完成复杂的数据配置任务，大大提升了系统的易用性和实用性。
