package nlp

import (
	"fmt"
	"log"
	"math"
	"strings"
	"sync"
	"time"
)

// ConversationManager 智能对话管理器
type ConversationManager struct {
	integratedProcessor *IntegratedProcessor
	vectorSearch        *EnhancedVectorSearch
	sessions            map[string]*ConversationSession
	sessionTimeout      time.Duration
	maxSessions         int
	mutex               sync.RWMutex
	initialized         bool
}

// ConversationSession 对话会话
type ConversationSession struct {
	ID           string    `json:"id"`
	UserID       string    `json:"user_id"`
	StartTime    time.Time `json:"start_time"`
	LastActivity time.Time `json:"last_activity"`
	MessageCount int       `json:"message_count"`

	// 对话历史
	Messages []ConversationMessage `json:"messages"`

	// 上下文信息
	Context *SessionContext `json:"context"`

	// 用户画像
	UserProfile *UserProfile `json:"user_profile"`

	// 会话状态
	State         string   `json:"state"`
	CurrentTopic  string   `json:"current_topic"`
	IntentHistory []string `json:"intent_history"`

	// 个性化设置
	Preferences map[string]interface{} `json:"preferences"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// ConversationMessage 对话消息
type ConversationMessage struct {
	ID        string    `json:"id"`
	Type      string    `json:"type"` // user, assistant, system
	Content   string    `json:"content"`
	Timestamp time.Time `json:"timestamp"`

	// NLP分析结果
	Analysis *MessageAnalysis `json:"analysis"`

	// 响应信息
	Response *MessageResponse `json:"response,omitempty"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// MessageAnalysis 消息分析
type MessageAnalysis struct {
	Intent    *IntentResult     `json:"intent"`
	Sentiment *SentimentResult  `json:"sentiment"`
	Entities  []ExtractedEntity `json:"entities"`
	Context   *ContextResult    `json:"context"`
	Relations []Relation        `json:"relations"`

	// 对话特征
	TopicShift      bool    `json:"topic_shift"`
	EmotionChange   bool    `json:"emotion_change"`
	ComplexityLevel float64 `json:"complexity_level"`
	UrgencyLevel    float64 `json:"urgency_level"`

	// 个性化特征
	UserStyle       string `json:"user_style"`
	PreferredFormat string `json:"preferred_format"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// MessageResponse 消息响应
type MessageResponse struct {
	Content    string  `json:"content"`
	Type       string  `json:"type"`
	Confidence float64 `json:"confidence"`

	// 响应策略
	Strategy string `json:"strategy"`
	Tone     string `json:"tone"`
	Format   string `json:"format"`

	// 建议和推荐
	Suggestions       []string `json:"suggestions"`
	RelatedTopics     []string `json:"related_topics"`
	FollowUpQuestions []string `json:"follow_up_questions"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// SessionContext 会话上下文
type SessionContext struct {
	// 当前话题
	CurrentTopic string            `json:"current_topic"`
	TopicHistory []TopicTransition `json:"topic_history"`

	// 情感状态
	EmotionalState string              `json:"emotional_state"`
	EmotionHistory []EmotionTransition `json:"emotion_history"`

	// 知识状态
	KnowledgeLevel float64  `json:"knowledge_level"`
	LearningGoals  []string `json:"learning_goals"`

	// 对话模式
	ConversationMode string `json:"conversation_mode"` // casual, formal, technical, educational
	InteractionStyle string `json:"interaction_style"` // direct, exploratory, supportive

	// 上下文记忆
	ShortTermMemory []ContextItem `json:"short_term_memory"`
	LongTermMemory  []ContextItem `json:"long_term_memory"`

	// 元数据
	Metadata map[string]interface{} `json:"metadata"`
}

// UserProfile 用户画像
type UserProfile struct {
	// 基础信息
	UserID   string `json:"user_id"`
	Name     string `json:"name"`
	Language string `json:"language"`
	Timezone string `json:"timezone"`

	// 技能水平
	TechnicalLevel  float64            `json:"technical_level"`
	DomainExpertise map[string]float64 `json:"domain_expertise"`

	// 学习偏好
	LearningStyle  string `json:"learning_style"`  // visual, auditory, kinesthetic, reading
	PreferredPace  string `json:"preferred_pace"`  // slow, normal, fast
	PreferredDepth string `json:"preferred_depth"` // overview, detailed, comprehensive

	// 交互偏好
	CommunicationStyle string `json:"communication_style"` // formal, casual, technical
	PreferredFormat    string `json:"preferred_format"`    // text, bullet_points, examples

	// 历史统计
	TotalSessions      int           `json:"total_sessions"`
	TotalMessages      int           `json:"total_messages"`
	AverageSessionTime time.Duration `json:"average_session_time"`
	TopTopics          []string      `json:"top_topics"`

	// 个性化设置
	Preferences map[string]interface{} `json:"preferences"`

	// 元数据
	Metadata    map[string]interface{} `json:"metadata"`
	LastUpdated time.Time              `json:"last_updated"`
}

// TopicTransition 话题转换
type TopicTransition struct {
	FromTopic  string    `json:"from_topic"`
	ToTopic    string    `json:"to_topic"`
	Timestamp  time.Time `json:"timestamp"`
	Trigger    string    `json:"trigger"`
	Confidence float64   `json:"confidence"`
}

// EmotionTransition 情感转换
type EmotionTransition struct {
	FromEmotion string    `json:"from_emotion"`
	ToEmotion   string    `json:"to_emotion"`
	Timestamp   time.Time `json:"timestamp"`
	Trigger     string    `json:"trigger"`
	Intensity   float64   `json:"intensity"`
}

// ContextItem 上下文项
type ContextItem struct {
	Type       string                 `json:"type"`
	Content    string                 `json:"content"`
	Importance float64                `json:"importance"`
	Timestamp  time.Time              `json:"timestamp"`
	ExpiryTime time.Time              `json:"expiry_time"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// NewConversationManager 创建对话管理器
func NewConversationManager(processor *IntegratedProcessor, vectorSearch *EnhancedVectorSearch) *ConversationManager {
	log.Printf("💬 初始化智能对话管理器...")

	cm := &ConversationManager{
		integratedProcessor: processor,
		vectorSearch:        vectorSearch,
		sessions:            make(map[string]*ConversationSession),
		sessionTimeout:      30 * time.Minute,
		maxSessions:         1000,
		initialized:         false,
	}

	// 异步初始化
	go cm.initializeAsync()

	return cm
}

// initializeAsync 异步初始化
func (cm *ConversationManager) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 对话管理器初始化失败: %v", r)
			cm.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化对话管理组件...")

	// 等待依赖组件初始化完成
	for i := 0; i < 30; i++ {
		if cm.integratedProcessor != nil && cm.integratedProcessor.IsInitialized() {
			break
		}
		time.Sleep(1 * time.Second)
		log.Printf("⏳ 等待NLP处理器初始化... (%d/30)", i+1)
	}

	// 启动会话清理任务
	go cm.startSessionCleanup()

	cm.mutex.Lock()
	cm.initialized = true
	cm.mutex.Unlock()

	log.Printf("✅ 智能对话管理器初始化完成")
}

// IsInitialized 检查是否已初始化
func (cm *ConversationManager) IsInitialized() bool {
	cm.mutex.RLock()
	defer cm.mutex.RUnlock()
	return cm.initialized
}

// StartSession 开始新会话
func (cm *ConversationManager) StartSession(userID string) (*ConversationSession, error) {
	sessionID := fmt.Sprintf("session_%s_%d", userID, time.Now().Unix())

	log.Printf("💬 开始新对话会话: %s (用户: %s)", sessionID, userID)

	session := &ConversationSession{
		ID:            sessionID,
		UserID:        userID,
		StartTime:     time.Now(),
		LastActivity:  time.Now(),
		MessageCount:  0,
		Messages:      []ConversationMessage{},
		Context:       cm.createInitialContext(),
		UserProfile:   cm.loadOrCreateUserProfile(userID),
		State:         "active",
		CurrentTopic:  "general",
		IntentHistory: []string{},
		Preferences:   make(map[string]interface{}),
		Metadata:      make(map[string]interface{}),
	}

	cm.mutex.Lock()
	// 检查会话数量限制
	if len(cm.sessions) >= cm.maxSessions {
		cm.cleanupOldestSession()
	}
	cm.sessions[sessionID] = session
	cm.mutex.Unlock()

	log.Printf("✅ 对话会话创建成功: %s", sessionID)

	return session, nil
}

// ProcessMessage 处理消息
func (cm *ConversationManager) ProcessMessage(sessionID, content string) (*ConversationMessage, error) {
	cm.mutex.RLock()
	session, exists := cm.sessions[sessionID]
	cm.mutex.RUnlock()

	if !exists {
		return nil, fmt.Errorf("会话不存在: %s", sessionID)
	}

	log.Printf("💬 处理消息: 会话=%s, 内容=%s", sessionID, content[:min(len(content), 50)])

	// 创建用户消息
	userMessage := ConversationMessage{
		ID:        fmt.Sprintf("msg_%d", time.Now().UnixNano()),
		Type:      "user",
		Content:   content,
		Timestamp: time.Now(),
		Metadata:  make(map[string]interface{}),
	}

	// 分析消息
	analysis, err := cm.analyzeMessage(content, session)
	if err != nil {
		log.Printf("⚠️ 消息分析失败: %v", err)
	} else {
		userMessage.Analysis = analysis
	}

	// 生成响应
	response, err := cm.generateResponse(content, analysis, session)
	if err != nil {
		log.Printf("⚠️ 响应生成失败: %v", err)
		response = &MessageResponse{
			Content:    "抱歉，我现在无法处理您的请求。请稍后再试。",
			Type:       "error",
			Confidence: 0.1,
			Strategy:   "fallback",
			Tone:       "apologetic",
			Format:     "text",
			Metadata:   make(map[string]interface{}),
		}
	}

	// 创建助手消息
	assistantMessage := ConversationMessage{
		ID:        fmt.Sprintf("msg_%d", time.Now().UnixNano()),
		Type:      "assistant",
		Content:   response.Content,
		Timestamp: time.Now(),
		Response:  response,
		Metadata:  make(map[string]interface{}),
	}

	// 更新会话
	cm.mutex.Lock()
	session.Messages = append(session.Messages, userMessage, assistantMessage)
	session.MessageCount += 2
	session.LastActivity = time.Now()

	// 更新上下文
	cm.updateSessionContext(session, userMessage, assistantMessage)

	// 更新用户画像
	cm.updateUserProfile(session.UserProfile, userMessage, assistantMessage)
	cm.mutex.Unlock()

	log.Printf("✅ 消息处理完成: 会话=%s, 响应长度=%d", sessionID, len(response.Content))

	return &assistantMessage, nil
}

// analyzeMessage 分析消息
func (cm *ConversationManager) analyzeMessage(content string, session *ConversationSession) (*MessageAnalysis, error) {
	if !cm.integratedProcessor.IsInitialized() {
		return &MessageAnalysis{
			ComplexityLevel: 0.5,
			UrgencyLevel:    0.5,
			UserStyle:       "unknown",
			PreferredFormat: "text",
			Metadata:        make(map[string]interface{}),
		}, nil
	}

	// 使用集成处理器分析消息
	nlpResult := cm.integratedProcessor.ProcessText(content)

	// 检测话题转换
	topicShift := cm.detectTopicShift(content, session)

	// 检测情感变化
	emotionChange := cm.detectEmotionChange(nlpResult.SentimentDetail, session)

	// 计算复杂度和紧急度
	complexityLevel := cm.calculateMessageComplexity(content, nlpResult)
	urgencyLevel := cm.calculateMessageUrgency(content, nlpResult)

	// 识别用户风格
	userStyle := cm.identifyUserStyle(content, session)
	preferredFormat := cm.identifyPreferredFormat(content, session)

	return &MessageAnalysis{
		Intent:          nlpResult.Intent,
		Sentiment:       nlpResult.SentimentDetail,
		Entities:        nlpResult.ExtractedEntities,
		Context:         nlpResult.Context,
		Relations:       nlpResult.Relations,
		TopicShift:      topicShift,
		EmotionChange:   emotionChange,
		ComplexityLevel: complexityLevel,
		UrgencyLevel:    urgencyLevel,
		UserStyle:       userStyle,
		PreferredFormat: preferredFormat,
		Metadata: map[string]interface{}{
			"nlp_result": nlpResult,
		},
	}, nil
}

// generateResponse 生成响应
func (cm *ConversationManager) generateResponse(content string, analysis *MessageAnalysis, session *ConversationSession) (*MessageResponse, error) {
	// 确定响应策略
	strategy := cm.determineResponseStrategy(analysis, session)

	// 确定响应语调
	tone := cm.determineResponseTone(analysis, session)

	// 确定响应格式
	format := cm.determineResponseFormat(analysis, session)

	// 生成响应内容
	responseContent := cm.generateResponseContent(content, analysis, session, strategy)

	// 生成建议和推荐
	suggestions := cm.generateSuggestions(analysis, session)
	relatedTopics := cm.generateRelatedTopics(analysis, session)
	followUpQuestions := cm.generateFollowUpQuestions(analysis, session)

	// 计算置信度
	confidence := cm.calculateResponseConfidence(analysis, session)

	return &MessageResponse{
		Content:           responseContent,
		Type:              "text",
		Confidence:        confidence,
		Strategy:          strategy,
		Tone:              tone,
		Format:            format,
		Suggestions:       suggestions,
		RelatedTopics:     relatedTopics,
		FollowUpQuestions: followUpQuestions,
		Metadata: map[string]interface{}{
			"generation_time": time.Now(),
		},
	}, nil
}

// createInitialContext 创建初始上下文
func (cm *ConversationManager) createInitialContext() *SessionContext {
	return &SessionContext{
		CurrentTopic:     "general",
		TopicHistory:     []TopicTransition{},
		EmotionalState:   "neutral",
		EmotionHistory:   []EmotionTransition{},
		KnowledgeLevel:   0.5,
		LearningGoals:    []string{},
		ConversationMode: "casual",
		InteractionStyle: "supportive",
		ShortTermMemory:  []ContextItem{},
		LongTermMemory:   []ContextItem{},
		Metadata:         make(map[string]interface{}),
	}
}

// loadOrCreateUserProfile 加载或创建用户画像
func (cm *ConversationManager) loadOrCreateUserProfile(userID string) *UserProfile {
	// 这里应该从数据库加载用户画像
	// 暂时创建一个默认画像
	return &UserProfile{
		UserID:             userID,
		Name:               "",
		Language:           "zh",
		Timezone:           "Asia/Shanghai",
		TechnicalLevel:     0.5,
		DomainExpertise:    make(map[string]float64),
		LearningStyle:      "reading",
		PreferredPace:      "normal",
		PreferredDepth:     "detailed",
		CommunicationStyle: "casual",
		PreferredFormat:    "text",
		TotalSessions:      0,
		TotalMessages:      0,
		AverageSessionTime: 0,
		TopTopics:          []string{},
		Preferences:        make(map[string]interface{}),
		Metadata:           make(map[string]interface{}),
		LastUpdated:        time.Now(),
	}
}

// startSessionCleanup 启动会话清理任务
func (cm *ConversationManager) startSessionCleanup() {
	ticker := time.NewTicker(5 * time.Minute)
	defer ticker.Stop()

	for range ticker.C {
		cm.cleanupExpiredSessions()
	}
}

// cleanupExpiredSessions 清理过期会话
func (cm *ConversationManager) cleanupExpiredSessions() {
	cm.mutex.Lock()
	defer cm.mutex.Unlock()

	now := time.Now()
	var expiredSessions []string

	for sessionID, session := range cm.sessions {
		if now.Sub(session.LastActivity) > cm.sessionTimeout {
			expiredSessions = append(expiredSessions, sessionID)
		}
	}

	for _, sessionID := range expiredSessions {
		delete(cm.sessions, sessionID)
		log.Printf("🗑️ 清理过期会话: %s", sessionID)
	}

	if len(expiredSessions) > 0 {
		log.Printf("✅ 清理了 %d 个过期会话", len(expiredSessions))
	}
}

// cleanupOldestSession 清理最旧的会话
func (cm *ConversationManager) cleanupOldestSession() {
	var oldestSessionID string
	var oldestTime time.Time

	for sessionID, session := range cm.sessions {
		if oldestSessionID == "" || session.StartTime.Before(oldestTime) {
			oldestSessionID = sessionID
			oldestTime = session.StartTime
		}
	}

	if oldestSessionID != "" {
		delete(cm.sessions, oldestSessionID)
		log.Printf("🗑️ 清理最旧会话: %s", oldestSessionID)
	}
}

// detectTopicShift 检测话题转换
func (cm *ConversationManager) detectTopicShift(content string, session *ConversationSession) bool {
	if len(session.Messages) == 0 {
		return false
	}

	// 简单的话题转换检测
	currentTopic := session.CurrentTopic

	// 基于关键词检测话题
	newTopic := cm.extractTopicFromContent(content)

	return newTopic != currentTopic && newTopic != "general"
}

// detectEmotionChange 检测情感变化
func (cm *ConversationManager) detectEmotionChange(sentiment *SentimentResult, session *ConversationSession) bool {
	if sentiment == nil || len(session.Context.EmotionHistory) == 0 {
		return false
	}

	lastEmotion := session.Context.EmotionalState
	currentEmotion := sentiment.Label

	return lastEmotion != currentEmotion
}

// calculateMessageComplexity 计算消息复杂度
func (cm *ConversationManager) calculateMessageComplexity(content string, nlpResult *IntegratedResult) float64 {
	complexity := 0.0

	// 基于文本长度
	complexity += float64(len(content)) / 500.0

	// 基于实体数量
	if len(nlpResult.ExtractedEntities) > 0 {
		complexity += float64(len(nlpResult.ExtractedEntities)) / 10.0
	}

	// 基于关系数量
	if len(nlpResult.Relations) > 0 {
		complexity += float64(len(nlpResult.Relations)) / 5.0
	}

	return min64(complexity, 1.0)
}

// calculateMessageUrgency 计算消息紧急度
func (cm *ConversationManager) calculateMessageUrgency(content string, nlpResult *IntegratedResult) float64 {
	urgency := 0.0

	// 基于紧急词汇
	urgentWords := []string{"紧急", "急", "立即", "马上", "快", "赶紧", "urgent", "asap", "immediately"}
	contentLower := strings.ToLower(content)

	for _, word := range urgentWords {
		if strings.Contains(contentLower, word) {
			urgency += 0.3
		}
	}

	// 基于标点符号
	if strings.Contains(content, "!") || strings.Contains(content, "！") {
		urgency += 0.2
	}

	// 基于上下文紧急度
	if nlpResult.Context != nil {
		urgency += nlpResult.Context.Urgency * 0.5
	}

	return min64(urgency, 1.0)
}

// identifyUserStyle 识别用户风格
func (cm *ConversationManager) identifyUserStyle(content string, session *ConversationSession) string {
	// 基于文本特征识别用户风格
	contentLower := strings.ToLower(content)

	// 正式风格指标
	formalIndicators := []string{"您", "请", "谢谢", "不好意思", "麻烦"}
	formalCount := 0
	for _, indicator := range formalIndicators {
		if strings.Contains(contentLower, indicator) {
			formalCount++
		}
	}

	// 非正式风格指标
	casualIndicators := []string{"嗨", "哈喽", "咋", "啥", "搞", "弄"}
	casualCount := 0
	for _, indicator := range casualIndicators {
		if strings.Contains(contentLower, indicator) {
			casualCount++
		}
	}

	// 技术风格指标
	techIndicators := []string{"api", "json", "http", "sql", "算法", "数据库", "服务器"}
	techCount := 0
	for _, indicator := range techIndicators {
		if strings.Contains(contentLower, indicator) {
			techCount++
		}
	}

	// 判断风格
	if techCount > 0 {
		return "technical"
	} else if formalCount > casualCount {
		return "formal"
	} else if casualCount > 0 {
		return "casual"
	}

	return "neutral"
}

// identifyPreferredFormat 识别偏好格式
func (cm *ConversationManager) identifyPreferredFormat(content string, session *ConversationSession) string {
	// 基于用户历史偏好
	if session.UserProfile != nil && session.UserProfile.PreferredFormat != "" {
		return session.UserProfile.PreferredFormat
	}

	// 基于当前消息特征
	if strings.Contains(content, "步骤") || strings.Contains(content, "流程") {
		return "bullet_points"
	} else if strings.Contains(content, "例子") || strings.Contains(content, "示例") {
		return "examples"
	} else if len(content) > 100 {
		return "detailed"
	}

	return "text"
}

// determineResponseStrategy 确定响应策略
func (cm *ConversationManager) determineResponseStrategy(analysis *MessageAnalysis, session *ConversationSession) string {
	if analysis.Intent != nil {
		switch analysis.Intent.Intent {
		case "question":
			return "informative"
		case "help":
			return "supportive"
		case "technical":
			return "technical"
		case "problem":
			return "problem_solving"
		default:
			return "conversational"
		}
	}

	// 基于紧急度
	if analysis.UrgencyLevel > 0.7 {
		return "urgent"
	}

	// 基于复杂度
	if analysis.ComplexityLevel > 0.8 {
		return "detailed"
	}

	return "general"
}

// determineResponseTone 确定响应语调
func (cm *ConversationManager) determineResponseTone(analysis *MessageAnalysis, session *ConversationSession) string {
	// 基于用户情感
	if analysis.Sentiment != nil {
		switch analysis.Sentiment.Label {
		case "positive":
			return "enthusiastic"
		case "negative":
			return "empathetic"
		case "neutral":
			return "professional"
		}
	}

	// 基于用户风格
	switch analysis.UserStyle {
	case "formal":
		return "professional"
	case "casual":
		return "friendly"
	case "technical":
		return "precise"
	}

	return "neutral"
}

// determineResponseFormat 确定响应格式
func (cm *ConversationManager) determineResponseFormat(analysis *MessageAnalysis, session *ConversationSession) string {
	return analysis.PreferredFormat
}

// generateResponseContent 生成响应内容
func (cm *ConversationManager) generateResponseContent(content string, analysis *MessageAnalysis, session *ConversationSession, strategy string) string {
	// 基于策略生成不同类型的响应
	switch strategy {
	case "informative":
		return cm.generateInformativeResponse(content, analysis, session)
	case "supportive":
		return cm.generateSupportiveResponse(content, analysis, session)
	case "technical":
		return cm.generateTechnicalResponse(content, analysis, session)
	case "problem_solving":
		return cm.generateProblemSolvingResponse(content, analysis, session)
	case "urgent":
		return cm.generateUrgentResponse(content, analysis, session)
	default:
		return cm.generateGeneralResponse(content, analysis, session)
	}
}

// generateInformativeResponse 生成信息性响应
func (cm *ConversationManager) generateInformativeResponse(content string, analysis *MessageAnalysis, session *ConversationSession) string {
	return fmt.Sprintf("根据您的问题 \"%s\"，我来为您详细解答...", content)
}

// generateSupportiveResponse 生成支持性响应
func (cm *ConversationManager) generateSupportiveResponse(content string, analysis *MessageAnalysis, session *ConversationSession) string {
	return fmt.Sprintf("我理解您需要帮助。让我来协助您解决 \"%s\" 这个问题。", content)
}

// generateTechnicalResponse 生成技术性响应
func (cm *ConversationManager) generateTechnicalResponse(content string, analysis *MessageAnalysis, session *ConversationSession) string {
	return fmt.Sprintf("关于您提到的技术问题 \"%s\"，这里是详细的技术说明...", content)
}

// generateProblemSolvingResponse 生成问题解决响应
func (cm *ConversationManager) generateProblemSolvingResponse(content string, analysis *MessageAnalysis, session *ConversationSession) string {
	return fmt.Sprintf("我来帮您分析和解决这个问题：\"%s\"。让我们一步步来处理。", content)
}

// generateUrgentResponse 生成紧急响应
func (cm *ConversationManager) generateUrgentResponse(content string, analysis *MessageAnalysis, session *ConversationSession) string {
	return fmt.Sprintf("我注意到这是一个紧急问题：\"%s\"。让我立即为您提供解决方案。", content)
}

// generateGeneralResponse 生成一般响应
func (cm *ConversationManager) generateGeneralResponse(content string, analysis *MessageAnalysis, session *ConversationSession) string {
	return fmt.Sprintf("感谢您的消息：\"%s\"。我来为您提供相关信息。", content)
}

// generateSuggestions 生成建议
func (cm *ConversationManager) generateSuggestions(analysis *MessageAnalysis, session *ConversationSession) []string {
	var suggestions []string

	if analysis.Intent != nil {
		switch analysis.Intent.Intent {
		case "question":
			suggestions = append(suggestions, "您可以提供更多具体信息")
			suggestions = append(suggestions, "我可以为您详细解释")
		case "help":
			suggestions = append(suggestions, "让我为您提供步骤指导")
			suggestions = append(suggestions, "您需要什么具体帮助？")
		case "technical":
			suggestions = append(suggestions, "我可以提供技术文档")
			suggestions = append(suggestions, "需要代码示例吗？")
		}
	}

	return suggestions
}

// generateRelatedTopics 生成相关话题
func (cm *ConversationManager) generateRelatedTopics(analysis *MessageAnalysis, session *ConversationSession) []string {
	var topics []string

	// 基于实体生成相关话题
	for _, entity := range analysis.Entities {
		if entity.Type == "TECH" || entity.Type == "PROGRAMMING_LANGUAGE" {
			topics = append(topics, entity.Text+" 教程")
			topics = append(topics, entity.Text+" 最佳实践")
		}
	}

	// 基于当前话题生成相关话题
	if session.CurrentTopic != "general" {
		topics = append(topics, session.CurrentTopic+" 进阶")
		topics = append(topics, session.CurrentTopic+" 案例")
	}

	return topics
}

// generateFollowUpQuestions 生成后续问题
func (cm *ConversationManager) generateFollowUpQuestions(analysis *MessageAnalysis, session *ConversationSession) []string {
	var questions []string

	if analysis.Intent != nil {
		switch analysis.Intent.Intent {
		case "question":
			questions = append(questions, "您还有其他相关问题吗？")
			questions = append(questions, "需要我详细解释某个部分吗？")
		case "help":
			questions = append(questions, "这个解决方案对您有帮助吗？")
			questions = append(questions, "您还需要其他协助吗？")
		case "technical":
			questions = append(questions, "您想了解更多技术细节吗？")
			questions = append(questions, "需要相关的代码示例吗？")
		}
	}

	return questions
}

// calculateResponseConfidence 计算响应置信度
func (cm *ConversationManager) calculateResponseConfidence(analysis *MessageAnalysis, session *ConversationSession) float64 {
	confidence := 0.5 // 基础置信度

	// 基于意图置信度
	if analysis.Intent != nil {
		confidence += analysis.Intent.Confidence * 0.3
	}

	// 基于实体识别质量
	if len(analysis.Entities) > 0 {
		avgEntityConf := 0.0
		for _, entity := range analysis.Entities {
			avgEntityConf += entity.Confidence
		}
		avgEntityConf /= float64(len(analysis.Entities))
		confidence += avgEntityConf * 0.2
	}

	// 基于会话历史
	if len(session.Messages) > 0 {
		confidence += 0.1 // 有历史上下文
	}

	return min64(confidence, 1.0)
}

// extractTopicFromContent 从内容中提取话题
func (cm *ConversationManager) extractTopicFromContent(content string) string {
	// 简单的话题提取
	contentLower := strings.ToLower(content)

	topicKeywords := map[string][]string{
		"programming": {"编程", "代码", "开发", "programming", "code", "development"},
		"database":    {"数据库", "sql", "mysql", "mongodb", "database"},
		"web":         {"网站", "网页", "html", "css", "javascript", "web"},
		"ai":          {"人工智能", "机器学习", "深度学习", "ai", "ml", "deep learning"},
		"system":      {"系统", "服务器", "部署", "system", "server", "deployment"},
	}

	for topic, keywords := range topicKeywords {
		for _, keyword := range keywords {
			if strings.Contains(contentLower, keyword) {
				return topic
			}
		}
	}

	return "general"
}

// updateSessionContext 更新会话上下文
func (cm *ConversationManager) updateSessionContext(session *ConversationSession, userMsg, assistantMsg ConversationMessage) {
	// 更新话题
	if userMsg.Analysis != nil && userMsg.Analysis.TopicShift {
		newTopic := cm.extractTopicFromContent(userMsg.Content)
		if newTopic != session.CurrentTopic {
			// 记录话题转换
			transition := TopicTransition{
				FromTopic:  session.CurrentTopic,
				ToTopic:    newTopic,
				Timestamp:  time.Now(),
				Trigger:    "user_message",
				Confidence: 0.8,
			}
			session.Context.TopicHistory = append(session.Context.TopicHistory, transition)
			session.CurrentTopic = newTopic
			session.Context.CurrentTopic = newTopic
		}
	}

	// 更新情感状态
	if userMsg.Analysis != nil && userMsg.Analysis.Sentiment != nil {
		newEmotion := userMsg.Analysis.Sentiment.Label
		if newEmotion != session.Context.EmotionalState {
			// 记录情感转换
			transition := EmotionTransition{
				FromEmotion: session.Context.EmotionalState,
				ToEmotion:   newEmotion,
				Timestamp:   time.Now(),
				Trigger:     "user_message",
				Intensity:   math.Abs(userMsg.Analysis.Sentiment.Score),
			}
			session.Context.EmotionHistory = append(session.Context.EmotionHistory, transition)
			session.Context.EmotionalState = newEmotion
		}
	}

	// 更新短期记忆
	contextItem := ContextItem{
		Type:       "message",
		Content:    userMsg.Content,
		Importance: 0.8,
		Timestamp:  time.Now(),
		ExpiryTime: time.Now().Add(1 * time.Hour),
		Metadata:   map[string]interface{}{"message_id": userMsg.ID},
	}
	session.Context.ShortTermMemory = append(session.Context.ShortTermMemory, contextItem)

	// 限制短期记忆大小
	if len(session.Context.ShortTermMemory) > 10 {
		session.Context.ShortTermMemory = session.Context.ShortTermMemory[1:]
	}
}

// updateUserProfile 更新用户画像
func (cm *ConversationManager) updateUserProfile(profile *UserProfile, userMsg, assistantMsg ConversationMessage) {
	profile.TotalMessages += 2
	profile.LastUpdated = time.Now()

	// 更新技术水平
	if userMsg.Analysis != nil && userMsg.Analysis.ComplexityLevel > 0.7 {
		profile.TechnicalLevel = (profile.TechnicalLevel + userMsg.Analysis.ComplexityLevel) / 2
	}

	// 更新领域专业度
	if userMsg.Analysis != nil && userMsg.Analysis.Context != nil {
		domain := userMsg.Analysis.Context.Domain
		if domain != "unknown" {
			if profile.DomainExpertise == nil {
				profile.DomainExpertise = make(map[string]float64)
			}
			profile.DomainExpertise[domain] = (profile.DomainExpertise[domain] + 0.1)
		}
	}

	// 更新交流风格
	if userMsg.Analysis != nil {
		profile.CommunicationStyle = userMsg.Analysis.UserStyle
		profile.PreferredFormat = userMsg.Analysis.PreferredFormat
	}
}
