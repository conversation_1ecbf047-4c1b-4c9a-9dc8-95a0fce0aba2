package nlp

import (
	"fmt"
	"log"
	"math"
	"sort"
	"strings"
	"sync"
	"time"
)

// AdaptiveLearningSystem 自适应学习系统
type AdaptiveLearningSystem struct {
	integratedProcessor *IntegratedProcessor
	conversationManager *ConversationManager
	knowledgeGraph      *KnowledgeGraph
	vectorSearch        *EnhancedVectorSearch

	// 学习模型
	userModels          map[string]*UserLearningModel
	feedbackProcessor   *FeedbackProcessor
	performanceAnalyzer *PerformanceAnalyzer
	adaptationEngine    *AdaptationEngine

	// 系统配置
	config      *LearningConfig
	initialized bool
	mutex       sync.RWMutex

	// 学习统计
	stats *LearningStats
}

// UserLearningModel 用户学习模型
type UserLearningModel struct {
	UserID string `json:"user_id"`

	// 学习特征
	LearningStyle  string  `json:"learning_style"`  // visual, auditory, kinesthetic, reading
	PreferredPace  string  `json:"preferred_pace"`  // slow, normal, fast
	KnowledgeLevel float64 `json:"knowledge_level"` // 0.0-1.0

	// 领域专业度
	DomainExpertise  map[string]float64 `json:"domain_expertise"`
	TopicInterests   map[string]float64 `json:"topic_interests"`
	SkillProgression map[string]float64 `json:"skill_progression"`

	// 学习行为
	QueryPatterns      []QueryPattern      `json:"query_patterns"`
	InteractionHistory []InteractionRecord `json:"interaction_history"`
	FeedbackHistory    []FeedbackRecord    `json:"feedback_history"`

	// 个性化参数
	ResponsePreferences *ResponsePreferences `json:"response_preferences"`
	AdaptationWeights   map[string]float64   `json:"adaptation_weights"`

	// 学习目标
	LearningGoals []LearningGoal `json:"learning_goals"`
	CurrentFocus  string         `json:"current_focus"`

	// 元数据
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	LastActive time.Time              `json:"last_active"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// QueryPattern 查询模式
type QueryPattern struct {
	Pattern     string    `json:"pattern"`
	Frequency   int       `json:"frequency"`
	SuccessRate float64   `json:"success_rate"`
	LastUsed    time.Time `json:"last_used"`
	Context     string    `json:"context"`
}

// InteractionRecord 交互记录
type InteractionRecord struct {
	ID            string                 `json:"id"`
	Timestamp     time.Time              `json:"timestamp"`
	QueryType     string                 `json:"query_type"`
	Query         string                 `json:"query"`
	Response      string                 `json:"response"`
	Satisfaction  float64                `json:"satisfaction"`  // 0.0-1.0
	Effectiveness float64                `json:"effectiveness"` // 0.0-1.0
	ResponseTime  time.Duration          `json:"response_time"`
	FollowUpCount int                    `json:"follow_up_count"`
	Metadata      map[string]interface{} `json:"metadata"`
}

// FeedbackRecord 反馈记录
type FeedbackRecord struct {
	ID          string                 `json:"id"`
	Timestamp   time.Time              `json:"timestamp"`
	Type        string                 `json:"type"`   // positive, negative, neutral
	Rating      float64                `json:"rating"` // 1.0-5.0
	Comment     string                 `json:"comment"`
	Context     string                 `json:"context"`
	ActionTaken string                 `json:"action_taken"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// ResponsePreferences 响应偏好
type ResponsePreferences struct {
	PreferredLength   string `json:"preferred_length"`   // short, medium, long
	PreferredTone     string `json:"preferred_tone"`     // formal, casual, friendly
	PreferredFormat   string `json:"preferred_format"`   // text, bullet_points, examples
	DetailLevel       string `json:"detail_level"`       // overview, detailed, comprehensive
	ExamplePreference bool   `json:"example_preference"` // 是否喜欢示例
	StepByStep        bool   `json:"step_by_step"`       // 是否喜欢分步说明
}

// LearningGoal 学习目标
type LearningGoal struct {
	ID          string    `json:"id"`
	Title       string    `json:"title"`
	Description string    `json:"description"`
	Category    string    `json:"category"`
	Priority    int       `json:"priority"`
	Progress    float64   `json:"progress"` // 0.0-1.0
	Deadline    time.Time `json:"deadline"`
	Status      string    `json:"status"` // active, completed, paused
	Milestones  []string  `json:"milestones"`
}

// FeedbackProcessor 反馈处理器
type FeedbackProcessor struct {
	feedbackQueue    chan *FeedbackRecord
	processingActive bool
	mutex            sync.RWMutex
}

// PerformanceAnalyzer 性能分析器
type PerformanceAnalyzer struct {
	metrics          map[string]*PerformanceMetric
	analysisInterval time.Duration
	mutex            sync.RWMutex
}

// PerformanceMetric 性能指标
type PerformanceMetric struct {
	Name           string    `json:"name"`
	Value          float64   `json:"value"`
	Trend          string    `json:"trend"` // improving, declining, stable
	LastUpdated    time.Time `json:"last_updated"`
	HistoricalData []float64 `json:"historical_data"`
}

// AdaptationEngine 适应引擎
type AdaptationEngine struct {
	adaptationRules  []*AdaptationRule
	adaptationQueue  chan *AdaptationTask
	processingActive bool
	mutex            sync.RWMutex
}

// AdaptationRule 适应规则
type AdaptationRule struct {
	ID        string                 `json:"id"`
	Name      string                 `json:"name"`
	Condition string                 `json:"condition"`
	Action    string                 `json:"action"`
	Priority  int                    `json:"priority"`
	Enabled   bool                   `json:"enabled"`
	Metadata  map[string]interface{} `json:"metadata"`
}

// AdaptationTask 适应任务
type AdaptationTask struct {
	UserID     string                 `json:"user_id"`
	Type       string                 `json:"type"`
	Parameters map[string]interface{} `json:"parameters"`
	Priority   int                    `json:"priority"`
	CreatedAt  time.Time              `json:"created_at"`
}

// LearningConfig 学习配置
type LearningConfig struct {
	// 学习参数
	LearningRate        float64 `json:"learning_rate"`
	AdaptationThreshold float64 `json:"adaptation_threshold"`
	FeedbackWeight      float64 `json:"feedback_weight"`

	// 更新频率
	ModelUpdateInterval time.Duration `json:"model_update_interval"`
	StatsUpdateInterval time.Duration `json:"stats_update_interval"`

	// 数据保留
	MaxHistorySize      int           `json:"max_history_size"`
	DataRetentionPeriod time.Duration `json:"data_retention_period"`

	// 个性化参数
	PersonalizationLevel float64 `json:"personalization_level"` // 0.0-1.0
	AdaptationSpeed      string  `json:"adaptation_speed"`      // slow, normal, fast
}

// LearningStats 学习统计
type LearningStats struct {
	// 用户统计
	TotalUsers    int `json:"total_users"`
	ActiveUsers   int `json:"active_users"`
	NewUsersToday int `json:"new_users_today"`

	// 交互统计
	TotalInteractions int     `json:"total_interactions"`
	AvgSatisfaction   float64 `json:"avg_satisfaction"`
	AvgEffectiveness  float64 `json:"avg_effectiveness"`

	// 学习统计
	TotalFeedback    int `json:"total_feedback"`
	PositiveFeedback int `json:"positive_feedback"`
	NegativeFeedback int `json:"negative_feedback"`

	// 适应统计
	AdaptationsApplied int `json:"adaptations_applied"`
	ModelUpdates       int `json:"model_updates"`

	// 性能指标
	SystemPerformance map[string]float64 `json:"system_performance"`

	// 更新时间
	LastUpdated time.Time `json:"last_updated"`
}

// NewAdaptiveLearningSystem 创建自适应学习系统
func NewAdaptiveLearningSystem(processor *IntegratedProcessor, conversationMgr *ConversationManager,
	knowledgeGraph *KnowledgeGraph, vectorSearch *EnhancedVectorSearch) *AdaptiveLearningSystem {

	log.Printf("🧠 初始化自适应学习系统...")

	config := &LearningConfig{
		LearningRate:         0.1,
		AdaptationThreshold:  0.7,
		FeedbackWeight:       0.8,
		ModelUpdateInterval:  10 * time.Minute,
		StatsUpdateInterval:  5 * time.Minute,
		MaxHistorySize:       1000,
		DataRetentionPeriod:  30 * 24 * time.Hour,
		PersonalizationLevel: 0.8,
		AdaptationSpeed:      "normal",
	}

	als := &AdaptiveLearningSystem{
		integratedProcessor: processor,
		conversationManager: conversationMgr,
		knowledgeGraph:      knowledgeGraph,
		vectorSearch:        vectorSearch,
		userModels:          make(map[string]*UserLearningModel),
		feedbackProcessor:   NewFeedbackProcessor(),
		performanceAnalyzer: NewPerformanceAnalyzer(),
		adaptationEngine:    NewAdaptationEngine(),
		config:              config,
		initialized:         false,
		stats:               &LearningStats{},
	}

	// 异步初始化
	go als.initializeAsync()

	return als
}

// initializeAsync 异步初始化
func (als *AdaptiveLearningSystem) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 自适应学习系统初始化失败: %v", r)
			als.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化自适应学习组件...")

	// 等待依赖组件初始化完成
	for i := 0; i < 30; i++ {
		if als.integratedProcessor != nil && als.integratedProcessor.IsInitialized() {
			break
		}
		time.Sleep(1 * time.Second)
		log.Printf("⏳ 等待依赖组件初始化... (%d/30)", i+1)
	}

	// 启动后台任务
	go als.startBackgroundTasks()

	als.mutex.Lock()
	als.initialized = true
	als.mutex.Unlock()

	log.Printf("✅ 自适应学习系统初始化完成")
}

// IsInitialized 检查是否已初始化
func (als *AdaptiveLearningSystem) IsInitialized() bool {
	als.mutex.RLock()
	defer als.mutex.RUnlock()
	return als.initialized
}

// GetOrCreateUserModel 获取或创建用户模型
func (als *AdaptiveLearningSystem) GetOrCreateUserModel(userID string) *UserLearningModel {
	als.mutex.Lock()
	defer als.mutex.Unlock()

	if model, exists := als.userModels[userID]; exists {
		model.LastActive = time.Now()
		return model
	}

	// 创建新的用户模型
	model := &UserLearningModel{
		UserID:             userID,
		LearningStyle:      "reading",
		PreferredPace:      "normal",
		KnowledgeLevel:     0.5,
		DomainExpertise:    make(map[string]float64),
		TopicInterests:     make(map[string]float64),
		SkillProgression:   make(map[string]float64),
		QueryPatterns:      []QueryPattern{},
		InteractionHistory: []InteractionRecord{},
		FeedbackHistory:    []FeedbackRecord{},
		ResponsePreferences: &ResponsePreferences{
			PreferredLength:   "medium",
			PreferredTone:     "friendly",
			PreferredFormat:   "text",
			DetailLevel:       "detailed",
			ExamplePreference: true,
			StepByStep:        false,
		},
		AdaptationWeights: make(map[string]float64),
		LearningGoals:     []LearningGoal{},
		CurrentFocus:      "general",
		CreatedAt:         time.Now(),
		UpdatedAt:         time.Now(),
		LastActive:        time.Now(),
		Metadata:          make(map[string]interface{}),
	}

	als.userModels[userID] = model

	log.Printf("👤 创建新用户学习模型: %s", userID)

	return model
}

// RecordInteraction 记录交互
func (als *AdaptiveLearningSystem) RecordInteraction(userID, queryType, query, response string,
	satisfaction, effectiveness float64, responseTime time.Duration) {

	model := als.GetOrCreateUserModel(userID)

	interaction := InteractionRecord{
		ID:            fmt.Sprintf("interaction_%d", time.Now().UnixNano()),
		Timestamp:     time.Now(),
		QueryType:     queryType,
		Query:         query,
		Response:      response,
		Satisfaction:  satisfaction,
		Effectiveness: effectiveness,
		ResponseTime:  responseTime,
		FollowUpCount: 0,
		Metadata:      make(map[string]interface{}),
	}

	als.mutex.Lock()
	model.InteractionHistory = append(model.InteractionHistory, interaction)

	// 限制历史记录大小
	if len(model.InteractionHistory) > als.config.MaxHistorySize {
		model.InteractionHistory = model.InteractionHistory[1:]
	}

	model.UpdatedAt = time.Now()
	als.mutex.Unlock()

	// 更新查询模式
	als.updateQueryPatterns(model, query, queryType)

	// 触发适应性学习
	als.triggerAdaptation(userID, "interaction", map[string]interface{}{
		"satisfaction":  satisfaction,
		"effectiveness": effectiveness,
		"query_type":    queryType,
	})

	log.Printf("📝 记录用户交互: %s, 满意度=%.2f, 有效性=%.2f", userID, satisfaction, effectiveness)
}

// ProcessFeedback 处理反馈
func (als *AdaptiveLearningSystem) ProcessFeedback(userID, feedbackType string, rating float64, comment, context string) {
	feedback := &FeedbackRecord{
		ID:        fmt.Sprintf("feedback_%d", time.Now().UnixNano()),
		Timestamp: time.Now(),
		Type:      feedbackType,
		Rating:    rating,
		Comment:   comment,
		Context:   context,
		Metadata:  make(map[string]interface{}),
	}

	model := als.GetOrCreateUserModel(userID)

	als.mutex.Lock()
	model.FeedbackHistory = append(model.FeedbackHistory, *feedback)

	// 限制反馈历史大小
	if len(model.FeedbackHistory) > als.config.MaxHistorySize {
		model.FeedbackHistory = model.FeedbackHistory[1:]
	}

	model.UpdatedAt = time.Now()
	als.mutex.Unlock()

	// 发送到反馈处理器
	select {
	case als.feedbackProcessor.feedbackQueue <- feedback:
		log.Printf("📬 反馈已发送到处理队列: %s", userID)
	default:
		log.Printf("⚠️ 反馈队列已满，跳过反馈: %s", userID)
	}

	// 触发适应性学习
	als.triggerAdaptation(userID, "feedback", map[string]interface{}{
		"type":    feedbackType,
		"rating":  rating,
		"context": context,
	})
}

// NewFeedbackProcessor 创建反馈处理器
func NewFeedbackProcessor() *FeedbackProcessor {
	fp := &FeedbackProcessor{
		feedbackQueue:    make(chan *FeedbackRecord, 100),
		processingActive: false,
	}

	go fp.startProcessing()

	return fp
}

// NewPerformanceAnalyzer 创建性能分析器
func NewPerformanceAnalyzer() *PerformanceAnalyzer {
	pa := &PerformanceAnalyzer{
		metrics:          make(map[string]*PerformanceMetric),
		analysisInterval: 5 * time.Minute,
	}

	go pa.startAnalysis()

	return pa
}

// NewAdaptationEngine 创建适应引擎
func NewAdaptationEngine() *AdaptationEngine {
	ae := &AdaptationEngine{
		adaptationRules:  []*AdaptationRule{},
		adaptationQueue:  make(chan *AdaptationTask, 100),
		processingActive: false,
	}

	ae.initializeRules()
	go ae.startProcessing()

	return ae
}

// updateQueryPatterns 更新查询模式
func (als *AdaptiveLearningSystem) updateQueryPatterns(model *UserLearningModel, query, queryType string) {
	// 查找现有模式
	for i, pattern := range model.QueryPatterns {
		if pattern.Pattern == query {
			model.QueryPatterns[i].Frequency++
			model.QueryPatterns[i].LastUsed = time.Now()
			return
		}
	}

	// 添加新模式
	newPattern := QueryPattern{
		Pattern:     query,
		Frequency:   1,
		SuccessRate: 0.5, // 初始成功率
		LastUsed:    time.Now(),
		Context:     queryType,
	}

	model.QueryPatterns = append(model.QueryPatterns, newPattern)

	// 限制模式数量
	if len(model.QueryPatterns) > 50 {
		// 按频率排序，保留最常用的
		sort.Slice(model.QueryPatterns, func(i, j int) bool {
			return model.QueryPatterns[i].Frequency > model.QueryPatterns[j].Frequency
		})
		model.QueryPatterns = model.QueryPatterns[:50]
	}
}

// triggerAdaptation 触发适应
func (als *AdaptiveLearningSystem) triggerAdaptation(userID, adaptationType string, parameters map[string]interface{}) {
	task := &AdaptationTask{
		UserID:     userID,
		Type:       adaptationType,
		Parameters: parameters,
		Priority:   1,
		CreatedAt:  time.Now(),
	}

	select {
	case als.adaptationEngine.adaptationQueue <- task:
		log.Printf("🔄 适应任务已加入队列: %s (%s)", userID, adaptationType)
	default:
		log.Printf("⚠️ 适应队列已满，跳过任务: %s", userID)
	}
}

// startBackgroundTasks 启动后台任务
func (als *AdaptiveLearningSystem) startBackgroundTasks() {
	// 模型更新任务
	modelUpdateTicker := time.NewTicker(als.config.ModelUpdateInterval)
	defer modelUpdateTicker.Stop()

	// 统计更新任务
	statsUpdateTicker := time.NewTicker(als.config.StatsUpdateInterval)
	defer statsUpdateTicker.Stop()

	// 数据清理任务
	cleanupTicker := time.NewTicker(24 * time.Hour)
	defer cleanupTicker.Stop()

	for {
		select {
		case <-modelUpdateTicker.C:
			als.updateAllUserModels()
		case <-statsUpdateTicker.C:
			als.updateStats()
		case <-cleanupTicker.C:
			als.cleanupOldData()
		}
	}
}

// updateAllUserModels 更新所有用户模型
func (als *AdaptiveLearningSystem) updateAllUserModels() {
	als.mutex.RLock()
	userIDs := make([]string, 0, len(als.userModels))
	for userID := range als.userModels {
		userIDs = append(userIDs, userID)
	}
	als.mutex.RUnlock()

	for _, userID := range userIDs {
		als.updateUserModel(userID)
	}

	log.Printf("🔄 更新了 %d 个用户模型", len(userIDs))
}

// updateUserModel 更新用户模型
func (als *AdaptiveLearningSystem) updateUserModel(userID string) {
	als.mutex.Lock()
	model, exists := als.userModels[userID]
	if !exists {
		als.mutex.Unlock()
		return
	}
	als.mutex.Unlock()

	// 更新知识水平
	als.updateKnowledgeLevel(model)

	// 更新领域专业度
	als.updateDomainExpertise(model)

	// 更新话题兴趣
	als.updateTopicInterests(model)

	// 更新学习风格
	als.updateLearningStyle(model)

	// 更新响应偏好
	als.updateResponsePreferences(model)

	model.UpdatedAt = time.Now()

	log.Printf("📊 更新用户模型: %s", userID)
}

// updateKnowledgeLevel 更新知识水平
func (als *AdaptiveLearningSystem) updateKnowledgeLevel(model *UserLearningModel) {
	if len(model.InteractionHistory) == 0 {
		return
	}

	// 基于最近的交互计算知识水平
	recentInteractions := model.InteractionHistory
	if len(recentInteractions) > 20 {
		recentInteractions = recentInteractions[len(recentInteractions)-20:]
	}

	totalEffectiveness := 0.0
	for _, interaction := range recentInteractions {
		totalEffectiveness += interaction.Effectiveness
	}

	avgEffectiveness := totalEffectiveness / float64(len(recentInteractions))

	// 使用学习率更新知识水平
	learningRate := als.config.LearningRate
	model.KnowledgeLevel = model.KnowledgeLevel*(1-learningRate) + avgEffectiveness*learningRate

	// 确保在合理范围内
	if model.KnowledgeLevel > 1.0 {
		model.KnowledgeLevel = 1.0
	} else if model.KnowledgeLevel < 0.0 {
		model.KnowledgeLevel = 0.0
	}
}

// updateDomainExpertise 更新领域专业度
func (als *AdaptiveLearningSystem) updateDomainExpertise(model *UserLearningModel) {
	domainCounts := make(map[string]int)
	domainEffectiveness := make(map[string]float64)

	// 分析最近的交互
	for _, interaction := range model.InteractionHistory {
		// 这里需要从查询中提取领域信息
		domain := als.extractDomainFromQuery(interaction.Query)
		if domain != "unknown" {
			domainCounts[domain]++
			domainEffectiveness[domain] += interaction.Effectiveness
		}
	}

	// 更新领域专业度
	for domain, count := range domainCounts {
		if count > 0 {
			avgEffectiveness := domainEffectiveness[domain] / float64(count)

			// 使用学习率更新
			learningRate := als.config.LearningRate
			currentExpertise := model.DomainExpertise[domain]
			model.DomainExpertise[domain] = currentExpertise*(1-learningRate) + avgEffectiveness*learningRate
		}
	}
}

// updateTopicInterests 更新话题兴趣
func (als *AdaptiveLearningSystem) updateTopicInterests(model *UserLearningModel) {
	topicCounts := make(map[string]int)
	topicSatisfaction := make(map[string]float64)

	// 分析最近的交互
	for _, interaction := range model.InteractionHistory {
		topic := als.extractTopicFromQuery(interaction.Query)
		if topic != "unknown" {
			topicCounts[topic]++
			topicSatisfaction[topic] += interaction.Satisfaction
		}
	}

	// 更新话题兴趣
	for topic, count := range topicCounts {
		if count > 0 {
			avgSatisfaction := topicSatisfaction[topic] / float64(count)
			frequency := float64(count) / float64(len(model.InteractionHistory))

			// 兴趣 = 满意度 * 频率
			interest := avgSatisfaction * frequency

			// 使用学习率更新
			learningRate := als.config.LearningRate
			currentInterest := model.TopicInterests[topic]
			model.TopicInterests[topic] = currentInterest*(1-learningRate) + interest*learningRate
		}
	}
}

// updateLearningStyle 更新学习风格
func (als *AdaptiveLearningSystem) updateLearningStyle(model *UserLearningModel) {
	// 基于用户的查询模式和反馈分析学习风格
	styleScores := map[string]float64{
		"visual":      0.0,
		"auditory":    0.0,
		"kinesthetic": 0.0,
		"reading":     0.0,
	}

	// 分析查询模式
	for _, pattern := range model.QueryPatterns {
		query := strings.ToLower(pattern.Pattern)

		if strings.Contains(query, "图") || strings.Contains(query, "图表") || strings.Contains(query, "示例") {
			styleScores["visual"] += float64(pattern.Frequency)
		} else if strings.Contains(query, "解释") || strings.Contains(query, "说明") {
			styleScores["auditory"] += float64(pattern.Frequency)
		} else if strings.Contains(query, "实践") || strings.Contains(query, "操作") || strings.Contains(query, "步骤") {
			styleScores["kinesthetic"] += float64(pattern.Frequency)
		} else {
			styleScores["reading"] += float64(pattern.Frequency)
		}
	}

	// 找到最高分的学习风格
	maxScore := 0.0
	bestStyle := "reading"
	for style, score := range styleScores {
		if score > maxScore {
			maxScore = score
			bestStyle = style
		}
	}

	model.LearningStyle = bestStyle
}

// updateResponsePreferences 更新响应偏好
func (als *AdaptiveLearningSystem) updateResponsePreferences(model *UserLearningModel) {
	if len(model.FeedbackHistory) == 0 {
		return
	}

	// 分析反馈中的偏好信息
	for _, feedback := range model.FeedbackHistory {
		comment := strings.ToLower(feedback.Comment)

		// 分析长度偏好
		if strings.Contains(comment, "太长") || strings.Contains(comment, "冗长") {
			model.ResponsePreferences.PreferredLength = "short"
		} else if strings.Contains(comment, "太短") || strings.Contains(comment, "详细") {
			model.ResponsePreferences.PreferredLength = "long"
		}

		// 分析格式偏好
		if strings.Contains(comment, "列表") || strings.Contains(comment, "要点") {
			model.ResponsePreferences.PreferredFormat = "bullet_points"
		} else if strings.Contains(comment, "例子") || strings.Contains(comment, "示例") {
			model.ResponsePreferences.PreferredFormat = "examples"
			model.ResponsePreferences.ExamplePreference = true
		}

		// 分析步骤偏好
		if strings.Contains(comment, "步骤") || strings.Contains(comment, "分步") {
			model.ResponsePreferences.StepByStep = true
		}
	}
}

// extractDomainFromQuery 从查询中提取领域
func (als *AdaptiveLearningSystem) extractDomainFromQuery(query string) string {
	queryLower := strings.ToLower(query)

	domains := map[string][]string{
		"programming": {"编程", "代码", "开发", "programming", "code", "development"},
		"database":    {"数据库", "sql", "mysql", "mongodb", "database"},
		"web":         {"网站", "网页", "html", "css", "javascript", "web"},
		"ai":          {"人工智能", "机器学习", "深度学习", "ai", "ml", "deep learning"},
		"system":      {"系统", "服务器", "部署", "system", "server", "deployment"},
	}

	for domain, keywords := range domains {
		for _, keyword := range keywords {
			if strings.Contains(queryLower, keyword) {
				return domain
			}
		}
	}

	return "unknown"
}

// extractTopicFromQuery 从查询中提取话题
func (als *AdaptiveLearningSystem) extractTopicFromQuery(query string) string {
	queryLower := strings.ToLower(query)

	topics := map[string][]string{
		"tutorial":      {"教程", "学习", "入门", "tutorial", "learn", "guide"},
		"troubleshoot":  {"问题", "错误", "故障", "problem", "error", "issue"},
		"best_practice": {"最佳实践", "建议", "推荐", "best practice", "recommendation"},
		"comparison":    {"比较", "对比", "区别", "compare", "difference", "vs"},
		"installation":  {"安装", "配置", "部署", "install", "setup", "deploy"},
	}

	for topic, keywords := range topics {
		for _, keyword := range keywords {
			if strings.Contains(queryLower, keyword) {
				return topic
			}
		}
	}

	return "unknown"
}

// cleanupOldData 清理旧数据
func (als *AdaptiveLearningSystem) cleanupOldData() {
	als.mutex.Lock()
	defer als.mutex.Unlock()

	cutoffTime := time.Now().Add(-als.config.DataRetentionPeriod)
	cleanedCount := 0

	for userID, model := range als.userModels {
		// 清理旧的交互记录
		var newInteractions []InteractionRecord
		for _, interaction := range model.InteractionHistory {
			if interaction.Timestamp.After(cutoffTime) {
				newInteractions = append(newInteractions, interaction)
			} else {
				cleanedCount++
			}
		}
		model.InteractionHistory = newInteractions

		// 清理旧的反馈记录
		var newFeedback []FeedbackRecord
		for _, feedback := range model.FeedbackHistory {
			if feedback.Timestamp.After(cutoffTime) {
				newFeedback = append(newFeedback, feedback)
			} else {
				cleanedCount++
			}
		}
		model.FeedbackHistory = newFeedback

		// 如果用户长时间不活跃，可以考虑删除模型
		if model.LastActive.Before(cutoffTime) {
			delete(als.userModels, userID)
			cleanedCount++
		}
	}

	if cleanedCount > 0 {
		log.Printf("🧹 清理了 %d 条旧数据", cleanedCount)
	}
}

// updateStats 更新统计信息
func (als *AdaptiveLearningSystem) updateStats() {
	als.mutex.RLock()
	defer als.mutex.RUnlock()

	stats := als.stats

	// 用户统计
	stats.TotalUsers = len(als.userModels)
	stats.ActiveUsers = 0
	stats.NewUsersToday = 0

	today := time.Now().Truncate(24 * time.Hour)

	// 交互和反馈统计
	totalInteractions := 0
	totalSatisfaction := 0.0
	totalEffectiveness := 0.0
	totalFeedback := 0
	positiveFeedback := 0
	negativeFeedback := 0

	for _, model := range als.userModels {
		// 活跃用户统计
		if time.Since(model.LastActive) < 24*time.Hour {
			stats.ActiveUsers++
		}

		// 新用户统计
		if model.CreatedAt.After(today) {
			stats.NewUsersToday++
		}

		// 交互统计
		totalInteractions += len(model.InteractionHistory)
		for _, interaction := range model.InteractionHistory {
			totalSatisfaction += interaction.Satisfaction
			totalEffectiveness += interaction.Effectiveness
		}

		// 反馈统计
		totalFeedback += len(model.FeedbackHistory)
		for _, feedback := range model.FeedbackHistory {
			if feedback.Type == "positive" {
				positiveFeedback++
			} else if feedback.Type == "negative" {
				negativeFeedback++
			}
		}
	}

	stats.TotalInteractions = totalInteractions
	if totalInteractions > 0 {
		stats.AvgSatisfaction = totalSatisfaction / float64(totalInteractions)
		stats.AvgEffectiveness = totalEffectiveness / float64(totalInteractions)
	}

	stats.TotalFeedback = totalFeedback
	stats.PositiveFeedback = positiveFeedback
	stats.NegativeFeedback = negativeFeedback

	// 系统性能指标
	if stats.SystemPerformance == nil {
		stats.SystemPerformance = make(map[string]float64)
	}

	stats.SystemPerformance["user_satisfaction"] = stats.AvgSatisfaction
	stats.SystemPerformance["response_effectiveness"] = stats.AvgEffectiveness
	stats.SystemPerformance["positive_feedback_ratio"] = float64(positiveFeedback) / math.Max(float64(totalFeedback), 1.0)

	stats.LastUpdated = time.Now()

	log.Printf("📊 更新学习统计: 用户=%d, 活跃=%d, 交互=%d", stats.TotalUsers, stats.ActiveUsers, stats.TotalInteractions)
}

// GetStats 获取统计信息
func (als *AdaptiveLearningSystem) GetStats() *LearningStats {
	als.mutex.RLock()
	defer als.mutex.RUnlock()

	return als.stats
}

// GetUserModel 获取用户模型
func (als *AdaptiveLearningSystem) GetUserModel(userID string) *UserLearningModel {
	als.mutex.RLock()
	defer als.mutex.RUnlock()

	if model, exists := als.userModels[userID]; exists {
		return model
	}

	return nil
}

// startProcessing 启动反馈处理
func (fp *FeedbackProcessor) startProcessing() {
	fp.mutex.Lock()
	if fp.processingActive {
		fp.mutex.Unlock()
		return
	}
	fp.processingActive = true
	fp.mutex.Unlock()

	log.Printf("📬 启动反馈处理器...")

	for feedback := range fp.feedbackQueue {
		fp.processFeedback(feedback)
	}
}

// processFeedback 处理单个反馈
func (fp *FeedbackProcessor) processFeedback(feedback *FeedbackRecord) {
	log.Printf("📝 处理反馈: 类型=%s, 评分=%.1f", feedback.Type, feedback.Rating)

	// 这里可以添加更复杂的反馈处理逻辑
	// 例如：情感分析、关键词提取、分类等

	// 记录处理动作
	if feedback.Rating < 3.0 {
		feedback.ActionTaken = "negative_feedback_analysis"
	} else if feedback.Rating > 4.0 {
		feedback.ActionTaken = "positive_feedback_reinforcement"
	} else {
		feedback.ActionTaken = "neutral_feedback_logged"
	}
}

// startAnalysis 启动性能分析
func (pa *PerformanceAnalyzer) startAnalysis() {
	log.Printf("📊 启动性能分析器...")

	ticker := time.NewTicker(pa.analysisInterval)
	defer ticker.Stop()

	for range ticker.C {
		pa.analyzePerformance()
	}
}

// analyzePerformance 分析性能
func (pa *PerformanceAnalyzer) analyzePerformance() {
	pa.mutex.Lock()
	defer pa.mutex.Unlock()

	// 更新各种性能指标
	pa.updateMetric("response_time", pa.calculateAvgResponseTime())
	pa.updateMetric("user_satisfaction", pa.calculateUserSatisfaction())
	pa.updateMetric("system_load", pa.calculateSystemLoad())

	log.Printf("📈 性能分析完成: %d 个指标", len(pa.metrics))
}

// updateMetric 更新指标
func (pa *PerformanceAnalyzer) updateMetric(name string, value float64) {
	metric, exists := pa.metrics[name]
	if !exists {
		metric = &PerformanceMetric{
			Name:           name,
			Value:          value,
			Trend:          "stable",
			LastUpdated:    time.Now(),
			HistoricalData: []float64{},
		}
		pa.metrics[name] = metric
	}

	// 更新历史数据
	metric.HistoricalData = append(metric.HistoricalData, value)
	if len(metric.HistoricalData) > 100 {
		metric.HistoricalData = metric.HistoricalData[1:]
	}

	// 计算趋势
	if len(metric.HistoricalData) >= 2 {
		recent := metric.HistoricalData[len(metric.HistoricalData)-1]
		previous := metric.HistoricalData[len(metric.HistoricalData)-2]

		if recent > previous*1.05 {
			metric.Trend = "improving"
		} else if recent < previous*0.95 {
			metric.Trend = "declining"
		} else {
			metric.Trend = "stable"
		}
	}

	metric.Value = value
	metric.LastUpdated = time.Now()
}

// calculateAvgResponseTime 计算平均响应时间
func (pa *PerformanceAnalyzer) calculateAvgResponseTime() float64 {
	// 这里应该从实际的系统指标中获取数据
	// 暂时返回模拟值
	return 150.0 + float64(time.Now().Unix()%100)
}

// calculateUserSatisfaction 计算用户满意度
func (pa *PerformanceAnalyzer) calculateUserSatisfaction() float64 {
	// 这里应该从用户反馈中计算实际满意度
	// 暂时返回模拟值
	return 0.8 + 0.1*math.Sin(float64(time.Now().Unix())/3600)
}

// calculateSystemLoad 计算系统负载
func (pa *PerformanceAnalyzer) calculateSystemLoad() float64 {
	// 这里应该从系统监控中获取实际负载
	// 暂时返回模拟值
	return 0.3 + 0.2*math.Cos(float64(time.Now().Unix())/1800)
}

// initializeRules 初始化适应规则
func (ae *AdaptationEngine) initializeRules() {
	log.Printf("🔧 初始化适应规则...")

	// 规则1: 低满意度触发响应调整
	rule1 := &AdaptationRule{
		ID:        "rule_low_satisfaction",
		Name:      "低满意度响应调整",
		Condition: "user_satisfaction < 0.6",
		Action:    "adjust_response_style",
		Priority:  1,
		Enabled:   true,
		Metadata:  make(map[string]interface{}),
	}

	// 规则2: 高频查询模式优化
	rule2 := &AdaptationRule{
		ID:        "rule_frequent_pattern",
		Name:      "高频查询模式优化",
		Condition: "query_frequency > 10",
		Action:    "optimize_response_template",
		Priority:  2,
		Enabled:   true,
		Metadata:  make(map[string]interface{}),
	}

	// 规则3: 学习风格适应
	rule3 := &AdaptationRule{
		ID:        "rule_learning_style",
		Name:      "学习风格适应",
		Condition: "learning_style_confidence > 0.8",
		Action:    "adapt_content_format",
		Priority:  3,
		Enabled:   true,
		Metadata:  make(map[string]interface{}),
	}

	ae.adaptationRules = append(ae.adaptationRules, rule1, rule2, rule3)

	log.Printf("✅ 适应规则初始化完成: %d 个规则", len(ae.adaptationRules))
}

// startProcessing 启动适应处理
func (ae *AdaptationEngine) startProcessing() {
	ae.mutex.Lock()
	if ae.processingActive {
		ae.mutex.Unlock()
		return
	}
	ae.processingActive = true
	ae.mutex.Unlock()

	log.Printf("🔄 启动适应引擎...")

	for task := range ae.adaptationQueue {
		ae.processAdaptationTask(task)
	}
}

// processAdaptationTask 处理适应任务
func (ae *AdaptationEngine) processAdaptationTask(task *AdaptationTask) {
	log.Printf("🔄 处理适应任务: 用户=%s, 类型=%s", task.UserID, task.Type)

	// 根据任务类型执行不同的适应逻辑
	switch task.Type {
	case "interaction":
		ae.processInteractionAdaptation(task)
	case "feedback":
		ae.processFeedbackAdaptation(task)
	case "pattern":
		ae.processPatternAdaptation(task)
	default:
		log.Printf("⚠️ 未知的适应任务类型: %s", task.Type)
	}
}

// processInteractionAdaptation 处理交互适应
func (ae *AdaptationEngine) processInteractionAdaptation(task *AdaptationTask) {
	satisfaction, _ := task.Parameters["satisfaction"].(float64)
	effectiveness, _ := task.Parameters["effectiveness"].(float64)

	// 如果满意度或有效性较低，触发适应
	if satisfaction < 0.6 || effectiveness < 0.6 {
		log.Printf("🔧 触发低满意度适应: 用户=%s", task.UserID)
		// 这里可以调整用户模型的参数
	}
}

// processFeedbackAdaptation 处理反馈适应
func (ae *AdaptationEngine) processFeedbackAdaptation(task *AdaptationTask) {
	feedbackType, _ := task.Parameters["type"].(string)
	rating, _ := task.Parameters["rating"].(float64)

	if feedbackType == "negative" || rating < 3.0 {
		log.Printf("🔧 触发负面反馈适应: 用户=%s", task.UserID)
		// 这里可以调整响应策略
	}
}

// processPatternAdaptation 处理模式适应
func (ae *AdaptationEngine) processPatternAdaptation(task *AdaptationTask) {
	log.Printf("🔧 处理模式适应: 用户=%s", task.UserID)
	// 这里可以基于用户的查询模式调整系统行为
}
