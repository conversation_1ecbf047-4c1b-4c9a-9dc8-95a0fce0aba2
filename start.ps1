# FAQ系统启动脚本
# 确保在PowerShell环境下运行，支持gojieba

Write-Host "🚀 启动FAQ系统..." -ForegroundColor Green
Write-Host "📍 当前目录: $(Get-Location)" -ForegroundColor Yellow
Write-Host "🔧 Go版本: $(go version)" -ForegroundColor Cyan

# 检查Go环境
if (-not (Get-Command go -ErrorAction SilentlyContinue)) {
    Write-Host "❌ 未找到Go环境，请先安装Go" -ForegroundColor Red
    exit 1
}

# 检查CGO环境
$cgoEnabled = go env CGO_ENABLED
Write-Host "🔧 CGO状态: $cgoEnabled" -ForegroundColor Cyan

if ($cgoEnabled -ne "1") {
    Write-Host "⚠️ CGO未启用，gojieba可能无法正常工作" -ForegroundColor Yellow
}

# 设置环境变量（如果需要）
$env:CGO_ENABLED = "1"

Write-Host "🔧 启动FAQ系统主程序..." -ForegroundColor Green
Write-Host "📝 提示：在PowerShell环境下gojieba应该可以正常工作" -ForegroundColor Yellow
Write-Host "🌐 系统启动后访问: http://localhost:8082" -ForegroundColor Cyan
Write-Host ""

# 启动系统
try {
    go run cmd/main.go
} catch {
    Write-Host "❌ 系统启动失败: $_" -ForegroundColor Red
    Write-Host "💡 请检查依赖和环境配置" -ForegroundColor Yellow
    exit 1
}
