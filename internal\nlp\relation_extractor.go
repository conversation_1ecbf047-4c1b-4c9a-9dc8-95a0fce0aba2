package nlp

import (
	"log"
	"regexp"
	"strings"
)

// RelationExtractor 关系抽取器
type RelationExtractor struct {
	relationPatterns map[string][]*regexp.Regexp
	verbPatterns     []*regexp.Regexp
}

// Relation 关系
type Relation struct {
	Subject    string                 `json:"subject"`
	Predicate  string                 `json:"predicate"`
	Object     string                 `json:"object"`
	Confidence float64                `json:"confidence"`
	Type       string                 `json:"type"`
	Position   RelationPosition       `json:"position"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// RelationPosition 关系位置
type RelationPosition struct {
	SubjectStart int `json:"subject_start"`
	SubjectEnd   int `json:"subject_end"`
	PredicateStart int `json:"predicate_start"`
	PredicateEnd   int `json:"predicate_end"`
	ObjectStart  int `json:"object_start"`
	ObjectEnd    int `json:"object_end"`
}

// NewRelationExtractor 创建关系抽取器
func NewRelationExtractor() *RelationExtractor {
	log.Printf("🔗 初始化关系抽取器...")
	
	re := &RelationExtractor{
		relationPatterns: make(map[string][]*regexp.Regexp),
		verbPatterns:     []*regexp.Regexp{},
	}
	
	re.initializePatterns()
	
	log.Printf("✅ 关系抽取器初始化完成")
	return re
}

// initializePatterns 初始化模式
func (re *RelationExtractor) initializePatterns() {
	// 因果关系
	re.addRelationPattern("causal", []string{
		`(.+)(导致|引起|造成|产生)(.+)`,
		`(.+)(因为|由于|因)(.+)`,
		`(.+)(所以|因此|故)(.+)`,
		`(.+) causes? (.+)`,
		`(.+) leads? to (.+)`,
		`(.+) results? in (.+)`,
	})
	
	// 包含关系
	re.addRelationPattern("containment", []string{
		`(.+)(包含|包括|含有)(.+)`,
		`(.+)(属于|隶属于)(.+)`,
		`(.+) contains? (.+)`,
		`(.+) includes? (.+)`,
		`(.+) belongs? to (.+)`,
	})
	
	// 比较关系
	re.addRelationPattern("comparison", []string{
		`(.+)(比|相比|对比)(.+)(更|较)(.+)`,
		`(.+)(优于|劣于|等于)(.+)`,
		`(.+) is (better|worse|equal) than (.+)`,
		`(.+) compared to (.+)`,
	})
	
	// 时间关系
	re.addRelationPattern("temporal", []string{
		`(.+)(之前|之后|同时)(.+)`,
		`(.+)(先于|后于)(.+)`,
		`(.+) before (.+)`,
		`(.+) after (.+)`,
		`(.+) during (.+)`,
	})
	
	// 位置关系
	re.addRelationPattern("spatial", []string{
		`(.+)(在|位于|处于)(.+)`,
		`(.+)(上面|下面|旁边|附近)(.+)`,
		`(.+) is (in|on|at|near) (.+)`,
		`(.+) located (in|at|on) (.+)`,
	})
	
	// 拥有关系
	re.addRelationPattern("possession", []string{
		`(.+)(有|拥有|具有|持有)(.+)`,
		`(.+)(的|之)(.+)`,
		`(.+) has (.+)`,
		`(.+) owns (.+)`,
		`(.+) possesses (.+)`,
	})
	
	// 动作关系
	re.addRelationPattern("action", []string{
		`(.+)(使用|利用|采用)(.+)`,
		`(.+)(创建|制作|生产)(.+)`,
		`(.+)(学习|研究|分析)(.+)`,
		`(.+) uses (.+)`,
		`(.+) creates (.+)`,
		`(.+) studies (.+)`,
	})
	
	// 定义关系
	re.addRelationPattern("definition", []string{
		`(.+)(是|为|即)(.+)`,
		`(.+)(定义为|称为|叫做)(.+)`,
		`(.+) is (.+)`,
		`(.+) means (.+)`,
		`(.+) refers to (.+)`,
	})
	
	// 初始化动词模式
	verbPatterns := []string{
		`(是|为|有|做|用|学|看|说|想|知|去|来|给|拿|放|买|卖)`,
		`(创建|删除|修改|更新|查询|搜索|分析|处理|生成|执行)`,
		`(is|are|was|were|have|has|do|does|did|use|make|get|put|buy|sell)`,
		`(create|delete|modify|update|query|search|analyze|process|generate|execute)`,
	}
	
	for _, pattern := range verbPatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			re.verbPatterns = append(re.verbPatterns, regex)
		}
	}
}

// addRelationPattern 添加关系模式
func (re *RelationExtractor) addRelationPattern(relationType string, patterns []string) {
	for _, pattern := range patterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			re.relationPatterns[relationType] = append(re.relationPatterns[relationType], regex)
		} else {
			log.Printf("⚠️ 编译关系模式失败: %s - %v", pattern, err)
		}
	}
}

// ExtractRelations 抽取关系
func (re *RelationExtractor) ExtractRelations(text string, entities []Entity) []Relation {
	var relations []Relation
	
	if strings.TrimSpace(text) == "" {
		return relations
	}
	
	// 1. 基于模式的关系抽取
	relations = append(relations, re.extractByPatterns(text)...)
	
	// 2. 基于实体的关系抽取
	relations = append(relations, re.extractByEntities(text, entities)...)
	
	// 3. 基于动词的关系抽取
	relations = append(relations, re.extractByVerbs(text)...)
	
	// 4. 去重和过滤
	relations = re.filterRelations(relations)
	
	return relations
}

// extractByPatterns 基于模式抽取关系
func (re *RelationExtractor) extractByPatterns(text string) []Relation {
	var relations []Relation
	
	for relationType, patterns := range re.relationPatterns {
		for _, pattern := range patterns {
			matches := pattern.FindAllStringSubmatch(text, -1)
			indices := pattern.FindAllStringSubmatchIndex(text, -1)
			
			for i, match := range matches {
				if len(match) >= 3 && len(indices) > i {
					subject := strings.TrimSpace(match[1])
					predicate := strings.TrimSpace(match[2])
					object := ""
					
					// 根据匹配组数确定对象
					if len(match) >= 4 {
						object = strings.TrimSpace(match[3])
					}
					
					if subject != "" && predicate != "" {
						relation := Relation{
							Subject:    subject,
							Predicate:  predicate,
							Object:     object,
							Type:       relationType,
							Confidence: re.calculatePatternConfidence(relationType, match),
							Position:   re.extractPosition(indices[i], match),
							Metadata: map[string]interface{}{
								"extraction_method": "pattern",
								"pattern_type":      relationType,
								"full_match":        match[0],
							},
						}
						relations = append(relations, relation)
					}
				}
			}
		}
	}
	
	return relations
}

// extractByEntities 基于实体抽取关系
func (re *RelationExtractor) extractByEntities(text string, entities []Entity) []Relation {
	var relations []Relation
	
	if len(entities) < 2 {
		return relations
	}
	
	// 在实体之间寻找关系
	for i := 0; i < len(entities)-1; i++ {
		for j := i + 1; j < len(entities); j++ {
			entity1 := entities[i]
			entity2 := entities[j]
			
			// 查找实体之间的文本
			betweenText := re.getTextBetweenEntities(text, entity1.Text, entity2.Text)
			if betweenText != "" {
				// 尝试识别关系类型
				relationType := re.identifyRelationType(betweenText)
				if relationType != "unknown" {
					relation := Relation{
						Subject:    entity1.Text,
						Predicate:  betweenText,
						Object:     entity2.Text,
						Type:       relationType,
						Confidence: 0.6,
						Metadata: map[string]interface{}{
							"extraction_method": "entity_based",
							"entity1_type":      entity1.Type,
							"entity2_type":      entity2.Type,
						},
					}
					relations = append(relations, relation)
				}
			}
		}
	}
	
	return relations
}

// extractByVerbs 基于动词抽取关系
func (re *RelationExtractor) extractByVerbs(text string) []Relation {
	var relations []Relation
	
	for _, verbPattern := range re.verbPatterns {
		matches := verbPattern.FindAllString(text, -1)
		indices := verbPattern.FindAllStringIndex(text, -1)
		
		for i, verb := range matches {
			if len(indices) > i {
				// 提取动词前后的文本作为主语和宾语
				verbStart := indices[i][0]
				verbEnd := indices[i][1]
				
				subject := re.extractSubject(text, verbStart)
				object := re.extractObject(text, verbEnd)
				
				if subject != "" && object != "" {
					relation := Relation{
						Subject:    subject,
						Predicate:  verb,
						Object:     object,
						Type:       "action",
						Confidence: 0.5,
						Metadata: map[string]interface{}{
							"extraction_method": "verb_based",
							"verb":              verb,
						},
					}
					relations = append(relations, relation)
				}
			}
		}
	}
	
	return relations
}

// getTextBetweenEntities 获取实体之间的文本
func (re *RelationExtractor) getTextBetweenEntities(text, entity1, entity2 string) string {
	index1 := strings.Index(text, entity1)
	index2 := strings.Index(text, entity2)
	
	if index1 == -1 || index2 == -1 {
		return ""
	}
	
	start := index1 + len(entity1)
	end := index2
	
	if index2 < index1 {
		start = index2 + len(entity2)
		end = index1
	}
	
	if start >= end {
		return ""
	}
	
	between := strings.TrimSpace(text[start:end])
	return between
}

// identifyRelationType 识别关系类型
func (re *RelationExtractor) identifyRelationType(text string) string {
	text = strings.ToLower(text)
	
	// 简单的关系类型识别
	if strings.Contains(text, "是") || strings.Contains(text, "为") || strings.Contains(text, "is") {
		return "definition"
	} else if strings.Contains(text, "有") || strings.Contains(text, "拥有") || strings.Contains(text, "has") {
		return "possession"
	} else if strings.Contains(text, "在") || strings.Contains(text, "位于") || strings.Contains(text, "in") {
		return "spatial"
	} else if strings.Contains(text, "导致") || strings.Contains(text, "引起") || strings.Contains(text, "causes") {
		return "causal"
	} else if strings.Contains(text, "包含") || strings.Contains(text, "包括") || strings.Contains(text, "contains") {
		return "containment"
	}
	
	return "unknown"
}

// extractSubject 提取主语
func (re *RelationExtractor) extractSubject(text string, verbStart int) string {
	if verbStart <= 0 {
		return ""
	}
	
	// 向前查找主语（简单实现）
	beforeVerb := text[:verbStart]
	words := strings.Fields(beforeVerb)
	
	if len(words) > 0 {
		// 取最后几个词作为主语
		start := len(words) - 3
		if start < 0 {
			start = 0
		}
		return strings.Join(words[start:], " ")
	}
	
	return ""
}

// extractObject 提取宾语
func (re *RelationExtractor) extractObject(text string, verbEnd int) string {
	if verbEnd >= len(text) {
		return ""
	}
	
	// 向后查找宾语（简单实现）
	afterVerb := text[verbEnd:]
	words := strings.Fields(afterVerb)
	
	if len(words) > 0 {
		// 取前几个词作为宾语
		end := 3
		if end > len(words) {
			end = len(words)
		}
		return strings.Join(words[:end], " ")
	}
	
	return ""
}

// calculatePatternConfidence 计算模式置信度
func (re *RelationExtractor) calculatePatternConfidence(relationType string, match []string) float64 {
	baseConfidence := map[string]float64{
		"causal":      0.8,
		"containment": 0.7,
		"comparison":  0.7,
		"temporal":    0.6,
		"spatial":     0.6,
		"possession":  0.7,
		"action":      0.6,
		"definition":  0.8,
	}
	
	confidence := baseConfidence[relationType]
	if confidence == 0 {
		confidence = 0.5
	}
	
	// 根据匹配质量调整置信度
	if len(match) > 3 {
		confidence += 0.1
	}
	
	return confidence
}

// extractPosition 提取位置信息
func (re *RelationExtractor) extractPosition(indices []int, match []string) RelationPosition {
	position := RelationPosition{}
	
	if len(indices) >= 6 {
		position.SubjectStart = indices[2]
		position.SubjectEnd = indices[3]
		position.PredicateStart = indices[4]
		position.PredicateEnd = indices[5]
		
		if len(indices) >= 8 {
			position.ObjectStart = indices[6]
			position.ObjectEnd = indices[7]
		}
	}
	
	return position
}

// filterRelations 过滤关系
func (re *RelationExtractor) filterRelations(relations []Relation) []Relation {
	// 去重
	seen := make(map[string]bool)
	var filtered []Relation
	
	for _, relation := range relations {
		key := relation.Subject + "|" + relation.Predicate + "|" + relation.Object
		if !seen[key] && relation.Subject != "" && relation.Predicate != "" {
			seen[key] = true
			filtered = append(filtered, relation)
		}
	}
	
	// 按置信度排序
	for i := 0; i < len(filtered)-1; i++ {
		for j := i + 1; j < len(filtered); j++ {
			if filtered[i].Confidence < filtered[j].Confidence {
				filtered[i], filtered[j] = filtered[j], filtered[i]
			}
		}
	}
	
	return filtered
}
