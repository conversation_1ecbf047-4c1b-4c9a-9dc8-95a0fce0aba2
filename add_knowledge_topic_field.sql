-- 为learned_knowledge表添加知识点标志字段
ALTER TABLE learned_knowledge 
ADD COLUMN knowledge_topic VARCHAR(100) DEFAULT NULL COMMENT '知识点标志，用于关联相关问题';

-- 添加索引以提高查询性能
CREATE INDEX idx_knowledge_topic ON learned_knowledge(knowledge_topic);

-- 创建知识点管理表（可选，用于管理知识点的元信息）
CREATE TABLE IF NOT EXISTS knowledge_topics (
    id INT AUTO_INCREMENT PRIMARY KEY,
    topic_name VARCHAR(100) NOT NULL UNIQUE COMMENT '知识点名称',
    topic_description TEXT COMMENT '知识点描述',
    category VARCHAR(50) COMMENT '知识点分类',
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    INDEX idx_topic_name (topic_name),
    INDEX idx_category (category)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识点管理表';

-- 插入一些示例知识点
INSERT INTO knowledge_topics (topic_name, topic_description, category) VALUES
('JavaScript基础', 'JavaScript编程语言的基础概念和语法', 'technology'),
('React框架', 'React前端框架相关知识', 'technology'),
('Vue.js框架', 'Vue.js前端框架相关知识', 'technology'),
('Node.js', 'Node.js服务端JavaScript运行环境', 'technology'),
('数据库设计', '数据库设计原理和最佳实践', 'technology'),
('机器学习', '机器学习算法和应用', 'science'),
('深度学习', '深度学习和神经网络', 'science');

-- 更新现有知识的知识点标志（示例）
UPDATE learned_knowledge 
SET knowledge_topic = 'React框架' 
WHERE question LIKE '%React%' OR answer LIKE '%React%';

UPDATE learned_knowledge 
SET knowledge_topic = 'Vue.js框架' 
WHERE question LIKE '%Vue%' OR answer LIKE '%Vue%';

UPDATE learned_knowledge 
SET knowledge_topic = 'Node.js' 
WHERE question LIKE '%Node%' OR answer LIKE '%Node%';

UPDATE learned_knowledge 
SET knowledge_topic = 'JavaScript基础' 
WHERE (question LIKE '%JavaScript%' OR answer LIKE '%JavaScript%') 
AND knowledge_topic IS NULL;
