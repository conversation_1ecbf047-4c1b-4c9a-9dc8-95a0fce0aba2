package main

import (
	"fmt"
	"faq-system/internal/nlp"
)

func main() {
	fmt.Println("🧮 测试Jieba处理器初始化...")
	
	// 创建jieba处理器
	processor := nlp.NewJiebaProcessor()
	defer processor.Close()
	
	// 测试分词
	text := "1+1等于多少"
	words := processor.SegmentText(text)
	fmt.Printf("分词结果: %v\n", words)
	
	// 测试关键词提取
	keywords := processor.ExtractKeywords(text, 5)
	fmt.Printf("关键词: %v\n", keywords)
	
	fmt.Println("✅ Jieba处理器测试完成")
}
