<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试相关知识推荐功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 15px;
            border-radius: 4px;
            white-space: pre-wrap;
            max-height: 500px;
            overflow-y: auto;
            border: 1px solid #ddd;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border-color: #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border-color: #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border-color: #bee5eb;
        }
        .form-group {
            margin-bottom: 15px;
        }
        .form-group label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        .form-group input, .form-group textarea {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .form-group textarea {
            height: 80px;
            resize: vertical;
        }
        .question-examples {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 10px;
            margin-top: 15px;
        }
        .example-btn {
            background: #28a745;
            color: white;
            border: none;
            padding: 8px 12px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 14px;
        }
        .example-btn:hover {
            background: #218838;
        }
        .recommendation-section {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 4px;
            padding: 10px;
            margin: 10px 0;
        }
        .recommendation-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <h1>🔗 相关知识推荐功能测试</h1>
    
    <div class="test-section">
        <h2>📝 自定义问题测试</h2>
        <div class="form-group">
            <label for="customQuestion">输入问题:</label>
            <input type="text" id="customQuestion" placeholder="例如：什么是React？">
        </div>
        <button class="test-button" onclick="testCustomQuestion()">测试问题</button>
        <div id="custom-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🎯 预设问题测试</h2>
        <p>点击下面的问题来测试相关知识推荐功能：</p>
        <div class="question-examples">
            <button class="example-btn" onclick="testQuestion('什么是React？')">什么是React？</button>
            <button class="example-btn" onclick="testQuestion('Vue.js有什么特点？')">Vue.js有什么特点？</button>
            <button class="example-btn" onclick="testQuestion('Node.js是什么？')">Node.js是什么？</button>
            <button class="example-btn" onclick="testQuestion('JavaScript基础语法')">JavaScript基础语法</button>
            <button class="example-btn" onclick="testQuestion('什么是机器学习？')">什么是机器学习？</button>
            <button class="example-btn" onclick="testQuestion('数据库设计原则')">数据库设计原则</button>
            <button class="example-btn" onclick="testQuestion('Go语言优势')">Go语言优势</button>
            <button class="example-btn" onclick="testQuestion('Python编程')">Python编程</button>
        </div>
        <div id="preset-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 推荐效果分析</h2>
        <button class="test-button" onclick="analyzeRecommendations()">分析推荐效果</button>
        <div id="analysis-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🔍 知识点覆盖测试</h2>
        <p>测试不同知识点的推荐覆盖情况：</p>
        <button class="test-button" onclick="testKnowledgeTopicCoverage()">测试知识点覆盖</button>
        <div id="coverage-result" class="result"></div>
    </div>

    <script>
        const baseURL = 'http://localhost:8082';
        let testResults = [];

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 测试自定义问题
        async function testCustomQuestion() {
            const question = document.getElementById('customQuestion').value.trim();
            if (!question) {
                showResult('custom-result', '❌ 请输入问题', 'error');
                return;
            }
            
            await testQuestion(question, 'custom-result');
        }

        // 测试问题
        async function testQuestion(question, resultElementId = 'preset-result') {
            showResult(resultElementId, `正在测试问题: ${question}`, 'info');
            
            try {
                const response = await fetch(`${baseURL}/ask`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify({ question: question })
                });

                const result = await response.json();
                
                if (response.ok) {
                    // 分析回答中是否包含相关知识推荐
                    const hasRecommendations = result.answer.includes('您可能还想了解') || 
                                             result.answer.includes('相关问题推荐') ||
                                             result.answer.includes('🔗') ||
                                             result.answer.includes('🔍');
                    
                    let message = `✅ 测试成功！\n\n`;
                    message += `❓ 问题: ${question}\n\n`;
                    message += `🤖 回答:\n${result.answer}\n\n`;
                    message += `📊 来源: ${result.source}\n`;
                    message += `🎯 置信度: ${result.confidence || 0}\n`;
                    message += `⏱️ 响应时间: ${result.duration || '未知'}\n\n`;
                    
                    if (hasRecommendations) {
                        message += `🎉 检测到相关知识推荐！\n`;
                        // 提取推荐部分
                        const recommendationMatch = result.answer.match(/(🔗.*?您可能还想了解.*?)(?:\n\n|$)/s) ||
                                                  result.answer.match(/(🔍.*?相关问题推荐.*?)(?:\n\n|$)/s);
                        if (recommendationMatch) {
                            message += `\n推荐内容:\n${recommendationMatch[1]}\n`;
                        }
                    } else {
                        message += `⚠️ 未检测到相关知识推荐\n`;
                    }
                    
                    // 记录测试结果
                    testResults.push({
                        question: question,
                        hasRecommendations: hasRecommendations,
                        source: result.source,
                        confidence: result.confidence || 0,
                        timestamp: new Date().toISOString()
                    });
                    
                    showResult(resultElementId, message, hasRecommendations ? 'success' : 'info');
                } else {
                    showResult(resultElementId, `❌ 测试失败：${result.error || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultElementId, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 分析推荐效果
        async function analyzeRecommendations() {
            const resultDiv = 'analysis-result';
            
            if (testResults.length === 0) {
                showResult(resultDiv, '❌ 请先进行一些问题测试', 'error');
                return;
            }
            
            showResult(resultDiv, '正在分析推荐效果...', 'info');
            
            // 统计分析
            const totalTests = testResults.length;
            const withRecommendations = testResults.filter(r => r.hasRecommendations).length;
            const recommendationRate = (withRecommendations / totalTests * 100).toFixed(1);
            
            // 按来源分组
            const sourceStats = {};
            testResults.forEach(result => {
                if (!sourceStats[result.source]) {
                    sourceStats[result.source] = { total: 0, withRec: 0 };
                }
                sourceStats[result.source].total++;
                if (result.hasRecommendations) {
                    sourceStats[result.source].withRec++;
                }
            });
            
            let message = `📊 推荐效果分析报告\n\n`;
            message += `总测试数: ${totalTests}\n`;
            message += `包含推荐: ${withRecommendations}\n`;
            message += `推荐率: ${recommendationRate}%\n\n`;
            
            message += `按来源统计:\n`;
            Object.entries(sourceStats).forEach(([source, stats]) => {
                const rate = (stats.withRec / stats.total * 100).toFixed(1);
                message += `  ${source}: ${stats.withRec}/${stats.total} (${rate}%)\n`;
            });
            
            message += `\n详细结果:\n`;
            testResults.forEach((result, index) => {
                message += `${index + 1}. ${result.question}\n`;
                message += `   推荐: ${result.hasRecommendations ? '✅' : '❌'} | 来源: ${result.source}\n`;
            });
            
            showResult(resultDiv, message, 'success');
        }

        // 测试知识点覆盖
        async function testKnowledgeTopicCoverage() {
            const resultDiv = 'coverage-result';
            showResult(resultDiv, '正在测试知识点覆盖情况...', 'info');
            
            try {
                // 获取所有知识点
                const topicsResponse = await fetch(`${baseURL}/api/learning/topics`);
                const topicsResult = await topicsResponse.json();
                
                if (!topicsResponse.ok || !topicsResult.success) {
                    showResult(resultDiv, `❌ 获取知识点失败：${topicsResult.message || '未知错误'}`, 'error');
                    return;
                }
                
                let message = `📚 知识点覆盖测试\n\n`;
                message += `发现 ${topicsResult.total} 个知识点:\n`;
                
                for (const topic of topicsResult.data) {
                    message += `\n🏷️ ${topic}\n`;
                    
                    // 获取该知识点的知识数量
                    try {
                        const knowledgeResponse = await fetch(`${baseURL}/api/learning/knowledge?limit=100`);
                        const knowledgeResult = await knowledgeResponse.json();
                        
                        if (knowledgeResponse.ok && knowledgeResult.success) {
                            const topicKnowledge = knowledgeResult.data.filter(k => k.knowledge_topic === topic);
                            message += `   知识数量: ${topicKnowledge.length}\n`;
                            
                            if (topicKnowledge.length > 0) {
                                message += `   示例问题:\n`;
                                topicKnowledge.slice(0, 3).forEach(k => {
                                    message += `     • ${k.question}\n`;
                                });
                            }
                        }
                    } catch (error) {
                        message += `   获取知识失败: ${error.message}\n`;
                    }
                }
                
                showResult(resultDiv, message, 'success');
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 页面加载时的初始化
        window.addEventListener('load', function() {
            console.log('相关知识推荐测试页面已加载');
        });
    </script>
</body>
</html>
