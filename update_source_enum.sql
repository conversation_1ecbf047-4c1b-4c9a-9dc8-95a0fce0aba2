-- 更新learned_knowledge表的source字段ENUM定义
-- 添加新的source类型：manual_input, api_input

USE faqdb;

-- 1. 检查当前的source字段定义
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'faqdb' 
  AND TABLE_NAME = 'learned_knowledge' 
  AND COLUMN_NAME = 'source';

-- 2. 更新source字段的ENUM定义
ALTER TABLE learned_knowledge 
MODIFY COLUMN source ENUM(
    'user_input', 
    'conversation', 
    'correction', 
    'implicit', 
    'crawler', 
    'manual_input', 
    'api_input'
) NOT NULL COMMENT '知识来源';

-- 3. 验证更新后的字段定义
SELECT 
    COLUMN_NAME,
    COLUMN_TYPE,
    IS_NULLABLE,
    COLUMN_DEFAULT
FROM INFORMATION_SCHEMA.COLUMNS 
WHERE TABLE_SCHEMA = 'faqdb' 
  AND TABLE_NAME = 'learned_knowledge' 
  AND COLUMN_NAME = 'source';

-- 4. 显示当前表中的source值分布
SELECT source, COUNT(*) as count 
FROM learned_knowledge 
GROUP BY source 
ORDER BY count DESC;

SELECT 'Source ENUM update completed successfully!' as status;
