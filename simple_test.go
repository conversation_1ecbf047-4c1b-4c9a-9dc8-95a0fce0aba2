package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(localhost:3306)/faq_system?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	// 检查数据库中的知识点标志
	fmt.Printf("📊 检查数据库中的知识点标志:\n")
	
	query := `
		SELECT id, question, knowledge_topic, status
		FROM learned_knowledge
		WHERE knowledge_topic IS NOT NULL AND knowledge_topic != ''
		ORDER BY knowledge_topic, id
	`
	
	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()
	
	count := 0
	currentTopic := ""
	for rows.Next() {
		var id int
		var question, knowledgeTopic, status string
		
		err := rows.Scan(&id, &question, &knowledgeTopic, &status)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}
		
		if knowledgeTopic != currentTopic {
			if currentTopic != "" {
				fmt.Printf("\n")
			}
			fmt.Printf("🏷️ 知识点: %s\n", knowledgeTopic)
			currentTopic = knowledgeTopic
		}
		
		fmt.Printf("   ID:%d - %s (状态:%s)\n", id, question, status)
		count++
	}
	
	fmt.Printf("\n📊 总共找到 %d 个有知识点标志的知识\n\n", count)

	// 测试查询相关知识
	fmt.Printf("🔍 测试查询相关知识: 知识点='阿萨技术', 排除ID=123\n")

	relatedQuery := `
		SELECT id, question, answer, knowledge_topic, status
		FROM learned_knowledge
		WHERE knowledge_topic = ? AND id != ? AND status = 'approved'
		ORDER BY confidence DESC, created_at DESC
		LIMIT 3
	`

	rows2, err := db.Query(relatedQuery, "阿萨技术", 123)
	if err != nil {
		fmt.Printf("❌ 查询相关知识失败: %v\n", err)
		return
	}
	defer rows2.Close()

	relatedCount := 0
	for rows2.Next() {
		var id int
		var question, answer, knowledgeTopic, status string

		err := rows2.Scan(&id, &question, &answer, &knowledgeTopic, &status)
		if err != nil {
			fmt.Printf("❌ 扫描相关知识失败: %v\n", err)
			continue
		}

		relatedCount++
		fmt.Printf("✅ 找到相关知识 %d:\n", relatedCount)
		fmt.Printf("   ID: %d\n", id)
		fmt.Printf("   问题: %s\n", question)
		fmt.Printf("   答案: %s\n", answer)
		fmt.Printf("   知识点: %s\n", knowledgeTopic)
		fmt.Printf("   状态: %s\n", status)
		fmt.Printf("\n")
	}

	if relatedCount == 0 {
		fmt.Printf("❌ 没有找到相关知识\n")
		
		// 检查是否有其他状态的知识
		fmt.Printf("\n🔍 检查所有状态的'阿萨技术'知识:\n")
		allStatusQuery := `
			SELECT id, question, knowledge_topic, status
			FROM learned_knowledge
			WHERE knowledge_topic = ?
			ORDER BY id
		`
		
		rows3, err := db.Query(allStatusQuery, "阿萨技术")
		if err != nil {
			fmt.Printf("❌ 查询所有状态失败: %v\n", err)
			return
		}
		defer rows3.Close()
		
		allCount := 0
		for rows3.Next() {
			var id int
			var question, knowledgeTopic, status string
			
			err := rows3.Scan(&id, &question, &knowledgeTopic, &status)
			if err != nil {
				continue
			}
			
			allCount++
			fmt.Printf("   ID:%d - %s (状态:%s)\n", id, question, status)
		}
		
		fmt.Printf("📊 '阿萨技术'知识点总数: %d\n", allCount)
	} else {
		fmt.Printf("📊 找到 %d 个相关知识\n", relatedCount)
		
		// 测试推荐格式化
		fmt.Printf("\n🔗 推荐格式化测试:\n")
		fmt.Printf("🔗 **您可能还想了解 (阿萨技术)：**\n")
		
		rows4, err := db.Query(relatedQuery, "阿萨技术", 123)
		if err == nil {
			defer rows4.Close()
			for rows4.Next() {
				var id int
				var question, answer, knowledgeTopic, status string
				
				err := rows4.Scan(&id, &question, &answer, &knowledgeTopic, &status)
				if err == nil {
					fmt.Printf("• %s\n", question)
				}
			}
		}
	}
}
