package nlp

import (
	"log"
	"regexp"
	"strings"
	"time"
)

// GreetingRecognizer 问候语识别器 - 专门用于识别各种问候语
type GreetingRecognizer struct {
	patterns    map[string][]*regexp.Regexp
	keywords    map[string][]string
	timeBasedGreetings map[string][]string
	initialized bool
}

// GreetingResult 问候语识别结果
type GreetingResult struct {
	IsGreeting      bool                   `json:"is_greeting"`
	GreetingType    string                 `json:"greeting_type"`    // basic, time_based, care, farewell, group
	TimeContext     string                 `json:"time_context"`     // morning, afternoon, evening, night
	Formality       string                 `json:"formality"`        // formal, informal, casual
	Confidence      float64                `json:"confidence"`
	MatchedPatterns []string               `json:"matched_patterns"`
	MatchedKeywords []string               `json:"matched_keywords"`
	Attributes      map[string]interface{} `json:"attributes"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// NewGreetingRecognizer 创建问候语识别器
func NewGreetingRecognizer() *GreetingRecognizer {
	gr := &GreetingRecognizer{
		patterns:           make(map[string][]*regexp.Regexp),
		keywords:           make(map[string][]string),
		timeBasedGreetings: make(map[string][]string),
	}
	
	gr.initializePatterns()
	gr.initializeKeywords()
	gr.initializeTimeBasedGreetings()
	gr.initialized = true
	
	log.Printf("✅ 问候语识别器初始化完成")
	return gr
}

// initializePatterns 初始化模式
func (gr *GreetingRecognizer) initializePatterns() {
	// 基础问候语
	gr.addPattern("basic", `^(你好|您好|hi|hello|嗨|hey)$`)
	gr.addPattern("basic", `^(你好|您好|hi|hello|嗨|hey)[！!。.]*$`)
	
	// 时间相关问候语
	gr.addPattern("time_based", `^(早上好|上午好|中午好|下午好|晚上好|晚安|good morning|good afternoon|good evening|good night)$`)
	gr.addPattern("time_based", `^(早上好|上午好|中午好|下午好|晚上好|晚安|good morning|good afternoon|good evening|good night)[！!。.]*$`)
	gr.addPattern("time_based", `^(早|早啊|晚安|安)$`)
	
	// 群体问候语
	gr.addPattern("group", `^(大家好|各位好|朋友们好|小伙伴们好|同学们好|老师好|同事们好)$`)
	gr.addPattern("group", `^(大家好|各位好|朋友们好|小伙伴们好|同学们好|老师好|同事们好)[！!。.]*$`)
	
	// 关怀问候语
	gr.addPattern("care", `^(注意身体|保重|多保重|身体健康|注意休息|早点休息|好好休息)$`)
	gr.addPattern("care", `^(注意身体|保重|多保重|身体健康|注意休息|早点休息|好好休息)[！!。.]*$`)
	gr.addPattern("care", `(身体|健康|保重|休息|注意)`)
	
	// 告别问候语
	gr.addPattern("farewell", `^(再见|拜拜|bye|goodbye|see you|回见|一会见|明天见|下次见|88)$`)
	gr.addPattern("farewell", `^(再见|拜拜|bye|goodbye|see you|回见|一会见|明天见|下次见|88)[！!。.]*$`)
	
	// 礼貌问候语
	gr.addPattern("polite", `^(久仰|久违|幸会|初次见面|很高兴见到你|很高兴认识你|请多指教|请多关照)$`)
	gr.addPattern("polite", `^(久仰|久违|幸会|初次见面|很高兴见到你|很高兴认识你|请多指教|请多关照)[！!。.]*$`)
	
	// 节日问候语
	gr.addPattern("festival", `^(新年快乐|春节快乐|生日快乐|节日快乐|圣诞快乐|元旦快乐|中秋快乐|国庆快乐)$`)
	gr.addPattern("festival", `^(新年快乐|春节快乐|生日快乐|节日快乐|圣诞快乐|元旦快乐|中秋快乐|国庆快乐)[！!。.]*$`)
}

// initializeKeywords 初始化关键词
func (gr *GreetingRecognizer) initializeKeywords() {
	gr.keywords["basic"] = []string{
		"你好", "您好", "hi", "hello", "嗨", "hey", "哈喽",
	}
	
	gr.keywords["time_based"] = []string{
		"早上好", "上午好", "中午好", "下午好", "晚上好", "晚安",
		"good morning", "good afternoon", "good evening", "good night",
		"早", "早啊", "安", "morning", "evening", "night",
	}
	
	gr.keywords["group"] = []string{
		"大家好", "各位好", "朋友们好", "小伙伴们好", "同学们好", "老师好", "同事们好",
		"大家", "各位", "朋友们", "小伙伴们", "同学们",
	}
	
	gr.keywords["care"] = []string{
		"注意身体", "保重", "多保重", "身体健康", "注意休息", "早点休息", "好好休息",
		"身体", "健康", "保重", "休息", "注意", "照顾", "当心",
	}
	
	gr.keywords["farewell"] = []string{
		"再见", "拜拜", "bye", "goodbye", "see you", "回见", "一会见", "明天见", "下次见", "88",
	}
	
	gr.keywords["polite"] = []string{
		"久仰", "久违", "幸会", "初次见面", "很高兴见到你", "很高兴认识你", "请多指教", "请多关照",
	}
	
	gr.keywords["festival"] = []string{
		"新年快乐", "春节快乐", "生日快乐", "节日快乐", "圣诞快乐", "元旦快乐", "中秋快乐", "国庆快乐",
		"快乐", "祝福", "节日", "生日", "新年", "春节", "圣诞", "中秋", "国庆",
	}
}

// initializeTimeBasedGreetings 初始化时间相关问候语
func (gr *GreetingRecognizer) initializeTimeBasedGreetings() {
	gr.timeBasedGreetings["morning"] = []string{
		"早上好", "上午好", "早", "早啊", "good morning", "morning",
	}
	
	gr.timeBasedGreetings["afternoon"] = []string{
		"中午好", "下午好", "good afternoon", "afternoon",
	}
	
	gr.timeBasedGreetings["evening"] = []string{
		"晚上好", "good evening", "evening",
	}
	
	gr.timeBasedGreetings["night"] = []string{
		"晚安", "good night", "night", "安",
	}
}

// addPattern 添加模式
func (gr *GreetingRecognizer) addPattern(category string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 问候语识别器编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	gr.patterns[category] = append(gr.patterns[category], regex)
}

// RecognizeGreeting 识别问候语
func (gr *GreetingRecognizer) RecognizeGreeting(text string) GreetingResult {
	if !gr.initialized {
		return GreetingResult{
			IsGreeting: false,
			Confidence: 0.0,
			Attributes: make(map[string]interface{}),
			Metadata:   make(map[string]interface{}),
		}
	}
	
	textLower := strings.ToLower(strings.TrimSpace(text))
	textOriginal := strings.TrimSpace(text)
	
	result := GreetingResult{
		MatchedPatterns: []string{},
		MatchedKeywords: []string{},
		Attributes:      make(map[string]interface{}),
		Metadata: map[string]interface{}{
			"original_text": text,
			"text_length":   len(text),
			"word_count":    len(strings.Fields(text)),
		},
	}
	
	// 1. 模式匹配
	patternScore := gr.matchPatterns(textLower, &result)
	
	// 2. 关键词匹配
	keywordScore := gr.matchKeywords(textLower, &result)
	
	// 3. 时间上下文分析
	timeScore := gr.analyzeTimeContext(textLower, &result)
	
	// 4. 特殊规则
	specialScore := gr.applySpecialRules(textOriginal, textLower, &result)
	
	// 5. 计算综合置信度
	totalScore := patternScore + keywordScore + timeScore + specialScore
	result.Confidence = gr.normalizeScore(totalScore)
	
	// 6. 判断是否为问候语
	result.IsGreeting = result.Confidence >= 0.5
	
	// 7. 确定问候语类型和正式程度
	if result.IsGreeting {
		result.GreetingType = gr.determineGreetingType(&result)
		result.Formality = gr.determineFormality(textOriginal, &result)
	}
	
	log.Printf("👋 问候语识别: %s -> %t (%.2f) [%s/%s]", 
		text, result.IsGreeting, result.Confidence, result.GreetingType, result.TimeContext)
	
	return result
}

// matchPatterns 模式匹配
func (gr *GreetingRecognizer) matchPatterns(text string, result *GreetingResult) float64 {
	score := 0.0
	
	for category, patterns := range gr.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				score += gr.getPatternWeight(category)
				result.MatchedPatterns = append(result.MatchedPatterns, category)
				result.Attributes[category+"_matched"] = true
			}
		}
	}
	
	return score
}

// matchKeywords 关键词匹配
func (gr *GreetingRecognizer) matchKeywords(text string, result *GreetingResult) float64 {
	score := 0.0
	
	for category, keywords := range gr.keywords {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				score += gr.getKeywordWeight(category)
				result.MatchedKeywords = append(result.MatchedKeywords, keyword)
			}
		}
	}
	
	return score
}

// analyzeTimeContext 分析时间上下文
func (gr *GreetingRecognizer) analyzeTimeContext(text string, result *GreetingResult) float64 {
	score := 0.0
	
	for timeContext, greetings := range gr.timeBasedGreetings {
		for _, greeting := range greetings {
			if strings.Contains(text, greeting) {
				result.TimeContext = timeContext
				score += 0.5
				result.Attributes["time_context_matched"] = true
				break
			}
		}
		if result.TimeContext != "" {
			break
		}
	}
	
	// 根据当前时间增强时间相关问候语的识别
	currentHour := time.Now().Hour()
	if result.TimeContext != "" {
		if (result.TimeContext == "morning" && currentHour >= 6 && currentHour < 12) ||
		   (result.TimeContext == "afternoon" && currentHour >= 12 && currentHour < 18) ||
		   (result.TimeContext == "evening" && currentHour >= 18 && currentHour < 22) ||
		   (result.TimeContext == "night" && (currentHour >= 22 || currentHour < 6)) {
			score += 0.3 // 时间匹配加分
			result.Attributes["time_appropriate"] = true
		}
	}
	
	return score
}

// applySpecialRules 应用特殊规则
func (gr *GreetingRecognizer) applySpecialRules(textOriginal, textLower string, result *GreetingResult) float64 {
	score := 0.0
	
	// 1. 短文本更可能是问候语
	if len(textOriginal) <= 10 {
		score += 0.3
		result.Attributes["short_text"] = true
	}
	
	// 2. 包含感叹号表示热情
	if strings.Contains(textOriginal, "!") || strings.Contains(textOriginal, "！") {
		score += 0.2
		result.Attributes["enthusiastic"] = true
	}
	
	// 3. 重复字符表示亲切
	if strings.Contains(textLower, "哈哈") || strings.Contains(textLower, "嘿嘿") {
		score += 0.1
		result.Attributes["friendly"] = true
	}
	
	// 4. 表情符号增强问候语识别
	emojis := []string{"😊", "😄", "😃", "🙂", "👋", "🤝", "😁", "😆"}
	for _, emoji := range emojis {
		if strings.Contains(textOriginal, emoji) {
			score += 0.3
			result.Attributes["has_emoji"] = true
			break
		}
	}
	
	// 5. 排除技术问题的误判
	techKeywords := []string{"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置", "算法", "函数"}
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			score *= 0.1 // 大幅降低分数
			result.Attributes["has_tech_keyword"] = true
			break
		}
	}
	
	return score
}

// getPatternWeight 获取模式权重
func (gr *GreetingRecognizer) getPatternWeight(category string) float64 {
	weights := map[string]float64{
		"basic":    1.0,
		"time_based": 1.2,
		"group":    1.1,
		"care":     0.9,
		"farewell": 1.0,
		"polite":   1.1,
		"festival": 1.3,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.5
}

// getKeywordWeight 获取关键词权重
func (gr *GreetingRecognizer) getKeywordWeight(category string) float64 {
	weights := map[string]float64{
		"basic":      0.4,
		"time_based": 0.5,
		"group":      0.4,
		"care":       0.3,
		"farewell":   0.4,
		"polite":     0.4,
		"festival":   0.5,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.2
}

// normalizeScore 标准化分数
func (gr *GreetingRecognizer) normalizeScore(score float64) float64 {
	// 使用sigmoid函数进行标准化
	if score <= 0 {
		return 0.0
	}
	normalized := 1.0 / (1.0 + 1.0/score)
	if normalized > 1.0 {
		normalized = 1.0
	}
	return normalized
}

// determineGreetingType 确定问候语类型
func (gr *GreetingRecognizer) determineGreetingType(result *GreetingResult) string {
	// 根据匹配的模式确定类型
	for _, pattern := range result.MatchedPatterns {
		if pattern != "basic" {
			return pattern
		}
	}
	
	// 如果有时间上下文，返回time_based
	if result.TimeContext != "" {
		return "time_based"
	}
	
	return "basic"
}

// determineFormality 确定正式程度
func (gr *GreetingRecognizer) determineFormality(text string, result *GreetingResult) string {
	// 正式用语
	formalWords := []string{"您好", "久仰", "幸会", "请多指教", "请多关照"}
	for _, word := range formalWords {
		if strings.Contains(text, word) {
			return "formal"
		}
	}
	
	// 非正式用语
	informalWords := []string{"嗨", "hey", "哈喽", "早啊", "拜拜", "88"}
	for _, word := range informalWords {
		if strings.Contains(text, word) {
			return "informal"
		}
	}
	
	// 包含感叹号或表情符号通常比较随意
	if result.Attributes["enthusiastic"] == true || result.Attributes["has_emoji"] == true {
		return "casual"
	}
	
	return "neutral"
}

// IsInitialized 检查是否已初始化
func (gr *GreetingRecognizer) IsInitialized() bool {
	return gr.initialized
}
