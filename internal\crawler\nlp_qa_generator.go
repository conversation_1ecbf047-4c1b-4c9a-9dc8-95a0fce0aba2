package crawler

import (
	"fmt"
	"strings"

	"faq-system/internal/nlp"
)

// generateRealtimeQA 生成实时热点问答
func (sc *SmartKnowledgeCrawler) generateRealtimeQA(extracted *nlp.ExtractedContent) (string, string) {
	if len(extracted.Topics) == 0 {
		return "", ""
	}
	
	question := "当前实时热点有哪些？"
	
	topicList := []string{}
	for i, topic := range extracted.Topics {
		if i >= 10 { // 最多显示10个
			break
		}
		
		topicStr := fmt.Sprintf("%d. %s", topic.Rank, topic.Title)
		if topic.HotValue != "" {
			topicStr += fmt.Sprintf(" (热度: %s)", topic.HotValue)
		}
		if topic.Category != "" && topic.Category != "综合" {
			topicStr += fmt.Sprintf(" [%s]", topic.Category)
		}
		topicList = append(topicList, topicStr)
	}
	
	answer := fmt.Sprintf("根据最新的实时热点榜，当前热门话题包括：\n%s", strings.Join(topicList, "\n"))
	
	return question, answer
}

// generateNovelQA 生成小说问答
func (sc *SmartKnowledgeCrawler) generateNovelQA(extracted *nlp.ExtractedContent) (string, string) {
	if len(extracted.Topics) == 0 {
		return "", ""
	}
	
	question := "当前热门小说有哪些？"
	
	novelList := []string{}
	for i, topic := range extracted.Topics {
		if i >= 10 {
			break
		}
		novelList = append(novelList, fmt.Sprintf("%d. %s", topic.Rank, topic.Title))
	}
	
	answer := fmt.Sprintf("根据最新的小说榜单，当前热门小说包括：\n%s", strings.Join(novelList, "\n"))
	
	return question, answer
}

// generateMovieQA 生成电影问答
func (sc *SmartKnowledgeCrawler) generateMovieQA(extracted *nlp.ExtractedContent) (string, string) {
	if len(extracted.Topics) == 0 {
		return "", ""
	}
	
	question := "当前热门电影有哪些？"
	
	movieList := []string{}
	for i, topic := range extracted.Topics {
		if i >= 10 {
			break
		}
		movieList = append(movieList, fmt.Sprintf("%d. %s", topic.Rank, topic.Title))
	}
	
	answer := fmt.Sprintf("根据最新的电影榜单，当前热门电影包括：\n%s", strings.Join(movieList, "\n"))
	
	return question, answer
}

// generateTeleplayQA 生成电视剧问答
func (sc *SmartKnowledgeCrawler) generateTeleplayQA(extracted *nlp.ExtractedContent) (string, string) {
	if len(extracted.Topics) == 0 {
		return "", ""
	}
	
	question := "当前热门电视剧有哪些？"
	
	teleplayList := []string{}
	for i, topic := range extracted.Topics {
		if i >= 10 {
			break
		}
		teleplayList = append(teleplayList, fmt.Sprintf("%d. %s", topic.Rank, topic.Title))
	}
	
	answer := fmt.Sprintf("根据最新的电视剧榜单，当前热门电视剧包括：\n%s", strings.Join(teleplayList, "\n"))
	
	return question, answer
}

// generateHotSearchQA 生成热搜问答
func (sc *SmartKnowledgeCrawler) generateHotSearchQA(extracted *nlp.ExtractedContent) (string, string) {
	if len(extracted.Topics) == 0 {
		return "", ""
	}
	
	question := fmt.Sprintf("当前%s上有哪些热门话题？", extracted.Title)
	
	topicList := []string{}
	for i, topic := range extracted.Topics {
		if i >= 15 { // 热搜榜显示更多内容
			break
		}
		
		topicStr := fmt.Sprintf("%d. %s", topic.Rank, topic.Title)
		if topic.HotValue != "" {
			topicStr += fmt.Sprintf(" (热度: %s)", topic.HotValue)
		}
		if topic.Category != "" && topic.Category != "综合" {
			topicStr += fmt.Sprintf(" [%s]", topic.Category)
		}
		topicList = append(topicList, topicStr)
	}
	
	answer := fmt.Sprintf("根据最新的%s，当前热门话题包括：\n%s", extracted.Title, strings.Join(topicList, "\n"))
	
	return question, answer
}

// generateGeneralQA 生成通用问答
func (sc *SmartKnowledgeCrawler) generateGeneralQA(extracted *nlp.ExtractedContent) (string, string) {
	// 基于智能标题生成问题
	question := fmt.Sprintf("关于%s有什么信息？", extracted.Title)
	
	// 使用智能摘要作为答案
	answer := extracted.Summary
	
	// 如果有话题，补充话题信息
	if len(extracted.Topics) > 0 {
		topicTitles := []string{}
		for i, topic := range extracted.Topics {
			if i >= 5 { // 最多5个话题
				break
			}
			topicTitles = append(topicTitles, topic.Title)
		}
		
		if len(topicTitles) > 0 {
			answer += "\n\n相关话题包括：" + strings.Join(topicTitles, "、")
		}
	}
	
	return question, answer
}
