package recommendation

import (
	"faq-system/internal/learning"
	"faq-system/internal/logger"
)

// TopicBasedStrategy 基于知识点的推荐策略
type TopicBasedStrategy struct {
	knowledgeLearner *learning.KnowledgeLearner
	config           *RecommendationConfig
}

// NewTopicBasedStrategy 创建基于知识点的推荐策略
func NewTopicBasedStrategy(knowledgeLearner *learning.KnowledgeLearner, config *RecommendationConfig) *TopicBasedStrategy {
	return &TopicBasedStrategy{
		knowledgeLearner: knowledgeLearner,
		config:           config,
	}
}

// GetStrategyName 获取策略名称
func (s *TopicBasedStrategy) GetStrategyName() string {
	return "topic_based"
}

// IsApplicable 判断策略是否适用
func (s *TopicBasedStrategy) IsApplicable(ctx *RecommendationContext) bool {
	// 需要有知识点标志
	return ctx.KnowledgeTopic != ""
}

// GetRecommendations 获取推荐
func (s *TopicBasedStrategy) GetRecommendations(ctx *RecommendationContext) (*RecommendationResult, error) {
	logger.Infof("🏷️ 基于知识点推荐: 知识点='%s', 排除ID=%d", ctx.KnowledgeTopic, ctx.KnowledgeID)

	// 从知识库中获取相关知识
	relatedKnowledge, err := s.knowledgeLearner.GetRelatedKnowledgeByTopic(
		ctx.KnowledgeTopic,
		ctx.KnowledgeID,
		s.config.MaxRecommendations,
	)
	if err != nil {
		logger.Warnf("❌ 获取相关知识失败: %v", err)
		return nil, err
	}

	if len(relatedKnowledge) == 0 {
		logger.Infof("❌ 没有找到相关知识")
		return &RecommendationResult{
			Success:            false,
			RecommendationType: s.GetStrategyName(),
			KnowledgeTopic:     ctx.KnowledgeTopic,
			Items:              []*RecommendationItem{},
			FormattedText:      "",
			Metadata: map[string]interface{}{
				"reason": "no_related_knowledge",
				"topic":  ctx.KnowledgeTopic,
			},
		}, nil
	}

	// 转换为推荐项目
	items := make([]*RecommendationItem, 0, len(relatedKnowledge))
	for _, knowledge := range relatedKnowledge {
		// 过滤低置信度的知识
		if float64(knowledge.Confidence) < s.config.MinConfidence {
			logger.Infof("⏭️ 跳过低置信度知识: ID=%d, 置信度=%.2f", knowledge.ID, knowledge.Confidence)
			continue
		}

		item := &RecommendationItem{
			ID:         knowledge.ID,
			Question:   knowledge.Question,
			Answer:     knowledge.Answer,
			Confidence: float64(knowledge.Confidence),
			Relevance:  1.0, // 基于知识点的推荐相关性为1.0
			Topic:      knowledge.KnowledgeTopic,
			Source:     knowledge.Source,
			CreatedAt:  knowledge.CreatedAt,
			Knowledge:  knowledge,
		}
		items = append(items, item)

		// 限制推荐数量
		if len(items) >= s.config.MaxRecommendations {
			break
		}
	}

	if len(items) == 0 {
		logger.Infof("❌ 过滤后没有符合条件的知识")
		return &RecommendationResult{
			Success:            false,
			RecommendationType: s.GetStrategyName(),
			KnowledgeTopic:     ctx.KnowledgeTopic,
			Items:              []*RecommendationItem{},
			FormattedText:      "",
			Metadata: map[string]interface{}{
				"reason":         "low_confidence_filtered",
				"topic":          ctx.KnowledgeTopic,
				"min_confidence": s.config.MinConfidence,
			},
		}, nil
	}

	logger.Infof("✅ 基于知识点推荐成功: 找到 %d 个相关知识", len(items))

	return &RecommendationResult{
		Success:            true,
		RecommendationType: s.GetStrategyName(),
		KnowledgeTopic:     ctx.KnowledgeTopic,
		Items:              items,
		FormattedText:      "", // 将由服务层格式化
		Metadata: map[string]interface{}{
			"strategy":       s.GetStrategyName(),
			"topic":          ctx.KnowledgeTopic,
			"total_found":    len(relatedKnowledge),
			"after_filter":   len(items),
			"min_confidence": s.config.MinConfidence,
		},
	}, nil
}
