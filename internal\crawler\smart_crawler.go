package crawler

import (
	"context"
	"database/sql"
	"fmt"
	"log"
	"math/rand"
	"net/http"
	"regexp"
	"sort"
	"strings"
	"sync"
	"time"
	"unicode/utf8"

	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/nlp"

	"github.com/PuerkitoBio/goquery"
	"github.com/chromedp/chromedp"
)

// SmartCrawlerConfig 智能爬虫配置
type SmartCrawlerConfig struct {
	MaxDepth            int           `json:"max_depth"`             // 最大爬取深度
	MaxPagesPerDomain   int           `json:"max_pages_per_domain"`  // 每个域名最大页面数
	ContentMinLength    int           `json:"content_min_length"`    // 内容最小长度
	ContentQualityScore float64       `json:"content_quality_score"` // 内容质量阈值
	EnableJSRendering   bool          `json:"enable_js_rendering"`   // 启用JavaScript渲染
	RespectRobotsTxt    bool          `json:"respect_robots_txt"`    // 遵守robots.txt
	UserAgentRotation   []string      `json:"user_agent_rotation"`   // User-Agent轮换
	ProxyList           []string      `json:"proxy_list"`            // 代理列表
	RequestDelay        time.Duration `json:"request_delay"`         // 请求延迟
	RandomDelay         bool          `json:"random_delay"`          // 随机延迟
	MaxConcurrency      int           `json:"max_concurrency"`       // 最大并发数
	Timeout             time.Duration `json:"timeout"`               // 请求超时

	// 链接数量限制
	MaxLinksPerPage  int    `json:"max_links_per_page"`  // 每页最大链接数
	MaxLinksPerDepth int    `json:"max_links_per_depth"` // 每层最大链接数
	MaxTotalLinks    int    `json:"max_total_links"`     // 总最大链接数
	LinkPriorityMode string `json:"link_priority_mode"`  // 链接优先级模式: quality, keyword, random
}

// CrawlRule 爬取规则
type CrawlRule struct {
	URLPattern      string            `json:"url_pattern"`      // URL匹配模式
	FollowLinks     []string          `json:"follow_links"`     // 要跟踪的链接选择器
	IgnoreLinks     []string          `json:"ignore_links"`     // 要忽略的链接选择器
	ContentSelector string            `json:"content_selector"` // 内容选择器
	TitleSelector   string            `json:"title_selector"`   // 标题选择器
	MetaSelectors   map[string]string `json:"meta_selectors"`   // 元数据选择器
	RequiredWords   []string          `json:"required_words"`   // 必须包含的词汇
	ExcludedWords   []string          `json:"excluded_words"`   // 排除的词汇
	MinWordCount    int               `json:"min_word_count"`   // 最小词数
	MaxDepth        int               `json:"max_depth"`        // 此规则的最大深度
}

// SmartCrawlTarget 智能爬取目标
type SmartCrawlTarget struct {
	*CrawlTarget
	Rules            []CrawlRule `json:"rules"`             // 爬取规则
	SeedURLs         []string    `json:"seed_urls"`         // 种子URL
	AllowedDomains   []string    `json:"allowed_domains"`   // 允许的域名
	BlockedDomains   []string    `json:"blocked_domains"`   // 禁止的域名
	CrawlStrategy    string      `json:"crawl_strategy"`    // 爬取策略: breadth_first, depth_first, smart
	PriorityKeywords []string    `json:"priority_keywords"` // 优先关键词
}

// PageInfo 页面信息
type PageInfo struct {
	URL            string                 `json:"url"`
	Title          string                 `json:"title"`
	Content        string                 `json:"content"`
	Links          []string               `json:"links"`
	Depth          int                    `json:"depth"`
	ParentURL      string                 `json:"parent_url"`
	QualityScore   float64                `json:"quality_score"`
	ContentType    string                 `json:"content_type"`
	Language       string                 `json:"language"`
	Metadata       map[string]interface{} `json:"metadata"`
	CrawledAt      time.Time              `json:"crawled_at"`
	ProcessingTime time.Duration          `json:"processing_time"`
}

// SmartKnowledgeCrawler 智能知识爬虫
type SmartKnowledgeCrawler struct {
	db                *sql.DB
	config            *SmartCrawlerConfig
	knowledgeLearner  *learning.KnowledgeLearner
	contentExtractor  *nlp.ContentExtractor
	contentVectorizer *ContentVectorizer
	httpClient        *http.Client

	// 爬取状态管理
	visitedURLs  map[string]bool
	urlQueue     chan *PageInfo
	resultQueue  chan *PageInfo
	visitedMutex sync.RWMutex

	// 域名管理
	domainPageCount map[string]int
	domainMutex     sync.RWMutex

	// 反爬虫对抗
	userAgentIndex  int
	proxyIndex      int
	lastRequestTime map[string]time.Time
	requestMutex    sync.RWMutex

	// Chrome实例（用于JS渲染）
	chromeCtx    context.Context
	chromeCancel context.CancelFunc

	// 控制
	stopChan chan struct{}
	wg       sync.WaitGroup
	running  bool

	// 多目标支持
	activeTargets map[int]bool
	targetsMutex  sync.RWMutex

	// 链接数量统计
	totalLinksProcessed int
	depthLinkCount      map[int]int // 每层链接数量统计
	linkCountMutex      sync.RWMutex
}

// NewSmartKnowledgeCrawler 创建智能知识爬虫
func NewSmartKnowledgeCrawler(db *sql.DB, knowledgeLearner *learning.KnowledgeLearner, embedClient interface{}, integratedProcessor *nlp.IntegratedProcessor) *SmartKnowledgeCrawler {
	config := &SmartCrawlerConfig{
		MaxDepth:            5,
		MaxPagesPerDomain:   100,
		ContentMinLength:    200,
		ContentQualityScore: 0.3,
		EnableJSRendering:   true,
		RespectRobotsTxt:    true,
		UserAgentRotation: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		},
		RequestDelay:   time.Second * 3,
		RandomDelay:    true,
		MaxConcurrency: 1,
		Timeout:        time.Second * 60,

		// 链接数量限制配置
		MaxLinksPerPage:  50,        // 每页最多提取50个链接
		MaxLinksPerDepth: 200,       // 每层最多爬取200个链接
		MaxTotalLinks:    1000,      // 总共最多爬取1000个链接
		LinkPriorityMode: "quality", // 优先级模式：quality, keyword, random
	}

	httpClient := &http.Client{
		Timeout: config.Timeout,
		Transport: &http.Transport{
			MaxIdleConns:        100,
			MaxIdleConnsPerHost: 10,
			IdleConnTimeout:     90 * time.Second,
		},
	}

	// 创建内容向量化器
	var contentVectorizer *ContentVectorizer
	if integratedProcessor != nil {
		// 创建简单向量存储
		vectorStore := NewSimpleVectorStore(db)

		// 创建内容向量化器（优先使用NLP，降级到嵌入客户端）
		contentVectorizer = NewContentVectorizer(db, nil, integratedProcessor, vectorStore)
		log.Printf("🧠 内容向量化器已创建，使用NLP生成向量")
	}

	return &SmartKnowledgeCrawler{
		db:                db,
		config:            config,
		knowledgeLearner:  knowledgeLearner,
		contentExtractor:  nlp.NewContentExtractor(),
		contentVectorizer: contentVectorizer,
		httpClient:        httpClient,
		visitedURLs:       make(map[string]bool),
		urlQueue:          make(chan *PageInfo, 1000),
		resultQueue:       make(chan *PageInfo, 1000),
		domainPageCount:   make(map[string]int),
		lastRequestTime:   make(map[string]time.Time),
		stopChan:          make(chan struct{}),
		activeTargets:     make(map[int]bool),
		depthLinkCount:    make(map[int]int),
	}
}

// StartSmartCrawl 启动智能爬取
func (sc *SmartKnowledgeCrawler) StartSmartCrawl(target *SmartCrawlTarget) error {
	// 检查目标是否已在运行
	sc.targetsMutex.Lock()
	if sc.activeTargets[target.ID] {
		sc.targetsMutex.Unlock()
		return fmt.Errorf("目标 %s 已经在智能爬取中", target.Name)
	}
	sc.activeTargets[target.ID] = true
	sc.targetsMutex.Unlock()

	log.Printf("🧠 启动智能爬取: %s (ID: %d)", target.Name, target.ID)

	// 为每个目标启动独立的爬取任务
	go sc.startTargetCrawl(target)

	log.Printf("✅ 智能爬取任务已启动: %s", target.Name)
	return nil
}

// StopSmartCrawl 停止智能爬取
func (sc *SmartKnowledgeCrawler) StopSmartCrawl() {
	if !sc.running {
		return
	}

	log.Println("🛑 停止智能爬虫...")
	sc.running = false
	close(sc.stopChan)

	// 关闭Chrome实例
	if sc.chromeCancel != nil {
		sc.chromeCancel()
	}

	sc.wg.Wait()
	log.Println("✅ 智能爬虫已停止")
}

// initChrome 初始化Chrome实例
func (sc *SmartKnowledgeCrawler) initChrome() error {
	opts := append(chromedp.DefaultExecAllocatorOptions[:],
		chromedp.Flag("headless", true),
		chromedp.Flag("disable-gpu", true),
		chromedp.Flag("disable-dev-shm-usage", true),
		chromedp.Flag("disable-extensions", true),
		chromedp.Flag("no-sandbox", true),
		chromedp.Flag("disable-web-security", true),
		chromedp.Flag("disable-features", "VizDisplayCompositor"),
		chromedp.Flag("disable-background-timer-throttling", true),
		chromedp.Flag("disable-backgrounding-occluded-windows", true),
		chromedp.Flag("disable-renderer-backgrounding", true),
		chromedp.Flag("disable-field-trial-config", true),
		chromedp.Flag("disable-ipc-flooding-protection", true),
		chromedp.Flag("ignore-certificate-errors", true),
		chromedp.Flag("ignore-ssl-errors", true),
		chromedp.Flag("ignore-certificate-errors-spki-list", true),
		chromedp.Flag("ignore-certificate-errors-ssl-errors", true),
		chromedp.UserAgent(sc.config.UserAgentRotation[0]),
	)

	allocCtx, cancel := chromedp.NewExecAllocator(context.Background(), opts...)

	// 创建带有错误处理的上下文
	sc.chromeCtx, sc.chromeCancel = chromedp.NewContext(allocCtx, chromedp.WithErrorf(func(s string, i ...interface{}) {
		// 忽略 Cookie 相关的错误
		if strings.Contains(s, "cookiePartitionKey") || strings.Contains(s, "CookiePartitionKey") {
			return
		}
		log.Printf("Chrome调试信息: "+s, i...)
	}))

	// 测试Chrome是否可用
	err := chromedp.Run(sc.chromeCtx, chromedp.Navigate("about:blank"))
	if err != nil {
		cancel()
		return fmt.Errorf("Chrome测试失败: %v", err)
	}

	log.Println("✅ Chrome实例初始化成功")
	return nil
}

// initChromeWithRetry 带重试的Chrome初始化
func (sc *SmartKnowledgeCrawler) initChromeWithRetry() error {
	maxRetries := 3
	for attempt := 1; attempt <= maxRetries; attempt++ {
		log.Printf("🔄 Chrome初始化尝试 %d/%d", attempt, maxRetries)

		err := sc.initChrome()
		if err == nil {
			return nil
		}

		log.Printf("   ❌ 尝试 %d 失败: %v", attempt, err)

		// 清理失败的上下文
		if sc.chromeCancel != nil {
			sc.chromeCancel()
			sc.chromeCancel = nil
		}
		sc.chromeCtx = nil

		// 等待后重试
		if attempt < maxRetries {
			time.Sleep(time.Duration(attempt) * time.Second)
		}
	}

	return fmt.Errorf("Chrome初始化失败，已重试%d次", maxRetries)
}

// crawlWorker 爬取工作协程
func (sc *SmartKnowledgeCrawler) crawlWorker(target *SmartCrawlTarget) {
	defer sc.wg.Done()

	for {
		select {
		case <-sc.stopChan:
			return
		case pageInfo := <-sc.urlQueue:
			if pageInfo == nil {
				continue
			}

			// 检查是否已访问
			if sc.isVisited(pageInfo.URL) {
				continue
			}

			// 检查域名限制
			if !sc.isDomainAllowed(pageInfo.URL, target) {
				continue
			}

			// 检查深度限制
			if pageInfo.Depth > sc.config.MaxDepth {
				continue
			}

			// 检查域名页面数限制
			domain := sc.extractDomain(pageInfo.URL)
			if sc.getDomainPageCount(domain) >= sc.config.MaxPagesPerDomain {
				continue
			}

			// 执行爬取
			result, err := sc.crawlPage(pageInfo, target)
			if err != nil {
				log.Printf("❌ 爬取失败 %s: %v", pageInfo.URL, err)
				continue
			}

			// 标记为已访问
			sc.markVisited(pageInfo.URL)
			sc.incrementDomainPageCount(domain)

			// 发送结果到处理队列
			select {
			case sc.resultQueue <- result:
			default:
				log.Printf("⚠️ 结果队列已满，丢弃结果: %s", result.URL)
			}

			// 请求延迟
			sc.applyRequestDelay(domain)
		}
	}
}

// crawlPage 爬取单个页面
func (sc *SmartKnowledgeCrawler) crawlPage(pageInfo *PageInfo, target *SmartCrawlTarget) (*PageInfo, error) {
	startTime := time.Now()

	// 详细日志：开始爬取
	log.Printf("🕷️ [第%d层] 开始爬取: %s", pageInfo.Depth, pageInfo.URL)
	logger.CrawlerLogInfo(fmt.Sprintf("[第%d层] 开始爬取: %s", pageInfo.Depth, pageInfo.URL), "🕷️")

	if pageInfo.ParentURL != "" {
		log.Printf("   └─ 来源页面: %s", pageInfo.ParentURL)
		logger.CrawlerLogDebug(fmt.Sprintf("来源页面: %s", pageInfo.ParentURL), "└─")
	}

	log.Printf("🕷️ 爬取页面: %s (深度: %d)", pageInfo.URL, pageInfo.Depth)
	logger.CrawlerLogInfo(fmt.Sprintf("爬取页面: %s (深度: %d)", pageInfo.URL, pageInfo.Depth), "🕷️")

	var content, title string
	var links []string
	var err error

	// 根据配置选择爬取方式
	if sc.config.EnableJSRendering && sc.needsJSRendering(pageInfo.URL) {
		log.Printf("   🌐 使用Chrome渲染爬取")
		logger.CrawlerLogInfo("使用Chrome渲染爬取", "🌐")
		content, title, links, err = sc.crawlWithChrome(pageInfo.URL, target)
	} else {
		log.Printf("   📄 使用HTTP客户端爬取")
		logger.CrawlerLogInfo("使用HTTP客户端爬取", "📄")
		content, title, links, err = sc.crawlWithHTTP(pageInfo.URL, target)
	}

	if err != nil {
		log.Printf("   ❌ 爬取失败: %v", err)
		return nil, err
	}

	log.Printf("   ✅ 爬取成功: 标题长度=%d, 内容长度=%d, 链接数=%d",
		len(title), len(content), len(links))

	// 应用爬取规则（保护内容和链接）
	originalContent := content
	originalTitle := title
	originalLinks := links

	rule := sc.findMatchingRule(pageInfo.URL, target.Rules)
	if rule != nil {
		ruleContent, ruleTitle, _ := sc.applyRule(content, title, links, rule)
		// 如果规则导致内容被清空，保留原始内容和链接以便继续爬取
		if ruleContent == "" && ruleTitle == "" {
			content = originalContent
			title = originalTitle
			links = originalLinks
			log.Printf("   ⚠️ 内容不符合规则要求，但保留原始内容和链接继续爬取")
		} else {
			content = ruleContent
			title = ruleTitle
		}
	}

	// 计算内容质量分数
	qualityScore := sc.calculateQualityScore(content, title, target.PriorityKeywords)
	log.Printf("   📊 内容质量评分: %.2f (阈值: %.2f)", qualityScore, sc.config.ContentQualityScore)

	// 内容质量检查：只有在最大深度时才严格过滤，否则继续深入爬取
	shouldSaveContent := qualityScore >= sc.config.ContentQualityScore
	if !shouldSaveContent && pageInfo.Depth >= sc.config.MaxDepth {
		// 已达到最大深度且质量不达标，放弃
		log.Printf("   ❌ 最大深度内容质量不达标，放弃: %.2f < %.2f", qualityScore, sc.config.ContentQualityScore)
		return nil, fmt.Errorf("最大深度内容质量不达标: %.2f < %.2f", qualityScore, sc.config.ContentQualityScore)
	}

	// 如果质量不达标但未达到最大深度，仍然提取链接继续爬取
	if !shouldSaveContent {
		log.Printf("   ⚠️ 第%d层内容质量不达标(%.2f < %.2f)，继续深入爬取",
			pageInfo.Depth, qualityScore, sc.config.ContentQualityScore)
	} else {
		log.Printf("   ✅ 内容质量达标，将保存内容")
	}

	// 构建结果
	result := &PageInfo{
		URL:          pageInfo.URL,
		Title:        title,
		Content:      content,
		Links:        links,
		Depth:        pageInfo.Depth,
		ParentURL:    pageInfo.ParentURL,
		QualityScore: qualityScore,
		ContentType:  "text/html",
		Language:     sc.detectLanguage(content),
		Metadata: map[string]interface{}{
			"should_save": shouldSaveContent, // 标记是否应该保存内容
		},
		CrawledAt:      time.Now(),
		ProcessingTime: time.Since(startTime),
	}

	// 使用新的链接过滤和限制逻辑
	log.Printf("   🔗 发现 %d 个原始链接", len(links))
	if len(links) > 0 {
		log.Printf("   📋 原始链接列表:")
		for i, link := range links[:min(10, len(links))] { // 只显示前10个
			log.Printf("     %d. %s", i+1, link)
		}
		if len(links) > 10 {
			log.Printf("     ... 还有 %d 个链接", len(links)-10)
		}
	}

	filteredLinks := sc.filterAndLimitLinks(links, pageInfo.Depth+1, target)

	// 添加过滤后的链接到队列
	addedCount := 0
	for _, link := range filteredLinks {
		newPageInfo := &PageInfo{
			URL:       link,
			Depth:     pageInfo.Depth + 1,
			ParentURL: pageInfo.URL,
			CrawledAt: time.Now(),
		}

		// 检查是否已访问
		sc.visitedMutex.RLock()
		visited := sc.visitedURLs[link]
		sc.visitedMutex.RUnlock()

		if !visited {
			select {
			case sc.urlQueue <- newPageInfo:
				addedCount++
				log.Printf("   ➕ 添加到队列: %s", link)
			default:
				log.Printf("   ⚠️ URL队列已满，跳过链接: %s", link)
			}
		} else {
			log.Printf("   ⏭️ 已访问，跳过: %s", link)
		}
	}

	log.Printf("   📊 链接处理结果: 发现%d个 → 过滤%d个 → 添加%d个到队列",
		len(links), len(filteredLinks), addedCount)
	logger.CrawlerLogInfo(fmt.Sprintf("链接处理结果: 发现%d个 → 过滤%d个 → 添加%d个到队列",
		len(links), len(filteredLinks), addedCount), "📊")

	return result, nil
}

// crawlWithHTTP 使用HTTP客户端爬取
func (sc *SmartKnowledgeCrawler) crawlWithHTTP(url string, target *SmartCrawlTarget) (string, string, []string, error) {
	req, err := http.NewRequest("GET", url, nil)
	if err != nil {
		return "", "", nil, err
	}

	// 设置随机User-Agent
	userAgent := sc.getRandomUserAgent()
	req.Header.Set("User-Agent", userAgent)
	req.Header.Set("Accept", "text/html,application/xhtml+xml,application/xml;q=0.9,*/*;q=0.8")
	req.Header.Set("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8")
	req.Header.Set("Accept-Encoding", "gzip, deflate")
	req.Header.Set("Connection", "keep-alive")

	resp, err := sc.httpClient.Do(req)
	if err != nil {
		return "", "", nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return "", "", nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return "", "", nil, err
	}

	// 提取标题
	title := strings.TrimSpace(doc.Find("title").First().Text())

	// 提取内容
	content := sc.extractContent(doc)

	// 提取链接
	links := sc.extractLinks(doc, url)

	return content, title, links, nil
}

// crawlWithChrome 使用Chrome爬取（支持JavaScript）
func (sc *SmartKnowledgeCrawler) crawlWithChrome(url string, target *SmartCrawlTarget) (string, string, []string, error) {
	// 如果Chrome上下文不可用，直接降级到HTTP爬取
	if sc.chromeCtx == nil {
		log.Printf("   ⚠️ Chrome上下文不可用，降级到HTTP爬取")
		return sc.crawlWithHTTP(url, target)
	}

	ctx, cancel := context.WithTimeout(sc.chromeCtx, sc.config.Timeout)
	defer cancel()

	var title, content string
	var links []string

	// 尝试导航到页面
	err := chromedp.Run(ctx, chromedp.Navigate(url))
	if err != nil {
		log.Printf("   ⚠️ Chrome导航失败，降级到HTTP爬取: %v", err)
		return sc.crawlWithHTTP(url, target)
	}

	// 等待页面加载，如果失败则继续
	chromedp.Run(ctx, chromedp.WaitVisible("body", chromedp.ByQuery))

	// 等待JavaScript加载完成
	time.Sleep(time.Second * 3)

	// 获取页面内容，分步进行以提高容错性
	err = chromedp.Run(ctx, chromedp.Title(&title))
	if err != nil {
		log.Printf("   ⚠️ 获取标题失败: %v", err)
		title = ""
	}

	err = chromedp.Run(ctx, chromedp.Text("body", &content, chromedp.ByQuery))
	if err != nil {
		log.Printf("   ⚠️ 获取内容失败: %v", err)
		// 如果获取内容失败，降级到HTTP爬取
		return sc.crawlWithHTTP(url, target)
	}

	// 尝试获取链接，失败也不影响主要功能
	err = chromedp.Run(ctx, chromedp.Evaluate(`
		(() => {
			try {
				return Array.from(document.querySelectorAll('a[href]'))
					.map(a => {
						const href = a.href;
						if (href && (href.startsWith('http://') || href.startsWith('https://'))) {
							return href;
						}
						return null;
					})
					.filter(href => href !== null);
			} catch(e) {
				console.log('Link extraction error:', e);
				return [];
			}
		})()
	`, &links))
	if err != nil {
		log.Printf("   ⚠️ 获取链接失败: %v", err)
		links = []string{} // 设置为空数组
	}

	// 内容清理和验证
	content = sc.sanitizeContent(content)
	title = sc.sanitizeContent(title)

	log.Printf("   🔗 Chrome提取到 %d 个链接", len(links))
	if len(links) > 0 {
		log.Printf("   📋 前5个链接示例:")
		for i, link := range links[:min(5, len(links))] {
			log.Printf("     %d. %s", i+1, link)
		}
	}

	return content, title, links, nil
}

// resultProcessor 结果处理协程
func (sc *SmartKnowledgeCrawler) resultProcessor(target *SmartCrawlTarget) {
	defer sc.wg.Done()

	for {
		select {
		case <-sc.stopChan:
			return
		case result := <-sc.resultQueue:
			if result == nil {
				continue
			}

			// 处理爬取结果
			if err := sc.processResult(result, target); err != nil {
				log.Printf("❌ 处理结果失败 %s: %v", result.URL, err)
			}
		}
	}
}

// processResult 处理单个结果
func (sc *SmartKnowledgeCrawler) processResult(result *PageInfo, target *SmartCrawlTarget) error {
	// 检查是否应该保存内容
	shouldSave, ok := result.Metadata["should_save"].(bool)
	if !ok || !shouldSave {
		log.Printf("⏭️ 跳过低质量内容: %s (质量分数: %.2f)", result.URL, result.QualityScore)
		return nil // 不保存，但不报错
	}

	log.Printf("📝 处理高质量结果: %s (质量分数: %.2f)", result.URL, result.QualityScore)
	logger.CrawlerLogInfo(fmt.Sprintf("处理高质量结果: %s (质量分数: %.2f)", result.URL, result.QualityScore), "📝")

	// 清理所有文本字段，确保数据库兼容性
	cleanTitle := sc.sanitizeContent(result.Title)
	cleanContent := sc.sanitizeContent(result.Content)
	cleanURL := sc.sanitizeContent(result.URL)

	log.Printf("   🧹 内容清理: 标题 %d→%d 字符, 内容 %d→%d 字符",
		len(result.Title), len(cleanTitle), len(result.Content), len(cleanContent))
	logger.CrawlerLogDebug(fmt.Sprintf("内容清理: 标题 %d→%d 字符, 内容 %d→%d 字符",
		len(result.Title), len(cleanTitle), len(result.Content), len(cleanContent)), "🧹")

	// 转换为CrawlResult格式
	crawlResult := &CrawlResult{
		TargetID:  target.ID,
		URL:       cleanURL,
		Title:     cleanTitle,
		Content:   cleanContent,
		Summary:   sc.generateSummary(cleanContent),
		Keywords:  sc.extractKeywords(cleanTitle + " " + cleanContent),
		Category:  target.Category,
		CrawledAt: result.CrawledAt,
		Status:    "processed",
		Metadata: map[string]interface{}{
			"depth":           result.Depth,
			"parent_url":      result.ParentURL,
			"quality_score":   result.QualityScore,
			"content_type":    result.ContentType,
			"language":        result.Language,
			"processing_time": result.ProcessingTime.Milliseconds(),
			"links_found":     len(result.Links),
		},
	}

	// 保存爬取结果
	if err := sc.saveCrawlResult(crawlResult); err != nil {
		return fmt.Errorf("保存爬取结果失败: %v", err)
	}

	// 异步提取并保存知识（避免阻塞爬取）
	if len(result.Content) > sc.config.ContentMinLength {
		go func() {
			// 优先使用内容向量化器（包含NLP分析和向量生成）
			if sc.contentVectorizer != nil {
				// 使用NLP处理器提取摘要和关键词
				summary, keywords := sc.extractContentWithNLP(result.Content, result.Title)

				// 转换PageInfo为CrawlResult格式
				crawlResult := &CrawlResult{
					URL:      result.URL,
					Title:    result.Title,
					Content:  result.Content,
					Summary:  summary,
					Keywords: keywords,
				}

				vectorizedContent, err := sc.contentVectorizer.ProcessCrawledContent(crawlResult)
				if err != nil {
					log.Printf("❌ 内容向量化失败: %v", err)
					logger.CrawlerLogError(fmt.Sprintf("内容向量化失败: %v", err), "❌")
				} else {
					log.Printf("🧠 内容向量化成功: ID=%d, Q=%s", vectorizedContent.ID, vectorizedContent.Question)
					logger.CrawlerLogSuccess(fmt.Sprintf("内容向量化成功: %s", vectorizedContent.Question), "🧠")
				}
			} else if sc.knowledgeLearner != nil {
				// 降级到传统知识提取
				knowledge := sc.extractKnowledgeFromContent(result, target)
				if knowledge != nil {
					if err := sc.knowledgeLearner.SaveKnowledge(knowledge); err != nil {
						log.Printf("❌ 保存知识失败: %v", err)
						logger.CrawlerLogError(fmt.Sprintf("保存知识失败: %v", err), "❌")
					} else {
						log.Printf("✅ 知识保存成功: %s", knowledge.Question)
						logger.CrawlerLogSuccess(fmt.Sprintf("知识保存成功: %s", knowledge.Question), "✅")
					}
				}
			}
		}()
	}

	return nil
}

// extractContentWithNLP 使用NLP提取内容摘要和关键词
func (sc *SmartKnowledgeCrawler) extractContentWithNLP(content, title string) (string, []string) {
	// 优先使用集成NLP处理器
	if sc.contentExtractor != nil {
		log.Printf("🧠 使用高级NLP内容提取器处理内容")

		// 使用NLP内容提取器
		extracted := sc.contentExtractor.ExtractContent(content, "", title)
		if extracted != nil {
			// 生成智能摘要
			summary := extracted.Summary
			if summary == "" && len(extracted.Topics) > 0 {
				// 基于话题生成摘要
				topicTitles := make([]string, 0, len(extracted.Topics))
				for i, topic := range extracted.Topics {
					if i >= 3 { // 最多3个话题
						break
					}
					topicTitles = append(topicTitles, topic.Title)
				}
				summary = "主要内容包括：" + strings.Join(topicTitles, "、")
			}

			// 如果还是没有摘要，使用智能截取
			if summary == "" {
				summary = sc.generateIntelligentSummary(content)
			}

			log.Printf("✅ NLP提取完成: 摘要长度=%d, 关键词数量=%d", len(summary), len(extracted.Keywords))
			return summary, extracted.Keywords
		}
	}

	// 降级到基础处理
	log.Printf("⚠️ NLP处理器不可用，使用基础提取")
	return sc.generateIntelligentSummary(content), sc.extractBasicKeywords(content)
}

// generateIntelligentSummary 生成智能摘要
func (sc *SmartKnowledgeCrawler) generateIntelligentSummary(content string) string {
	// 清理内容
	content = strings.TrimSpace(content)
	if len(content) <= 200 {
		return content
	}

	// 按句子分割
	sentences := sc.splitIntoSentences(content)
	if len(sentences) == 0 {
		return sc.generateSummary(content) // 降级到原有方法
	}

	// 选择前2-3个最有信息量的句子
	var summary strings.Builder
	sentenceCount := 0
	totalLength := 0

	for _, sentence := range sentences {
		sentence = strings.TrimSpace(sentence)
		if len(sentence) < 10 { // 过滤太短的句子
			continue
		}

		if totalLength+len(sentence) > 300 { // 控制摘要长度
			break
		}

		if sentenceCount > 0 {
			summary.WriteString(" ")
		}
		summary.WriteString(sentence)
		totalLength += len(sentence)
		sentenceCount++

		if sentenceCount >= 3 { // 最多3个句子
			break
		}
	}

	result := summary.String()
	if result == "" {
		return sc.generateSummary(content) // 降级到原有方法
	}

	return result
}

// splitIntoSentences 将文本分割为句子
func (sc *SmartKnowledgeCrawler) splitIntoSentences(text string) []string {
	// 中英文句子分割
	sentenceEnders := []string{"。", "！", "？", ".", "!", "?"}

	sentences := []string{text}
	for _, ender := range sentenceEnders {
		var newSentences []string
		for _, sentence := range sentences {
			parts := strings.Split(sentence, ender)
			for i, part := range parts {
				if i < len(parts)-1 {
					part += ender
				}
				if strings.TrimSpace(part) != "" {
					newSentences = append(newSentences, part)
				}
			}
		}
		sentences = newSentences
	}

	return sentences
}

// extractBasicKeywords 提取基础关键词（降级方案）
func (sc *SmartKnowledgeCrawler) extractBasicKeywords(content string) []string {
	// 简单的关键词提取：基于词频
	words := strings.Fields(content)
	wordCount := make(map[string]int)

	for _, word := range words {
		// 过滤短词和常见词
		if len(word) >= 2 && !sc.isStopWord(word) {
			wordCount[word]++
		}
	}

	// 按频率排序
	type wordFreq struct {
		word  string
		count int
	}

	var wordFreqs []wordFreq
	for word, count := range wordCount {
		if count >= 2 { // 至少出现2次
			wordFreqs = append(wordFreqs, wordFreq{word, count})
		}
	}

	sort.Slice(wordFreqs, func(i, j int) bool {
		return wordFreqs[i].count > wordFreqs[j].count
	})

	// 返回前10个关键词
	var keywords []string
	for i, wf := range wordFreqs {
		if i >= 10 {
			break
		}
		keywords = append(keywords, wf.word)
	}

	return keywords
}

// extractKnowledgeFromContent 从内容中提取知识
func (sc *SmartKnowledgeCrawler) extractKnowledgeFromContent(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	content := result.Content
	title := result.Title

	// 1. 如果标题是问句形式，直接使用
	if strings.Contains(title, "什么是") || strings.Contains(title, "如何") ||
		strings.Contains(title, "怎么") || strings.Contains(title, "为什么") ||
		strings.HasSuffix(title, "?") || strings.HasSuffix(title, "？") {

		return sc.createKnowledge(title, sc.generateSummary(content), result, target, "question_title")
	}

	// 2. 尝试从内容中提取问答对
	if qa := sc.extractQAFromContent(content, title); qa != nil {
		qa.Source = "crawler"
		qa.LearnedFrom = "smart_crawler"
		qa.Status = "approved"
		qa.Category = target.Category
		qa.Context = fmt.Sprintf("从网站爬取: %s", result.URL)
		qa.Confidence = float32(result.QualityScore)
		qa.CreatedAt = time.Now()
		qa.Metadata = map[string]interface{}{
			"source_url":      result.URL,
			"crawled_at":      result.CrawledAt,
			"depth":           result.Depth,
			"quality_score":   result.QualityScore,
			"content_length":  len(content),
			"language":        result.Language,
			"extraction_type": "qa_content",
		}
		return qa
	}

	// 3. 基于标题和内容生成通用知识
	if len(title) > 0 && len(content) > 100 {
		question := sc.generateQuestionFromTitle(title, target)
		if question != "" {
			answer := sc.generateSummary(content)
			if len(answer) > 20 {
				return sc.createKnowledge(question, answer, result, target, "generated_content")
			}
		}
	}

	// 4. 特殊网站处理
	if specialKnowledge := sc.extractSpecialSiteKnowledge(result, target); specialKnowledge != nil {
		return specialKnowledge
	}

	return nil
}

// extractQAFromContent 从内容中提取问答对
func (sc *SmartKnowledgeCrawler) extractQAFromContent(content, title string) *learning.LearnedKnowledge {
	// 查找问答模式
	qaPatterns := []struct {
		questionPattern string
		answerPattern   string
	}{
		{`问[:：]\s*(.+?)[\n\r]`, `答[:：]\s*(.+?)(?:\n\n|\r\r|$)`},
		{`Q[:：]\s*(.+?)[\n\r]`, `A[:：]\s*(.+?)(?:\n\n|\r\r|$)`},
		{`\d+[\.、]\s*(.+?\?)`, `(.+?)(?:\n\d+|\n[A-Z]|$)`},
	}

	for _, pattern := range qaPatterns {
		questionRegex := regexp.MustCompile(pattern.questionPattern)
		answerRegex := regexp.MustCompile(pattern.answerPattern)

		questionMatches := questionRegex.FindAllStringSubmatch(content, -1)
		answerMatches := answerRegex.FindAllStringSubmatch(content, -1)

		if len(questionMatches) > 0 && len(answerMatches) > 0 {
			question := strings.TrimSpace(questionMatches[0][1])
			answer := strings.TrimSpace(answerMatches[0][1])

			if len(question) > 5 && len(answer) > 10 {
				return &learning.LearnedKnowledge{
					Question: question,
					Answer:   answer,
				}
			}
		}
	}

	return nil
}

// startTargetCrawl 为单个目标启动独立的爬取任务
func (sc *SmartKnowledgeCrawler) startTargetCrawl(target *SmartCrawlTarget) {
	defer func() {
		// 清理目标状态
		sc.targetsMutex.Lock()
		delete(sc.activeTargets, target.ID)
		sc.targetsMutex.Unlock()
		log.Printf("🏁 智能爬取完成: %s", target.Name)
	}()

	// 初始化Chrome实例（如果启用JS渲染且尚未初始化）
	if sc.config.EnableJSRendering && sc.chromeCtx == nil {
		if err := sc.initChromeWithRetry(); err != nil {
			log.Printf("⚠️ Chrome初始化失败，将使用静态爬取: %v", err)
			sc.config.EnableJSRendering = false
		}
	}

	// 创建目标专用的URL队列和结果队列
	urlQueue := make(chan *PageInfo, 100)
	resultQueue := make(chan *PageInfo, 100)
	visitedURLs := make(map[string]bool)
	var visitedMutex sync.RWMutex

	// 添加种子URL到队列
	for _, seedURL := range target.SeedURLs {
		pageInfo := &PageInfo{
			URL:       seedURL,
			Depth:     0,
			ParentURL: "",
			CrawledAt: time.Now(),
		}
		select {
		case urlQueue <- pageInfo:
		default:
			log.Printf("⚠️ URL队列已满，跳过: %s", seedURL)
		}
	}

	// 启动工作协程
	var wg sync.WaitGroup
	stopChan := make(chan struct{})

	// 启动爬取工作协程
	for i := 0; i < sc.config.MaxConcurrency; i++ {
		wg.Add(1)
		go func() {
			defer wg.Done()

			for {
				select {
				case <-stopChan:
					return
				case pageInfo := <-urlQueue:
					if pageInfo == nil {
						continue
					}

					// 检查是否已访问
					visitedMutex.RLock()
					visited := visitedURLs[pageInfo.URL]
					visitedMutex.RUnlock()
					if visited {
						continue
					}

					// 检查域名限制
					if !sc.isDomainAllowed(pageInfo.URL, target) {
						continue
					}

					// 检查深度限制
					if pageInfo.Depth > sc.config.MaxDepth {
						continue
					}

					// 执行爬取
					result, err := sc.crawlPage(pageInfo, target)
					if err != nil {
						log.Printf("❌ 爬取失败 %s: %v", pageInfo.URL, err)
						continue
					}

					// 标记为已访问
					visitedMutex.Lock()
					visitedURLs[pageInfo.URL] = true
					visitedMutex.Unlock()

					// 发送结果到处理队列
					select {
					case resultQueue <- result:
					default:
						log.Printf("⚠️ 结果队列已满，丢弃结果: %s", result.URL)
					}

					// 使用新的链接过滤和限制逻辑
					if pageInfo.Depth+1 <= sc.config.MaxDepth {
						filteredLinks := sc.filterAndLimitLinks(result.Links, pageInfo.Depth+1, target)

						for _, link := range filteredLinks {
							// 检查是否已访问
							visitedMutex.RLock()
							visited := visitedURLs[link]
							visitedMutex.RUnlock()

							if !visited {
								newPageInfo := &PageInfo{
									URL:       link,
									Depth:     pageInfo.Depth + 1,
									ParentURL: pageInfo.URL,
									CrawledAt: time.Now(),
								}
								select {
								case urlQueue <- newPageInfo:
								default:
									// 队列已满，跳过
								}
							}
						}
					}
				}
			}
		}()
	}

	// 启动结果处理协程
	wg.Add(1)
	go func() {
		defer wg.Done()

		for {
			select {
			case <-stopChan:
				return
			case result := <-resultQueue:
				if result == nil {
					continue
				}

				// 处理爬取结果
				if err := sc.processResult(result, target); err != nil {
					log.Printf("❌ 处理结果失败 %s: %v", result.URL, err)
				}
			}
		}
	}()

	// 等待一段时间或者队列为空
	timeout := time.After(time.Minute * 5)    // 5分钟超时
	ticker := time.NewTicker(time.Second * 2) // 更频繁的检查
	defer ticker.Stop()

	emptyCount := 0 // 连续空队列计数

	for {
		select {
		case <-timeout:
			log.Printf("⏰ 智能爬取超时: %s", target.Name)
			close(stopChan)
			wg.Wait()
			return
		case <-ticker.C:
			// 检查队列是否为空
			if len(urlQueue) == 0 && len(resultQueue) == 0 {
				emptyCount++
				log.Printf("🔍 队列检查 %d/3: URL队列=%d, 结果队列=%d", emptyCount, len(urlQueue), len(resultQueue))

				// 连续3次检查都为空才认为真正完成
				if emptyCount >= 3 {
					log.Printf("✅ 智能爬取队列已空: %s", target.Name)
					close(stopChan)
					wg.Wait()
					return
				}
			} else {
				emptyCount = 0 // 重置计数
				log.Printf("🔄 爬取进行中: URL队列=%d, 结果队列=%d", len(urlQueue), len(resultQueue))
			}
		}
	}
}

// filterAndLimitLinks 过滤和限制链接数量
func (sc *SmartKnowledgeCrawler) filterAndLimitLinks(links []string, depth int, target *SmartCrawlTarget) []string {
	if len(links) == 0 {
		return links
	}

	// 1. 基础过滤：移除无效链接
	validLinks := make([]string, 0, len(links))
	for _, link := range links {
		if sc.isValidLink(link) && sc.isDomainAllowed(link, target) {
			validLinks = append(validLinks, link)
		}
	}

	// 2. 检查总链接数限制
	sc.linkCountMutex.Lock()
	if sc.totalLinksProcessed >= sc.config.MaxTotalLinks {
		sc.linkCountMutex.Unlock()
		log.Printf("⚠️ 已达到总链接数限制 (%d)，停止添加新链接", sc.config.MaxTotalLinks)
		return []string{}
	}

	// 3. 检查每层链接数限制
	currentDepthCount := sc.depthLinkCount[depth]
	if currentDepthCount >= sc.config.MaxLinksPerDepth {
		sc.linkCountMutex.Unlock()
		log.Printf("⚠️ 第%d层已达到链接数限制 (%d)，停止添加新链接", depth, sc.config.MaxLinksPerDepth)
		return []string{}
	}
	sc.linkCountMutex.Unlock()

	// 4. 限制每页链接数
	if len(validLinks) > sc.config.MaxLinksPerPage {
		log.Printf("📊 发现 %d 个链接，限制为 %d 个", len(validLinks), sc.config.MaxLinksPerPage)
		validLinks = sc.prioritizeLinks(validLinks, target)[:sc.config.MaxLinksPerPage]
	}

	// 5. 检查剩余可用链接数
	sc.linkCountMutex.Lock()
	remainingTotal := sc.config.MaxTotalLinks - sc.totalLinksProcessed
	remainingDepth := sc.config.MaxLinksPerDepth - sc.depthLinkCount[depth]
	maxAllowed := min(remainingTotal, remainingDepth)

	if len(validLinks) > maxAllowed {
		validLinks = validLinks[:maxAllowed]
		log.Printf("📊 根据限制调整为 %d 个链接", maxAllowed)
	}

	// 6. 更新统计
	sc.totalLinksProcessed += len(validLinks)
	sc.depthLinkCount[depth] += len(validLinks)
	sc.linkCountMutex.Unlock()

	log.Printf("🔗 第%d层添加 %d 个链接 (总计: %d/%d, 本层: %d/%d)",
		depth, len(validLinks), sc.totalLinksProcessed, sc.config.MaxTotalLinks,
		sc.depthLinkCount[depth], sc.config.MaxLinksPerDepth)

	return validLinks
}

// prioritizeLinks 根据优先级模式排序链接
func (sc *SmartKnowledgeCrawler) prioritizeLinks(links []string, target *SmartCrawlTarget) []string {
	switch sc.config.LinkPriorityMode {
	case "keyword":
		return sc.prioritizeByKeywords(links, target.PriorityKeywords)
	case "quality":
		return sc.prioritizeByQuality(links)
	case "random":
		return sc.shuffleLinks(links)
	default:
		return sc.prioritizeByKeywords(links, target.PriorityKeywords)
	}
}

// prioritizeByKeywords 根据关键词优先级排序
func (sc *SmartKnowledgeCrawler) prioritizeByKeywords(links []string, keywords []string) []string {
	if len(keywords) == 0 {
		return links
	}

	type linkScore struct {
		url   string
		score int
	}

	scored := make([]linkScore, 0, len(links))
	for _, link := range links {
		score := 0
		linkLower := strings.ToLower(link)
		for _, keyword := range keywords {
			if strings.Contains(linkLower, strings.ToLower(keyword)) {
				score++
			}
		}
		scored = append(scored, linkScore{url: link, score: score})
	}

	// 按分数降序排序
	sort.Slice(scored, func(i, j int) bool {
		return scored[i].score > scored[j].score
	})

	result := make([]string, len(scored))
	for i, item := range scored {
		result[i] = item.url
	}
	return result
}

// prioritizeByQuality 根据URL质量排序（简单启发式）
func (sc *SmartKnowledgeCrawler) prioritizeByQuality(links []string) []string {
	type linkScore struct {
		url   string
		score int
	}

	scored := make([]linkScore, 0, len(links))
	for _, link := range links {
		score := sc.calculateURLQuality(link)
		scored = append(scored, linkScore{url: link, score: score})
	}

	// 按分数降序排序
	sort.Slice(scored, func(i, j int) bool {
		return scored[i].score > scored[j].score
	})

	result := make([]string, len(scored))
	for i, item := range scored {
		result[i] = item.url
	}
	return result
}

// calculateURLQuality 计算URL质量分数
func (sc *SmartKnowledgeCrawler) calculateURLQuality(url string) int {
	score := 0
	urlLower := strings.ToLower(url)

	// 正面指标
	positiveKeywords := []string{"article", "post", "blog", "news", "content", "detail", "tutorial", "guide"}
	for _, keyword := range positiveKeywords {
		if strings.Contains(urlLower, keyword) {
			score += 2
		}
	}

	// 负面指标
	negativeKeywords := []string{"ad", "advertisement", "popup", "banner", "promo", "download", "pdf", "zip"}
	for _, keyword := range negativeKeywords {
		if strings.Contains(urlLower, keyword) {
			score -= 3
		}
	}

	// URL结构评分
	if strings.Count(url, "/") >= 3 && strings.Count(url, "/") <= 6 {
		score += 1 // 适中的URL深度
	}

	return score
}

// shuffleLinks 随机打乱链接顺序
func (sc *SmartKnowledgeCrawler) shuffleLinks(links []string) []string {
	result := make([]string, len(links))
	copy(result, links)

	for i := len(result) - 1; i > 0; i-- {
		j := rand.Intn(i + 1)
		result[i], result[j] = result[j], result[i]
	}

	return result
}

// min 返回两个整数中的较小值
func min(a, b int) int {
	if a < b {
		return a
	}
	return b
}

// sanitizeContent 清理和验证内容，解决字符编码问题
func (sc *SmartKnowledgeCrawler) sanitizeContent(content string) string {
	if content == "" {
		return content
	}

	// 1. 移除NULL字符和其他控制字符
	content = strings.ReplaceAll(content, "\x00", "")
	content = strings.ReplaceAll(content, "\x01", "")
	content = strings.ReplaceAll(content, "\x02", "")
	content = strings.ReplaceAll(content, "\x03", "")
	content = strings.ReplaceAll(content, "\x04", "")
	content = strings.ReplaceAll(content, "\x05", "")
	content = strings.ReplaceAll(content, "\x06", "")
	content = strings.ReplaceAll(content, "\x07", "")
	content = strings.ReplaceAll(content, "\x08", "")
	// 保留\x09 (tab), \x0A (LF), \x0D (CR)
	content = strings.ReplaceAll(content, "\x0B", "")
	content = strings.ReplaceAll(content, "\x0C", "")
	content = strings.ReplaceAll(content, "\x0E", "")
	content = strings.ReplaceAll(content, "\x0F", "")

	// 2. 确保UTF-8编码有效性
	if !utf8.ValidString(content) {
		// 将无效的UTF-8字符替换为问号
		content = strings.ToValidUTF8(content, "?")
		log.Printf("   ⚠️ 发现无效UTF-8字符，已替换")
	}

	// 3. 移除MySQL不支持的4字节UTF-8字符（emoji等）
	// 如果数据库使用utf8而不是utf8mb4
	// 注意：只移除真正的4字节字符，不影响中文
	if strings.Contains(content, "😀") || strings.Contains(content, "🎉") || strings.Contains(content, "👍") {
		content = regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]`).ReplaceAllString(content, "")
	}

	// 4. 限制长度避免数据库字段溢出
	maxLength := 65535 // TEXT字段的最大长度
	if len(content) > maxLength {
		content = content[:maxLength]
		log.Printf("   ⚠️ 内容过长，已截断到 %d 字符", maxLength)
	}

	// 5. 清理多余的空白字符
	content = strings.TrimSpace(content)

	return content
}

// PageAnalysis 页面分析结果
type PageAnalysis struct {
	PageType    string            // 页面类型：homepage, realtime, novel, movie, teleplay等
	Category    string            // 内容分类
	MainContent []string          // 主要内容列表
	Keywords    []string          // 提取的关键词
	Topics      []TopicInfo       // 话题信息
	ContentType string            // 内容类型：list, article, search等
	Quality     float64           // 内容质量评分
	Metadata    map[string]string // 额外元数据
}

// TopicInfo 话题信息
type TopicInfo struct {
	Title       string // 话题标题
	Description string // 话题描述
	Rank        int    // 排名
	Category    string // 分类
	HotValue    string // 热度值
}

// createKnowledge 创建知识对象的辅助方法
func (sc *SmartKnowledgeCrawler) createKnowledge(question, answer string, result *PageInfo, target *SmartCrawlTarget, extractionType string) *learning.LearnedKnowledge {
	return &learning.LearnedKnowledge{
		Question:    question,
		Answer:      answer,
		Source:      "crawler",
		Confidence:  float32(result.QualityScore),
		Category:    target.Category,
		Keywords:    target.Keywords,
		Context:     fmt.Sprintf("从网站爬取: %s", result.URL),
		LearnedFrom: "smart_crawler",
		Status:      "approved",
		CreatedAt:   time.Now(),
		Metadata: map[string]interface{}{
			"source_url":      result.URL,
			"crawled_at":      result.CrawledAt,
			"depth":           result.Depth,
			"quality_score":   result.QualityScore,
			"content_length":  len(result.Content),
			"language":        result.Language,
			"extraction_type": extractionType,
		},
	}
}

// generateQuestionFromTitle 从标题生成问题
func (sc *SmartKnowledgeCrawler) generateQuestionFromTitle(title string, target *SmartCrawlTarget) string {
	if title == "" {
		return ""
	}

	// 清理标题
	title = strings.TrimSpace(title)
	title = strings.ReplaceAll(title, "_百度搜索", "")
	title = strings.ReplaceAll(title, " - 百度", "")
	title = strings.ReplaceAll(title, "百度", "")

	// 如果标题已经是问句，直接返回
	if strings.HasSuffix(title, "?") || strings.HasSuffix(title, "？") {
		return title
	}

	// 根据不同类型生成问题
	if strings.Contains(title, "小说") {
		return fmt.Sprintf("什么是%s？", title)
	}

	if strings.Contains(title, "热搜") {
		return "当前有哪些热门话题？"
	}

	if strings.Contains(title, "新闻") {
		return fmt.Sprintf("关于%s的最新消息是什么？", strings.ReplaceAll(title, "新闻", ""))
	}

	// 通用问题生成
	if len(title) > 0 && len(title) < 50 {
		return fmt.Sprintf("什么是%s？", title)
	}

	return ""
}

// extractSpecialSiteKnowledge 提取特殊网站的知识
func (sc *SmartKnowledgeCrawler) extractSpecialSiteKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	url := result.URL
	content := result.Content
	title := result.Title

	// 百度热搜特殊处理 - 扩大匹配范围
	if strings.Contains(url, "top.baidu.com") ||
		(strings.Contains(url, "baidu.com") && (strings.Contains(title, "热搜") || strings.Contains(content, "热搜") ||
			strings.Contains(url, "tab=") || strings.Contains(url, "board"))) {
		log.Printf("   🎯 匹配百度热搜页面，启用智能分析")
		return sc.extractBaiduHotSearchKnowledge(result, target)
	}

	// 百度搜索结果特殊处理
	if strings.Contains(url, "baidu.com/s?") {
		return sc.extractBaiduSearchKnowledge(result, target)
	}

	// 新闻网站特殊处理
	if strings.Contains(url, "news") || strings.Contains(title, "新闻") {
		return sc.extractNewsKnowledge(result, target)
	}

	return nil
}

// extractBaiduHotSearchKnowledge 智能提取百度热搜知识
func (sc *SmartKnowledgeCrawler) extractBaiduHotSearchKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	log.Printf("   🧠 使用NLP智能提取百度热搜知识")

	// 使用NLP内容提取器进行智能分析
	extracted := sc.contentExtractor.ExtractContent(result.Content, result.URL, result.Title)

	// 将NLP提取结果转换为知识
	return sc.convertNLPToKnowledge(extracted, result, target)
}

// extractBaiduSearchKnowledge 提取百度搜索知识
func (sc *SmartKnowledgeCrawler) extractBaiduSearchKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	title := result.Title
	content := result.Content

	// 从搜索标题中提取关键词
	if strings.Contains(title, "_百度搜索") {
		searchTerm := strings.ReplaceAll(title, "_百度搜索", "")
		searchTerm = strings.TrimSpace(searchTerm)

		if len(searchTerm) > 0 && len(content) > 100 {
			question := fmt.Sprintf("关于%s有什么信息？", searchTerm)
			answer := sc.generateSummary(content)

			if len(answer) > 20 {
				return sc.createKnowledge(question, answer, result, target, "baidu_search")
			}
		}
	}

	return nil
}

// extractNewsKnowledge 提取新闻知识
func (sc *SmartKnowledgeCrawler) extractNewsKnowledge(result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	title := result.Title
	content := result.Content

	if len(title) > 0 && len(content) > 100 {
		question := fmt.Sprintf("关于%s的新闻内容是什么？", title)
		answer := sc.generateSummary(content)

		if len(answer) > 20 {
			return sc.createKnowledge(question, answer, result, target, "news_content")
		}
	}

	return nil
}

// analyzeBaiduPage 智能分析百度页面
func (sc *SmartKnowledgeCrawler) analyzeBaiduPage(url, title, content string) *PageAnalysis {
	log.Printf("   🧠 开始智能分析百度页面")
	log.Printf("     URL: %s", url)
	log.Printf("     标题: %s", title)
	log.Printf("     内容长度: %d", len(content))

	analysis := &PageAnalysis{
		Metadata: make(map[string]string),
	}

	// 1. 分析页面类型
	analysis.PageType = sc.detectBaiduPageType(url, title)
	analysis.Category = sc.mapPageTypeToCategory(analysis.PageType)
	log.Printf("     页面类型: %s, 分类: %s", analysis.PageType, analysis.Category)

	// 2. 提取主要内容
	analysis.MainContent = sc.extractMainContent(content, analysis.PageType)
	log.Printf("     提取主要内容: %d 条", len(analysis.MainContent))

	// 3. 提取话题信息
	analysis.Topics = sc.extractTopics(content, analysis.PageType)
	log.Printf("     提取话题信息: %d 个", len(analysis.Topics))

	// 4. 提取关键词
	analysis.Keywords = sc.extractKeywordsFromContent(content, analysis.Topics)
	log.Printf("     提取关键词: %d 个", len(analysis.Keywords))

	// 5. 评估内容质量
	analysis.Quality = sc.evaluateContentQuality(analysis)
	log.Printf("     内容质量评分: %.2f", analysis.Quality)

	// 6. 设置内容类型
	analysis.ContentType = sc.determineContentType(analysis)
	log.Printf("     内容类型: %s", analysis.ContentType)

	return analysis
}

// detectBaiduPageType 检测百度页面类型
func (sc *SmartKnowledgeCrawler) detectBaiduPageType(url, title string) string {
	if strings.Contains(url, "tab=homepage") {
		return "homepage"
	}
	if strings.Contains(url, "tab=realtime") {
		return "realtime"
	}
	if strings.Contains(url, "tab=novel") {
		return "novel"
	}
	if strings.Contains(url, "tab=movie") {
		return "movie"
	}
	if strings.Contains(url, "tab=teleplay") {
		return "teleplay"
	}
	if strings.Contains(url, "tab=game") {
		return "game"
	}
	if strings.Contains(url, "tab=sports") {
		return "sports"
	}

	// 根据标题判断
	if strings.Contains(title, "实时") || strings.Contains(title, "热点") {
		return "realtime"
	}
	if strings.Contains(title, "小说") {
		return "novel"
	}
	if strings.Contains(title, "电影") {
		return "movie"
	}
	if strings.Contains(title, "电视剧") {
		return "teleplay"
	}

	return "general"
}

// mapPageTypeToCategory 将页面类型映射到分类
func (sc *SmartKnowledgeCrawler) mapPageTypeToCategory(pageType string) string {
	categoryMap := map[string]string{
		"homepage": "综合热搜",
		"realtime": "实时热点",
		"novel":    "小说榜单",
		"movie":    "电影榜单",
		"teleplay": "电视剧榜单",
		"game":     "游戏榜单",
		"sports":   "体育榜单",
		"general":  "热门话题",
	}

	if category, exists := categoryMap[pageType]; exists {
		return category
	}
	return "热门话题"
}

// extractMainContent 提取页面主要内容
func (sc *SmartKnowledgeCrawler) extractMainContent(content, pageType string) []string {
	lines := strings.Split(content, "\n")
	mainContent := []string{}

	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 过滤掉无用内容
		if len(line) < 5 || len(line) > 200 {
			continue
		}

		// 过滤导航和广告内容
		if sc.isNavigationOrAd(line) {
			continue
		}

		// 根据页面类型提取相关内容
		if sc.isRelevantContent(line, pageType) {
			mainContent = append(mainContent, line)
			if len(mainContent) >= 20 { // 最多提取20条主要内容
				break
			}
		}
	}

	return mainContent
}

// isNavigationOrAd 判断是否为导航或广告内容
func (sc *SmartKnowledgeCrawler) isNavigationOrAd(line string) bool {
	filterWords := []string{
		"登录", "注册", "首页", "导航", "菜单", "广告", "推广",
		"更多", "展开", "收起", "点击", "链接", "跳转",
		"百度首页", "设置", "产品", "公司", "关于",
	}

	for _, word := range filterWords {
		if strings.Contains(line, word) {
			return true
		}
	}

	return false
}

// isRelevantContent 判断内容是否与页面类型相关
func (sc *SmartKnowledgeCrawler) isRelevantContent(line, pageType string) bool {
	switch pageType {
	case "novel":
		return strings.Contains(line, "小说") || strings.Contains(line, "作者") ||
			strings.Contains(line, "连载") || strings.Contains(line, "完结")
	case "movie":
		return strings.Contains(line, "电影") || strings.Contains(line, "导演") ||
			strings.Contains(line, "主演") || strings.Contains(line, "上映")
	case "teleplay":
		return strings.Contains(line, "电视剧") || strings.Contains(line, "剧集") ||
			strings.Contains(line, "演员") || strings.Contains(line, "播出")
	case "realtime":
		return strings.Contains(line, "热") || strings.Contains(line, "新") ||
			strings.Contains(line, "事件") || strings.Contains(line, "话题")
	default:
		// 通用内容：排除明显的导航和技术内容
		return !strings.Contains(line, "http") && !strings.Contains(line, "www") &&
			!strings.Contains(line, "javascript") && len(line) > 10
	}
}

// extractTopics 提取话题信息
func (sc *SmartKnowledgeCrawler) extractTopics(content, pageType string) []TopicInfo {
	topics := []TopicInfo{}
	lines := strings.Split(content, "\n")

	rank := 1
	for _, line := range lines {
		line = strings.TrimSpace(line)

		// 跳过太短或太长的行
		if len(line) < 3 || len(line) > 100 {
			continue
		}

		// 跳过导航内容
		if sc.isNavigationOrAd(line) {
			continue
		}

		// 根据页面类型提取话题
		if topic := sc.extractTopicFromLine(line, pageType, rank); topic != nil {
			topics = append(topics, *topic)
			rank++
			if len(topics) >= 15 { // 最多提取15个话题
				break
			}
		}
	}

	return topics
}

// extractTopicFromLine 从单行文本提取话题
func (sc *SmartKnowledgeCrawler) extractTopicFromLine(line, pageType string, rank int) *TopicInfo {
	// 清理行内容
	line = strings.TrimSpace(line)

	// 根据页面类型判断是否为有效话题
	isValidTopic := false
	category := ""

	switch pageType {
	case "novel":
		if strings.Contains(line, "小说") || strings.Contains(line, "作者") {
			isValidTopic = true
			category = "小说"
		}
	case "movie":
		if strings.Contains(line, "电影") || strings.Contains(line, "影片") {
			isValidTopic = true
			category = "电影"
		}
	case "teleplay":
		if strings.Contains(line, "电视剧") || strings.Contains(line, "剧集") {
			isValidTopic = true
			category = "电视剧"
		}
	case "realtime":
		// 实时热点：包含数字、热度词汇的内容
		if strings.Contains(line, "热") || strings.Contains(line, "新") ||
			strings.Contains(line, "万") || strings.Contains(line, "沸") {
			isValidTopic = true
			category = "实时热点"
		}
	default:
		// 通用话题：长度适中且不包含技术词汇
		if len(line) >= 5 && len(line) <= 50 &&
			!strings.Contains(line, "http") && !strings.Contains(line, "www") {
			isValidTopic = true
			category = "热门话题"
		}
	}

	if !isValidTopic {
		return nil
	}

	// 提取热度值（如果有）
	hotValue := sc.extractHotValue(line)

	return &TopicInfo{
		Title:       line,
		Description: "",
		Rank:        rank,
		Category:    category,
		HotValue:    hotValue,
	}
}

// extractHotValue 提取热度值
func (sc *SmartKnowledgeCrawler) extractHotValue(line string) string {
	// 查找数字+万的模式
	re := regexp.MustCompile(`(\d+\.?\d*万)`)
	matches := re.FindStringSubmatch(line)
	if len(matches) > 1 {
		return matches[1]
	}

	// 查找纯数字
	re = regexp.MustCompile(`(\d{4,})`) // 至少4位数字
	matches = re.FindStringSubmatch(line)
	if len(matches) > 1 {
		return matches[1]
	}

	return ""
}

// convertNLPToKnowledge 将NLP提取结果转换为知识
func (sc *SmartKnowledgeCrawler) convertNLPToKnowledge(extracted *nlp.ExtractedContent, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	log.Printf("   🔄 转换NLP提取结果为知识")
	log.Printf("     智能标题: %s", extracted.Title)
	log.Printf("     内容类型: %s", extracted.ContentType)
	log.Printf("     话题数量: %d", len(extracted.Topics))
	log.Printf("     提取置信度: %.2f", extracted.Confidence)

	// 检查提取质量
	if extracted.Confidence < 0.3 {
		log.Printf("     ❌ NLP提取置信度过低 (%.2f < 0.3)", extracted.Confidence)
		return nil
	}

	// 根据内容类型生成不同的问题和答案
	var question, answer string

	switch extracted.ContentType {
	case "实时热点":
		question, answer = sc.generateRealtimeQA(extracted)
	case "小说榜单":
		question, answer = sc.generateNovelQA(extracted)
	case "电影榜单":
		question, answer = sc.generateMovieQA(extracted)
	case "电视剧榜单":
		question, answer = sc.generateTeleplayQA(extracted)
	case "热搜榜单":
		question, answer = sc.generateHotSearchQA(extracted)
	default:
		question, answer = sc.generateGeneralQA(extracted)
	}

	if question == "" || answer == "" {
		log.Printf("     ❌ 未能生成有效的问答对")
		return nil
	}

	// 创建知识对象
	knowledge := &learning.LearnedKnowledge{
		Question:    question,
		Answer:      answer,
		Source:      "nlp_crawler",
		Confidence:  float32(extracted.Confidence),
		Category:    extracted.ContentType,
		Keywords:    extracted.Keywords,
		Context:     fmt.Sprintf("NLP智能提取自: %s", result.URL),
		LearnedFrom: "nlp_smart_crawler",
		Status:      "approved",
		CreatedAt:   time.Now(),
		Metadata: map[string]interface{}{
			"source_url":        result.URL,
			"crawled_at":        result.CrawledAt,
			"nlp_confidence":    extracted.Confidence,
			"nlp_content_type":  extracted.ContentType,
			"nlp_sentiment":     extracted.Sentiment,
			"nlp_entities":      len(extracted.Entities),
			"nlp_topics":        len(extracted.Topics),
			"nlp_keywords":      len(extracted.Keywords),
			"extraction_method": "nlp_analysis",
		},
	}

	log.Printf("     ✅ 成功生成NLP知识: %s", question)
	return knowledge
}
