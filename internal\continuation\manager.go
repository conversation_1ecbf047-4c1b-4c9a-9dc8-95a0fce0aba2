package continuation

import (
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/nlp"
	"fmt"
	"strings"
	"time"
)

// ConversationContext 对话上下文
type ConversationContext struct {
	UserID           string                 `json:"user_id"`
	LastQuery        string                 `json:"last_query"`
	LastAnswer       string                 `json:"last_answer"`
	LastIntent       string                 `json:"last_intent"`
	LastSource       string                 `json:"last_source"`
	LastTimestamp    time.Time              `json:"last_timestamp"`
	ContinuationData map[string]interface{} `json:"continuation_data"`
	CanContinue      bool                   `json:"can_continue"`

	// 新增：原始查询信息，用于多次续接
	OriginalQuery     string `json:"original_query"`
	OriginalAnswer    string `json:"original_answer"`
	ContinuationCount int    `json:"continuation_count"`

	// 新增：已回答的内容跟踪，避免重复
	AnsweredContent []string `json:"answered_content"`
	UsedTopics      []string `json:"used_topics"`
}

// ContinuationResult 续接结果
type ContinuationResult struct {
	Success         bool                   `json:"success"`
	ContinuedAnswer string                 `json:"continued_answer"`
	Source          string                 `json:"source"`
	Intent          string                 `json:"intent"`
	Confidence      float64                `json:"confidence"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// Manager 续接管理器
type Manager struct {
	contexts         map[string]*ConversationContext
	nlpProcessor     *nlp.IntegratedProcessor
	knowledgeLearner *learning.KnowledgeLearner
	maxContexts      int
}

// NewManager 创建续接管理器
func NewManager(nlpProcessor *nlp.IntegratedProcessor) *Manager {
	return &Manager{
		contexts:         make(map[string]*ConversationContext),
		nlpProcessor:     nlpProcessor,
		knowledgeLearner: nil, // 将在后续设置
		maxContexts:      100, // 最多保存100个用户的上下文
	}
}

// SetKnowledgeLearner 设置知识学习器
func (m *Manager) SetKnowledgeLearner(learner *learning.KnowledgeLearner) {
	m.knowledgeLearner = learner
}

// UpdateContext 更新对话上下文
func (m *Manager) UpdateContext(userID, query, answer, intent, source string) {
	// 清理过期的上下文
	m.cleanupOldContexts()

	// 判断是否可以续接
	canContinue := m.isAnswerContinuable(answer, intent)

	// 检查是否已有上下文
	existingContext, hasExisting := m.contexts[userID]

	context := &ConversationContext{
		UserID:           userID,
		LastQuery:        query,
		LastAnswer:       answer,
		LastIntent:       intent,
		LastSource:       source,
		LastTimestamp:    time.Now(),
		ContinuationData: make(map[string]interface{}),
		CanContinue:      canContinue,
	}

	// 如果是新的对话或者上一个上下文已过期，设置为原始查询
	if !hasExisting || time.Since(existingContext.LastTimestamp) > 10*time.Minute {
		context.OriginalQuery = query
		context.OriginalAnswer = answer
		context.ContinuationCount = 0
		context.AnsweredContent = []string{answer} // 初始化已回答内容
		context.UsedTopics = []string{}            // 初始化已使用话题
		logger.Infof("🔄 创建新的对话上下文: 原始查询='%s'", query)
	} else {
		// 保持原始查询信息和已回答内容
		context.OriginalQuery = existingContext.OriginalQuery
		context.OriginalAnswer = existingContext.OriginalAnswer
		context.ContinuationCount = existingContext.ContinuationCount
		context.AnsweredContent = existingContext.AnsweredContent
		context.UsedTopics = existingContext.UsedTopics
		logger.Infof("🔄 更新现有对话上下文: 原始查询='%s', 当前查询='%s'", context.OriginalQuery, query)
	}

	// 如果答案可以续接，提取续接数据
	if canContinue {
		context.ContinuationData = m.extractContinuationData(answer, intent)
	}

	m.contexts[userID] = context
	logger.Infof("🔄 更新用户 %s 的对话上下文，可续接: %v", userID, canContinue)
}

// HandleContinuation 处理续接请求
func (m *Manager) HandleContinuation(userID, continuationQuery string) *ContinuationResult {
	context, exists := m.contexts[userID]
	if !exists {
		logger.Infof("❌ 用户 %s 没有可续接的上下文", userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "no_context",
			},
		}
	}

	// 检查上下文是否过期（超过10分钟）
	if time.Since(context.LastTimestamp) > 10*time.Minute {
		logger.Infof("❌ 用户 %s 的上下文已过期", userID)
		delete(m.contexts, userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "context_expired",
			},
		}
	}

	if !context.CanContinue {
		logger.Infof("❌ 用户 %s 的上一个回答不支持续接", userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "not_continuable",
			},
		}
	}

	// 增加续接计数
	context.ContinuationCount++

	// 生成续接内容
	continuedAnswer := m.generateContinuation(context, continuationQuery)
	if continuedAnswer == "" {
		logger.Infof("❌ 无法为用户 %s 生成续接内容", userID)
		return &ContinuationResult{
			Success: false,
			Metadata: map[string]interface{}{
				"reason": "no_continuation_generated",
			},
		}
	}

	// 记录已回答的内容
	context.AnsweredContent = append(context.AnsweredContent, continuedAnswer)
	m.contexts[userID] = context

	logger.Infof("✅ 为用户 %s 生成续接内容，长度: %d, 续接次数: %d", userID, len(continuedAnswer), context.ContinuationCount)

	return &ContinuationResult{
		Success:         true,
		ContinuedAnswer: continuedAnswer,
		Source:          context.LastSource + " (续接)",
		Intent:          context.LastIntent,
		Confidence:      0.8,
		Metadata: map[string]interface{}{
			"original_query":     context.OriginalQuery,
			"original_answer":    context.OriginalAnswer,
			"continuation_type":  m.getContinuationType(continuationQuery),
			"continuation_count": context.ContinuationCount,
		},
	}
}

// isAnswerContinuable 判断答案是否可以续接
func (m *Manager) isAnswerContinuable(answer, intent string) bool {
	// 几乎所有回答都可以续接，除非是极短的回答
	if len(answer) < 10 {
		return false
	}

	// 特定意图的回答总是可以续接
	if intent == "technical_question" || intent == "learned_knowledge" ||
		intent == "general" || intent == "search_result" {
		return true
	}

	// 错误和系统消息不可续接
	if intent == "error" || intent == "system_message" {
		return false
	}

	// 包含列表、步骤等结构化内容的答案可以续接
	continuablePatterns := []string{
		"包括", "例如", "比如", "首先", "其次", "另外", "此外",
		"步骤", "方法", "方式", "特点", "优势", "缺点", "关键词",
		"主要", "话题", "分析", "来源", "http", "https", "www",
		"•", "1.", "2.", "3.", "-", "①", "②", "③",
	}

	for _, pattern := range continuablePatterns {
		if strings.Contains(answer, pattern) {
			return true
		}
	}

	// 默认情况下，大多数回答都可以续接
	return len(answer) > 50
}

// extractContinuationData 提取续接数据
func (m *Manager) extractContinuationData(answer, intent string) map[string]interface{} {
	data := make(map[string]interface{})

	// 提取关键词
	if m.nlpProcessor != nil && m.nlpProcessor.IsInitialized() {
		result := m.nlpProcessor.ProcessText(answer)
		if result != nil && len(result.Keywords) > 0 {
			data["keywords"] = result.Keywords
		}
	}

	// 分析答案结构
	data["answer_length"] = len(answer)
	data["has_list"] = strings.Contains(answer, "•") || strings.Contains(answer, "1.") || strings.Contains(answer, "-")
	data["has_steps"] = strings.Contains(answer, "步骤") || strings.Contains(answer, "方法")
	data["intent"] = intent

	return data
}

// generateContinuation 生成续接内容
func (m *Manager) generateContinuation(context *ConversationContext, continuationQuery string) string {
	continuationType := m.getContinuationType(continuationQuery)

	// 首先尝试查找与原始问题相关的其他具体内容
	relatedContent := m.findRelatedContent(context.OriginalQuery, context.OriginalAnswer, context)
	if relatedContent != "" {
		logger.Infof("✅ 找到与原始查询'%s'相关的内容，长度: %d", context.OriginalQuery, len(relatedContent))
		return relatedContent
	}

	// 如果没有找到相关内容，根据意图生成通用续接
	logger.Infof("❌ 未找到与原始查询'%s'相关的内容，生成通用续接", context.OriginalQuery)
	switch context.LastIntent {
	case "technical_question", "learned_knowledge":
		return m.generateTechnicalContinuation(context, continuationType)
	case "algorithm_request":
		return m.generateAlgorithmContinuation(context, continuationType)
	default:
		return m.generateGeneralContinuation(context, continuationType)
	}
}

// findRelatedContent 查找相关内容
func (m *Manager) findRelatedContent(originalQuery, originalAnswer string, context *ConversationContext) string {
	if m.knowledgeLearner == nil {
		return ""
	}

	// 1. 尝试从知识库中查找与原问题相关的其他内容
	relatedKnowledge, err := m.searchRelatedKnowledge(originalQuery)
	if err != nil {
		logger.Warnf("❌ 查找相关知识失败: %v", err)
		return ""
	}

	if len(relatedKnowledge) > 0 {
		return m.formatRelatedKnowledge(relatedKnowledge, originalQuery)
	}

	// 2. 如果没有找到相关知识，尝试从原答案中提取关键信息进行扩展
	return m.expandFromOriginalAnswer(originalQuery, originalAnswer, context)
}

// searchRelatedKnowledge 搜索相关知识
func (m *Manager) searchRelatedKnowledge(query string) ([]*learning.LearnedKnowledge, error) {
	// 使用知识学习器搜索相关内容
	// 这里可以使用语义搜索或关键词搜索
	return m.knowledgeLearner.GetRecentKnowledge(5) // 先简单获取最近的知识
}

// formatRelatedKnowledge 格式化相关知识
func (m *Manager) formatRelatedKnowledge(knowledge []*learning.LearnedKnowledge, originalQuery string) string {
	var result strings.Builder
	result.WriteString("📚 **与\"" + originalQuery + "\"相关的其他内容：**\n\n")

	count := 0
	for _, k := range knowledge {
		// 跳过与原问题完全相同的内容
		if strings.Contains(strings.ToLower(k.Question), strings.ToLower(originalQuery)) ||
			strings.Contains(strings.ToLower(originalQuery), strings.ToLower(k.Question)) {
			continue
		}

		if count >= 3 { // 最多显示3个相关内容
			break
		}

		result.WriteString(fmt.Sprintf("🔍 **%s**\n", k.Question))
		// 截取答案的前100个字符作为预览
		preview := k.Answer
		if len(preview) > 100 {
			preview = preview[:100] + "..."
		}
		result.WriteString(fmt.Sprintf("   %s\n\n", preview))
		count++
	}

	if count == 0 {
		return ""
	}

	result.WriteString("💡 如需了解详细信息，请直接提问相关问题。")
	return result.String()
}

// expandFromOriginalAnswer 从原答案扩展内容
func (m *Manager) expandFromOriginalAnswer(originalQuery, originalAnswer string, context *ConversationContext) string {
	// 分析原答案中的关键信息
	if strings.Contains(originalAnswer, "百度") || strings.Contains(strings.ToLower(originalQuery), "百度") {
		return m.generateBaiduRelatedContentWithContext(originalQuery, originalAnswer, context)
	}

	// 如果包含技术关键词，生成技术相关内容
	techKeywords := []string{"技术", "开发", "编程", "算法", "系统", "网站", "平台"}
	for _, keyword := range techKeywords {
		if strings.Contains(originalAnswer, keyword) {
			return m.generateTechRelatedContentWithContext(originalQuery, originalAnswer, context)
		}
	}

	return ""
}

// generateBaiduRelatedContent 生成百度相关内容
func (m *Manager) generateBaiduRelatedContent(originalQuery, originalAnswer string) string {
	// 定义百度相关的多个话题层次
	baiduTopics := []map[string]string{
		{
			"title": "百度主要业务",
			"content": `📊 **百度主要业务：**
• 搜索引擎服务 - 中国最大的搜索引擎
• 百度百科 - 中文知识分享平台
• 百度地图 - 地图导航服务
• 百度网盘 - 云存储服务
• 百度贴吧 - 社区交流平台`,
		},
		{
			"title": "百度技术特色",
			"content": `🔧 **百度技术特色：**
• 人工智能技术（百度大脑）
• 自动驾驶技术（Apollo平台）
• 深度学习框架（PaddlePaddle）
• 语音识别技术
• 自然语言处理技术`,
		},
		{
			"title": "百度发展历程",
			"content": `💼 **百度发展历程：**
• 2000年1月 - 李彦宏创立百度
• 2001年 - 推出百度搜索引擎
• 2005年8月 - 在纳斯达克上市
• 2010年 - 推出百度百科、贴吧等产品
• 2017年 - All in AI战略转型`,
		},
		{
			"title": "百度AI产品",
			"content": `🤖 **百度AI产品生态：**
• 小度智能音箱 - 智能语音助手
• 百度Apollo - 自动驾驶开放平台
• 百度飞桨 - 深度学习平台
• 百度智能云 - 云计算服务
• 文心一言 - 大语言模型`,
		},
		{
			"title": "百度商业模式",
			"content": `💰 **百度商业模式：**
• 搜索广告 - 核心收入来源
• 信息流广告 - 移动端变现
• 智能云服务 - B端业务增长
• 自动驾驶 - 未来增长点
• AI技术授权 - 技术变现`,
		},
		{
			"title": "百度竞争优势",
			"content": `⭐ **百度竞争优势：**
• 中文搜索技术领先
• 海量用户数据积累
• 强大的AI技术实力
• 完善的生态系统
• 本土化服务优势`,
		},
	}

	return m.getNextTopicContent("百度", baiduTopics)
}

// generateBaiduRelatedContentWithContext 生成百度相关内容（带上下文）
func (m *Manager) generateBaiduRelatedContentWithContext(originalQuery, originalAnswer string, context *ConversationContext) string {
	// 定义百度相关的多个话题层次
	baiduTopics := []map[string]string{
		{
			"title": "百度主要业务",
			"content": `📊 **百度主要业务：**
• 搜索引擎服务 - 中国最大的搜索引擎
• 百度百科 - 中文知识分享平台
• 百度地图 - 地图导航服务
• 百度网盘 - 云存储服务
• 百度贴吧 - 社区交流平台`,
		},
		{
			"title": "百度技术特色",
			"content": `🔧 **百度技术特色：**
• 人工智能技术（百度大脑）
• 自动驾驶技术（Apollo平台）
• 深度学习框架（PaddlePaddle）
• 语音识别技术
• 自然语言处理技术`,
		},
		{
			"title": "百度发展历程",
			"content": `💼 **百度发展历程：**
• 2000年1月 - 李彦宏创立百度
• 2001年 - 推出百度搜索引擎
• 2005年8月 - 在纳斯达克上市
• 2010年 - 推出百度百科、贴吧等产品
• 2017年 - All in AI战略转型`,
		},
		{
			"title": "百度AI产品",
			"content": `🤖 **百度AI产品生态：**
• 小度智能音箱 - 智能语音助手
• 百度Apollo - 自动驾驶开放平台
• 百度飞桨 - 深度学习平台
• 百度智能云 - 云计算服务
• 文心一言 - 大语言模型`,
		},
		{
			"title": "百度商业模式",
			"content": `💰 **百度商业模式：**
• 搜索广告 - 核心收入来源
• 信息流广告 - 移动端变现
• 智能云服务 - B端业务增长
• 自动驾驶 - 未来增长点
• AI技术授权 - 技术变现`,
		},
		{
			"title": "百度竞争优势",
			"content": `⭐ **百度竞争优势：**
• 中文搜索技术领先
• 海量用户数据积累
• 强大的AI技术实力
• 完善的生态系统
• 本土化服务优势`,
		},
	}

	return m.selectNextTopicWithContext(baiduTopics, context)
}

// getNextTopicContent 获取下一个话题内容
func (m *Manager) getNextTopicContent(queryKey string, topics []map[string]string) string {
	// 从contexts中获取当前用户的上下文（这里需要传入userID，暂时用一个通用方法）
	// 由于这个方法没有userID参数，我们需要重构一下

	// 简化版本：基于已回答内容的长度来决定返回哪个话题
	// 这里我们需要从调用方传入context
	return m.selectNextTopic(topics, []string{}) // 临时实现
}

// selectNextTopicWithContext 基于上下文选择下一个话题
func (m *Manager) selectNextTopicWithContext(topics []map[string]string, context *ConversationContext) string {
	for _, topic := range topics {
		title := topic["title"]
		// 检查这个话题是否已经使用过
		used := false
		for _, usedTopic := range context.UsedTopics {
			if usedTopic == title {
				used = true
				break
			}
		}

		if !used {
			// 记录已使用的话题
			context.UsedTopics = append(context.UsedTopics, title)
			return "🌐 **" + title + "：**\n\n" + topic["content"] + "\n\n💡 如需了解更多信息，请继续提问！"
		}
	}

	// 如果所有话题都用完了，返回总结
	return `📝 **关于这个话题的补充信息已经比较全面了。**

如果您想了解更具体的细节，请提出更明确的问题，比如：
• 具体的产品功能
• 技术实现细节
• 发展历史事件
• 商业策略分析

我会为您提供更深入的解答！`
}

// selectNextTopic 选择下一个话题
func (m *Manager) selectNextTopic(topics []map[string]string, usedTopics []string) string {
	for _, topic := range topics {
		title := topic["title"]
		// 检查这个话题是否已经使用过
		used := false
		for _, usedTopic := range usedTopics {
			if usedTopic == title {
				used = true
				break
			}
		}

		if !used {
			return "🌐 **" + title + "：**\n\n" + topic["content"] + "\n\n💡 如需了解更多信息，请继续提问！"
		}
	}

	// 如果所有话题都用完了，返回总结
	return `📝 **关于这个话题的补充信息已经比较全面了。**

如果您想了解更具体的细节，请提出更明确的问题，比如：
• 具体的产品功能
• 技术实现细节
• 发展历史事件
• 商业策略分析

我会为您提供更深入的解答！`
}

// generateTechRelatedContentWithContext 生成技术相关内容（带上下文）
func (m *Manager) generateTechRelatedContentWithContext(originalQuery, originalAnswer string, context *ConversationContext) string {
	techTopics := []map[string]string{
		{
			"title": "相关技术栈",
			"content": `🔧 **相关技术栈：**
• 前端技术：HTML、CSS、JavaScript、React、Vue
• 后端技术：Java、Python、Go、Node.js、C++
• 数据库：MySQL、MongoDB、Redis、PostgreSQL
• 云服务：阿里云、腾讯云、AWS、Azure`,
		},
		{
			"title": "学习资源",
			"content": `📚 **学习资源：**
• 官方文档和教程
• 开源项目和代码示例
• 技术社区和论坛（Stack Overflow、GitHub）
• 在线课程和培训（Coursera、edX）
• 技术博客和书籍`,
		},
		{
			"title": "最佳实践",
			"content": `⚡ **最佳实践：**
• 代码规范和质量控制
• 性能优化和监控
• 安全防护和数据保护
• 团队协作和版本管理
• 持续集成和部署（CI/CD）`,
		},
	}

	return m.selectNextTopicWithContext(techTopics, context)
}

// generateTechRelatedContent 生成技术相关内容
func (m *Manager) generateTechRelatedContent(originalQuery, originalAnswer string) string {
	return `💻 **技术相关的补充信息：**

🔧 **相关技术栈：**
• 前端技术：HTML、CSS、JavaScript
• 后端技术：Java、Python、Go、Node.js
• 数据库：MySQL、MongoDB、Redis
• 云服务：阿里云、腾讯云、AWS

📚 **学习资源：**
• 官方文档和教程
• 开源项目和代码示例
• 技术社区和论坛
• 在线课程和培训

⚡ **最佳实践：**
• 代码规范和质量控制
• 性能优化和监控
• 安全防护和数据保护
• 团队协作和版本管理

如需了解具体技术细节，请提出更具体的问题！`
}

// getContinuationType 获取续接类型
func (m *Manager) getContinuationType(query string) string {
	query = strings.ToLower(query)

	if strings.Contains(query, "详细") || strings.Contains(query, "具体") {
		return "detailed"
	}
	if strings.Contains(query, "例子") || strings.Contains(query, "示例") {
		return "example"
	}
	if strings.Contains(query, "步骤") || strings.Contains(query, "方法") {
		return "steps"
	}
	if strings.Contains(query, "还有") || strings.Contains(query, "其他") {
		return "more"
	}

	return "general"
}

// generateTechnicalContinuation 生成技术续接内容
func (m *Manager) generateTechnicalContinuation(context *ConversationContext, continuationType string) string {
	baseAnswer := context.LastAnswer

	switch continuationType {
	case "detailed":
		return m.generateDetailedExplanation(baseAnswer)
	case "example":
		return m.generateExamples(baseAnswer)
	case "steps":
		return m.generateSteps(baseAnswer)
	case "more":
		return m.generateMoreInfo(baseAnswer)
	default:
		return m.generateGeneralTechnicalContinuation(baseAnswer)
	}
}

// generateDetailedExplanation 生成详细解释
func (m *Manager) generateDetailedExplanation(baseAnswer string) string {
	return "让我为您详细解释一下：\n\n" +
		"📋 **深入分析**：\n" +
		"• 从技术原理来看，这涉及到多个层面的考虑\n" +
		"• 在实际应用中，需要注意性能和兼容性问题\n" +
		"• 最佳实践建议结合具体场景来选择合适的方案\n\n" +
		"💡 **关键要点**：\n" +
		"• 理解底层机制有助于更好地应用这个技术\n" +
		"• 注意边界条件和异常处理\n" +
		"• 考虑扩展性和维护性"
}

// generateExamples 生成示例
func (m *Manager) generateExamples(baseAnswer string) string {
	return "让我给您举几个具体的例子：\n\n" +
		"🔍 **示例1**：\n" +
		"在实际项目中，这种情况经常出现...\n\n" +
		"🔍 **示例2**：\n" +
		"另一个常见的应用场景是...\n\n" +
		"🔍 **示例3**：\n" +
		"在特殊情况下，我们可能需要..."
}

// generateSteps 生成步骤
func (m *Manager) generateSteps(baseAnswer string) string {
	return "让我为您详细说明具体步骤：\n\n" +
		"📝 **第一步**：准备工作\n" +
		"• 确认环境配置\n" +
		"• 检查依赖项\n\n" +
		"📝 **第二步**：核心实现\n" +
		"• 编写主要逻辑\n" +
		"• 处理边界情况\n\n" +
		"📝 **第三步**：测试验证\n" +
		"• 单元测试\n" +
		"• 集成测试"
}

// generateMoreInfo 生成更多信息
func (m *Manager) generateMoreInfo(baseAnswer string) string {
	return "还有一些重要的补充信息：\n\n" +
		"🔧 **相关技术**：\n" +
		"• 与此相关的其他技术栈\n" +
		"• 可以配合使用的工具\n\n" +
		"⚠️ **注意事项**：\n" +
		"• 常见的陷阱和误区\n" +
		"• 性能优化建议\n\n" +
		"📚 **进一步学习**：\n" +
		"• 推荐的学习资源\n" +
		"• 相关的最佳实践"
}

// generateGeneralTechnicalContinuation 生成通用技术续接
func (m *Manager) generateGeneralTechnicalContinuation(baseAnswer string) string {
	return "让我继续为您补充一些相关信息：\n\n" +
		"💡 **补充说明**：\n" +
		"基于刚才的回答，还有几个重要的方面需要考虑...\n\n" +
		"🎯 **实践建议**：\n" +
		"在实际应用中，建议您注意以下几点..."
}

// generateAlgorithmContinuation 生成算法续接内容
func (m *Manager) generateAlgorithmContinuation(context *ConversationContext, continuationType string) string {
	return "关于算法的进一步说明：\n\n" +
		"📊 **复杂度分析**：\n" +
		"• 时间复杂度考虑\n" +
		"• 空间复杂度优化\n\n" +
		"🔄 **算法优化**：\n" +
		"• 可能的改进方案\n" +
		"• 适用场景分析"
}

// generateGeneralContinuation 生成通用续接内容
func (m *Manager) generateGeneralContinuation(context *ConversationContext, continuationType string) string {
	return "让我继续为您说明：\n\n" +
		"基于前面的内容，还有一些相关的信息可以分享给您..."
}

// cleanupOldContexts 清理过期的上下文
func (m *Manager) cleanupOldContexts() {
	if len(m.contexts) <= m.maxContexts {
		return
	}

	// 删除最旧的上下文
	oldestTime := time.Now()
	oldestUserID := ""

	for userID, context := range m.contexts {
		if context.LastTimestamp.Before(oldestTime) {
			oldestTime = context.LastTimestamp
			oldestUserID = userID
		}
	}

	if oldestUserID != "" {
		delete(m.contexts, oldestUserID)
		logger.Infof("🧹 清理过期上下文: %s", oldestUserID)
	}
}

// GetContext 获取用户上下文
func (m *Manager) GetContext(userID string) *ConversationContext {
	return m.contexts[userID]
}

// HasContinuableContext 检查用户是否有可续接的上下文
func (m *Manager) HasContinuableContext(userID string) bool {
	context, exists := m.contexts[userID]
	if !exists {
		return false
	}

	// 检查是否过期
	if time.Since(context.LastTimestamp) > 10*time.Minute {
		delete(m.contexts, userID)
		return false
	}

	return context.CanContinue
}
