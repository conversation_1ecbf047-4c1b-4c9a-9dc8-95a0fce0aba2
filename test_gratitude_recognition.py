#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FAQ系统的感谢识别能力
"""

import requests
import json
import time

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/ask"

def test_conversation(question, expected_keywords=None):
    """测试对话功能"""
    print(f"\n🔍 测试问题: {question}")
    print("-" * 50)
    
    try:
        response = requests.post(ASK_ENDPOINT, 
                               json={"question": question},
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get("answer", "")
            intent = data.get("intent", "")
            confidence = data.get("confidence", 0)
            
            print(f"✅ 回答: {answer}")
            print(f"📊 意图: {intent} (置信度: {confidence:.2f})")
            
            # 检查期望的关键词
            if expected_keywords:
                found_keywords = []
                for keyword in expected_keywords:
                    if keyword in answer:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"🎯 包含期望关键词: {', '.join(found_keywords)}")
                else:
                    print(f"⚠️ 未找到期望关键词: {', '.join(expected_keywords)}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🙏 开始测试FAQ系统的感谢识别能力")
    print("=" * 60)
    
    # 测试用例列表
    test_cases = [
        # 基础感谢测试
        ("谢谢", ["不客气", "很高兴", "帮助"]),
        ("多谢", ["不客气", "应该做的", "开心"]),
        ("感谢", ["满意", "收获", "使命"]),
        ("thank you", ["welcome", "glad", "help"]),
        ("thanks", ["welcome", "glad", "help"]),
        ("thx", ["welcome", "glad", "help"]),
        ("ty", ["welcome", "glad", "help"]),
        
        # 强调感谢测试
        ("真的谢谢", ["太客气了", "荣幸", "使命达成"]),
        ("真的感谢", ["太客气了", "荣幸", "互相成就"]),
        ("非常感谢", ["太客气了", "荣幸", "共同成长"]),
        ("十分感谢", ["太客气了", "荣幸", "继续前行"]),
        ("万分感谢", ["太客气了", "荣幸", "技术挑战"]),
        ("特别感谢", ["太客气了", "荣幸", "帮助"]),
        ("太感谢了", ["太客气了", "荣幸", "意义"]),
        ("感谢不尽", ["太客气了", "荣幸", "动力"]),
        ("无比感谢", ["太客气了", "荣幸", "成就感"]),
        ("深深感谢", ["太客气了", "荣幸", "收获"]),
        ("由衷感谢", ["太客气了", "荣幸", "前行"]),
        ("thank you so much", ["welcome", "glad", "Technical solutions"]),
        ("thank you very much", ["welcome", "glad", "Knowledge sharing"]),
        ("thanks a lot", ["welcome", "glad", "Continuous support"]),
        ("many thanks", ["welcome", "glad", "Personalized service"]),
        
        # 正式感谢测试
        ("感谢您", ["感谢您的认可", "荣幸", "专业态度"]),
        ("谢谢您", ["感谢您的认可", "荣幸", "精准解答"]),
        ("多谢您", ["感谢您的认可", "荣幸", "持续学习"]),
        ("致谢", ["感谢您的认可", "荣幸", "长期合作"]),
        ("表示感谢", ["感谢您的认可", "荣幸", "专业"]),
        ("深表感谢", ["感谢您的认可", "荣幸", "信任"]),
        ("衷心感谢", ["感谢您的认可", "荣幸", "技术伙伴"]),
        ("诚挚感谢", ["感谢您的认可", "荣幸", "咨询"]),
        ("真诚感谢", ["感谢您的认可", "荣幸", "需求"]),
        
        # 随意感谢测试
        ("谢了", ["不客气啦", "开心", "自然对话"]),
        ("谢啦", ["不客气啦", "开心", "轻松交流"]),
        ("3q", ["不客气啦", "开心", "高效解决"]),
        ("3Q", ["不客气啦", "开心", "随时在线"]),
        ("tks", ["不客气啦", "开心", "简单直接"]),
        
        # 具体感谢测试
        ("谢谢你的帮助", ["很高兴", "有帮助", "质量提升"]),
        ("感谢你的帮助", ["很高兴", "有帮助", "深度理解"]),
        ("谢谢你的回答", ["很高兴", "有帮助", "知识扩展"]),
        ("感谢你的回答", ["很高兴", "有帮助", "个性化服务"]),
        ("谢谢你的解答", ["很高兴", "有帮助", "疑问"]),
        ("感谢你的解答", ["很高兴", "有帮助", "深入"]),
        ("谢谢你的指导", ["很高兴", "有帮助", "解释"]),
        ("感谢你的指导", ["很高兴", "有帮助", "告诉我"]),
        ("谢谢你的支持", ["很高兴", "有帮助", "反馈"]),
        ("感谢你的支持", ["很高兴", "有帮助", "改进"]),
        ("谢谢你的服务", ["很高兴", "有帮助", "服务"]),
        ("感谢你的服务", ["很高兴", "有帮助", "需求"]),
        
        # 带感叹号的感谢测试
        ("谢谢！", ["不客气", "很高兴", "帮助"]),
        ("感谢！", ["满意", "收获", "使命"]),
        ("非常感谢！", ["太客气了", "荣幸", "成就感"]),
        ("thank you!", ["welcome", "glad", "help"]),
        
        # 重复感谢测试
        ("谢谢谢谢", ["不客气", "很高兴", "帮助"]),
        ("感谢感谢", ["满意", "收获", "使命"]),
        ("thanks thanks", ["welcome", "glad", "help"]),
        
        # 混合测试 - 感谢 + 其他内容
        ("你好，谢谢", ["您好", "不客气"]),
        ("谢谢，再见", ["不客气", "再见"]),
        
        # 边界测试 - 技术问题（应该不被误判为感谢）
        ("MySQL谢谢", ["MySQL", "数据库", "技术"]),
        ("代码感谢", ["代码", "技术", "编程"]),
        ("API thanks", ["API", "接口", "技术"]),
        
        # 表情符号测试
        ("谢谢😊", ["不客气", "很高兴", "帮助"]),
        ("感谢🙏", ["满意", "收获", "使命"]),
        ("thank you❤️", ["welcome", "glad", "help"]),
        
        # 组合感谢测试
        ("真的非常感谢您的帮助", ["太客气了", "荣幸", "成就感"]),
        ("万分感谢您的详细解答", ["太客气了", "荣幸", "意义"]),
        ("衷心感谢您的专业指导", ["感谢您的认可", "荣幸", "专业"]),
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for question, expected_keywords in test_cases:
        if test_conversation(question, expected_keywords):
            success_count += 1
        time.sleep(0.5)  # 避免请求过快
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试完成: {success_count}/{total_count} 成功")
    print(f"✅ 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试用例都通过了！")
    else:
        print(f"⚠️ 有 {total_count - success_count} 个测试用例需要优化")

if __name__ == "__main__":
    main()
