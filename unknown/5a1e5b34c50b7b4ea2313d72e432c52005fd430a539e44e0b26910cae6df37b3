-- 移除FAQ相关表的SQL脚本
-- 执行前请确保已备份重要数据

USE faqdb;

-- 1. 检查表是否存在
SELECT 'Checking existing tables...' as status;
SHOW TABLES LIKE 'faq%';

-- 2. 删除外键约束（如果存在）
SET FOREIGN_KEY_CHECKS = 0;

-- 3. 删除faq_performance表
DROP TABLE IF EXISTS faq_performance;
SELECT 'faq_performance table dropped' as status;

-- 4. 删除faq表
DROP TABLE IF EXISTS faq;
SELECT 'faq table dropped' as status;

-- 5. 删除爬虫相关的FAQ表（如果存在）
DROP TABLE IF EXISTS crawler_faq_metadata;
DROP TABLE IF EXISTS vector_quality_metrics;
DROP VIEW IF EXISTS crawler_faq_complete;
SELECT 'Crawler FAQ tables dropped' as status;

-- 6. 重新启用外键检查
SET FOREIGN_KEY_CHECKS = 1;

-- 7. 显示剩余的表
SELECT 'Remaining tables:' as status;
SHOW TABLES;

-- 8. 验证learned_knowledge表存在
SELECT 'Checking learned_knowledge table...' as status;
DESCRIBE learned_knowledge;

SELECT 'FAQ tables removal completed successfully!' as status;
