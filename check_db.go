package main

import (
	"database/sql"
	"fmt"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 系统配置的数据库连接
	systemDSN := "root:park%25123456@tcp(127.0.0.1:33508)/faqdb?charset=utf8mb4&parseTime=True&loc=Local"
	
	// 备用配置
	altDSN := "root:root123@tcp(localhost:3306)/faq_system?charset=utf8mb4&parseTime=True&loc=Local"
	
	fmt.Println("🔧 调试知识更新问题")
	fmt.Println(strings.Repeat("=", 50))
	
	// 尝试系统配置
	fmt.Println("🔗 尝试连接系统数据库 (faqdb)...")
	if db, err := sql.Open("mysql", systemDSN); err == nil {
		defer db.Close()
		if err := db.<PERSON>(); err == nil {
			fmt.Println("✅ 系统数据库连接成功")
			checkDatabase(db, "faqdb")
		} else {
			fmt.Printf("❌ 系统数据库连接失败: %v\n", err)
		}
	} else {
		fmt.Printf("❌ 系统数据库打开失败: %v\n", err)
	}
	
	fmt.Println("\n" + strings.Repeat("=", 50))
	
	// 尝试备用配置
	fmt.Println("🔗 尝试连接备用数据库 (faq_system)...")
	if db, err := sql.Open("mysql", altDSN); err == nil {
		defer db.Close()
		if err := db.Ping(); err == nil {
			fmt.Println("✅ 备用数据库连接成功")
			checkDatabase(db, "faq_system")
		} else {
			fmt.Printf("❌ 备用数据库连接失败: %v\n", err)
		}
	} else {
		fmt.Printf("❌ 备用数据库打开失败: %v\n", err)
	}
}

func checkDatabase(db *sql.DB, dbName string) {
	fmt.Printf("\n📊 检查数据库: %s\n", dbName)
	
	// 检查当前数据库
	var currentDB string
	if err := db.QueryRow("SELECT DATABASE()").Scan(&currentDB); err == nil {
		fmt.Printf("📊 当前数据库: %s\n", currentDB)
	}
	
	// 检查learned_knowledge表是否存在
	var tableExists int
	query := `SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES 
			  WHERE TABLE_SCHEMA = DATABASE() AND TABLE_NAME = 'learned_knowledge'`
	if err := db.QueryRow(query).Scan(&tableExists); err == nil {
		fmt.Printf("📊 learned_knowledge表存在: %t\n", tableExists > 0)
		
		if tableExists > 0 {
			// 检查记录总数
			var totalCount int
			if err := db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&totalCount); err == nil {
				fmt.Printf("📊 learned_knowledge表记录数: %d\n", totalCount)
			}
			
			// 检查ID=5的记录
			var question string
			if err := db.QueryRow("SELECT question FROM learned_knowledge WHERE id = 5").Scan(&question); err == nil {
				fmt.Printf("✅ 找到ID=5的记录: %s\n", question)
			} else {
				fmt.Printf("❌ 未找到ID=5的记录: %v\n", err)
				
				// 显示前几条记录的ID
				rows, err := db.Query("SELECT id, question FROM learned_knowledge LIMIT 5")
				if err == nil {
					defer rows.Close()
					fmt.Println("📋 现有记录的ID:")
					for rows.Next() {
						var id int
						var q string
						if err := rows.Scan(&id, &q); err == nil {
							maxLen := 50
							if len(q) < maxLen {
								maxLen = len(q)
							}
							fmt.Printf("  ID=%d: %s\n", id, q[:maxLen])
						}
					}
				}
			}
		}
	} else {
		fmt.Printf("❌ 检查表存在性失败: %v\n", err)
	}
}
