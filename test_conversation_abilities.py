#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FAQ系统新增的基本对话能力
"""

import requests
import json
import time

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/ask"

def test_conversation(question, expected_keywords=None):
    """测试对话功能"""
    print(f"\n🔍 测试问题: {question}")
    print("-" * 50)
    
    try:
        response = requests.post(ASK_ENDPOINT, 
                               json={"question": question},
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get("answer", "")
            intent = data.get("intent", "")
            confidence = data.get("confidence", 0)
            
            print(f"✅ 回答: {answer}")
            print(f"📊 意图: {intent} (置信度: {confidence:.2f})")
            
            # 检查期望的关键词
            if expected_keywords:
                found_keywords = []
                for keyword in expected_keywords:
                    if keyword in answer:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"🎯 包含期望关键词: {', '.join(found_keywords)}")
                else:
                    print(f"⚠️ 未找到期望关键词: {', '.join(expected_keywords)}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 开始测试FAQ系统的基本对话能力")
    print("=" * 60)
    
    # 测试用例列表
    test_cases = [
        # 问候类
        ("你好", ["您好", "欢迎", "智能FAQ"]),
        ("hello", ["Hello", "技术问题"]),
        ("早上好", ["早上好", "欢迎"]),
        
        # 身份询问类
        ("你是谁", ["智能FAQ", "技术助手", "帮您"]),
        ("你叫什么", ["智能FAQ", "技术助手"]),
        ("介绍一下自己", ["智能FAQ", "技术助手"]),
        
        # 能力询问类
        ("你会什么", ["智能对话", "技术专业", "学习进化"]),
        ("你能做什么", ["意图识别", "情感感知", "知识问答"]),
        ("你有什么功能", ["多轮对话", "算法计算", "向量搜索"]),
        
        # 请求发言类
        ("你说", ["很乐意", "分享知识", "技术话题"]),
        ("你请说", ["荣幸", "服务", "具体说明"]),
        ("说说看", ["技术话题", "算法数学", "AI技术"]),
        
        # 教学请求类
        ("让我告诉你", ["乐意", "学习", "知识吸收"]),
        ("我来教你", ["感谢", "教学", "认真记录"]),
        ("我知道这个", ["了解", "分享", "学习"]),
        
        # 求助类
        ("帮我", ["乐意帮助", "具体问题", "解决方案"]),
        ("我需要帮助", ["帮助方式", "问题诊断", "步骤指导"]),
        ("求助", ["收到", "求助信号", "全力帮助"]),
        
        # 礼貌用语类
        ("请", ["客气", "服务", "朋友"]),
        ("麻烦你", ["不麻烦", "职责", "全力以赴"]),
        ("不好意思", ["不用道歉", "自然", "合作伙伴"]),
        
        # 确认类
        ("好的", ["很好", "继续", "技术相关"]),
        ("明白", ["很好", "问题", "随时"]),
        ("ok", ["很好", "继续"]),
        
        # 否定类
        ("不对", ["理解错了", "指正", "正确"]),
        ("不懂", ["没关系", "详细解释", "帮您理解"]),
        ("不是", ["理解有误", "正确信息", "学习"]),
        
        # 聊天请求类
        ("聊天", ["乐意", "聊天", "智能对话"]),
        ("我们聊聊", ["聊天伙伴", "有趣", "话题"]),
        ("随便聊聊", ["轻松", "聊天", "感兴趣"]),
        
        # 感谢类
        ("谢谢", ["不客气", "帮助", "随时"]),
        ("感谢", ["不用谢", "荣幸", "服务"]),
        ("多谢", ["不客气", "问题"]),
        
        # 技术问题（验证原有功能）
        ("什么是LocalAI", ["LocalAI", "本地AI", "推理引擎"]),
        ("MySQL", ["MySQL", "数据库", "FAQ系统"]),
        ("向量搜索", ["向量搜索", "语义", "embedding"]),
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for question, expected_keywords in test_cases:
        if test_conversation(question, expected_keywords):
            success_count += 1
        time.sleep(0.5)  # 避免请求过快
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试完成: {success_count}/{total_count} 成功")
    print(f"✅ 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试用例都通过了！")
    else:
        print(f"⚠️ 有 {total_count - success_count} 个测试用例需要优化")

if __name__ == "__main__":
    main()
