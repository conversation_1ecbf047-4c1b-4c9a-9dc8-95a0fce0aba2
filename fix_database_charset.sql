-- 修复数据库字符集问题

-- 1. 检查当前表的字符集
SHOW CREATE TABLE learned_knowledge;

-- 2. 修改表的字符集为统一的utf8mb4_general_ci
ALTER TABLE learned_knowledge CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 3. 确保所有文本字段使用相同的字符集
ALTER TABLE learned_knowledge 
MODIFY COLUMN question TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
MODIFY COLUMN answer TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
MODIFY COLUMN source VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
MODIFY COLUMN category VARCHAR(100) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
MODIFY COLUMN keywords JSON,
MODIFY COLUMN context TEXT CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
MODIFY COLUMN learned_from VARCHAR(255) CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci,
MODIFY COLUMN status ENUM('pending', 'approved', 'rejected') CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 4. 检查修改后的表结构
SHOW CREATE TABLE learned_knowledge;

-- 5. 同时修复其他相关表
ALTER TABLE crawl_results CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;
ALTER TABLE crawl_targets CONVERT TO CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci;

-- 6. 查看修复结果
SELECT TABLE_NAME, TABLE_COLLATION 
FROM information_schema.TABLES 
WHERE TABLE_SCHEMA = 'faqdb' 
AND TABLE_NAME IN ('learned_knowledge', 'crawl_results', 'crawl_targets');
