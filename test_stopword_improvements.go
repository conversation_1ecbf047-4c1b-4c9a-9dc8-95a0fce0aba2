package main

import (
	"fmt"
	"log"
	"strings"

	"faq-system/internal/mysql"
	"faq-system/internal/nlp"
	"faq-system/internal/semantic"
)

func main() {
	fmt.Println("🧪 测试通用词汇污染修复效果")
	fmt.Println(strings.Repeat("=", 50))

	// 初始化停用词管理器
	stopWordsManager := nlp.GetGlobalStopWordsManager()

	// 测试用例
	testCases := []struct {
		query       string
		description string
	}{
		{"b30是什么", "原问题：应该不匹配C#相关内容"},
		{"什么是", "纯疑问词：应该被识别为通用疑问"},
		{"是什么", "纯疑问词：应该被识别为通用疑问"},
		{"C#是什么", "技术词汇+疑问词：应该匹配C#内容"},
		{"Go语言怎么安装", "技术词汇+疑问词：应该匹配Go相关内容"},
		{"python配置环境", "技术词汇：应该匹配Python相关内容"},
		{"abc123是什么", "随机代码+疑问词：应该被识别为通用疑问"},
	}

	// 模拟FAQ数据
	faqs := []mysql.FAQ{
		{
			ID:       1,
			Question: "C#是什么编程语言",
			Answer:   "C#是微软开发的面向对象编程语言，运行在.NET框架上...",
		},
		{
			ID:       2,
			Question: "Go语言如何安装",
			Answer:   "Go语言安装步骤：1. 下载Go安装包 2. 配置环境变量...",
		},
		{
			ID:       3,
			Question: "Python环境配置指南",
			Answer:   "Python环境配置包括：1. 安装Python解释器 2. 配置pip...",
		},
	}

	// 创建语义匹配器
	_ = semantic.NewSmartMatcher(faqs) // 暂时不使用，避免编译错误

	fmt.Println("\n📊 停用词管理器测试:")
	for _, testCase := range testCases {
		fmt.Printf("\n🔍 测试查询: %s (%s)\n", testCase.query, testCase.description)

		// 测试词汇类型识别
		wordType := stopWordsManager.GetDominantWordType(testCase.query)
		fmt.Printf("   词汇类型: %s\n", wordType)

		// 测试技术词汇检测
		hasTechWords := stopWordsManager.HasSignificantTechnicalWords(testCase.query)
		fmt.Printf("   包含技术词汇: %t\n", hasTechWords)

		// 测试主题识别
		topic := identifyTopicTest(testCase.query, stopWordsManager)
		fmt.Printf("   识别主题: %s\n", topic)

		// 测试权重计算
		words := strings.Fields(strings.ToLower(testCase.query))
		weightedScore := stopWordsManager.CalculateWeightedScore(words, 1.0)
		fmt.Printf("   加权分数: %.3f\n", weightedScore)
	}

	fmt.Println("\n📊 语义匹配测试:")
	for _, testCase := range testCases {
		fmt.Printf("\n🔍 测试查询: %s\n", testCase.query)

		// 测试每个FAQ的匹配情况
		for _, faq := range faqs {
			// 模拟语义相似性检查
			similar := testSemanticSimilarity(testCase.query, faq, stopWordsManager)
			fmt.Printf("   vs FAQ %d (%s): %t\n", faq.ID, faq.Question[:20]+"...", similar)

			// 测试主题冲突
			conflict := testTopicConflict(testCase.query, faq, stopWordsManager)
			fmt.Printf("   主题冲突: %t\n", conflict)
		}
	}

	fmt.Println("\n✅ 测试完成")
}

// identifyTopicTest 测试主题识别
func identifyTopicTest(text string, swm *nlp.StopWordsManager) string {
	textLower := strings.ToLower(text)

	// 检查是否为纯疑问词查询
	questionWords := []string{"什么", "是什么", "什么是", "怎么", "如何", "为什么"}
	questionWordCount := 0
	totalWords := len(strings.Fields(textLower))

	for _, qw := range questionWords {
		if strings.Contains(textLower, qw) {
			questionWordCount++
		}
	}

	// 如果疑问词占比过高，且没有明确技术词汇，标记为通用疑问
	if questionWordCount > 0 && totalWords <= 3 && !swm.HasSignificantTechnicalWords(textLower) {
		return "generic_question"
	}

	// 编程语言主题
	programmingLanguages := map[string]string{
		"go语言": "go", "golang": "go", "go编程": "go", "go开发": "go",
		"python": "python", "python编程": "python", "python开发": "python",
		"java": "java", "java编程": "java", "java开发": "java",
		"javascript": "javascript", "js": "javascript", "前端": "javascript",
		"c++": "cpp", "cpp": "cpp", "c语言": "c",
		"c#": "csharp", "csharp": "csharp", ".net": "csharp",
		"rust": "rust", "php": "php", "ruby": "ruby",
	}

	for keyword, topic := range programmingLanguages {
		if strings.Contains(textLower, keyword) {
			return topic
		}
	}

	return "general"
}

// testSemanticSimilarity 测试语义相似性
func testSemanticSimilarity(question string, faq mysql.FAQ, swm *nlp.StopWordsManager) bool {
	questionLower := strings.ToLower(question)
	faqQuestionLower := strings.ToLower(faq.Question)
	faqAnswerLower := strings.ToLower(faq.Answer)

	// 检查问题的词汇组成
	questionWordType := swm.GetDominantWordType(questionLower)
	faqWordType := swm.GetDominantWordType(faqQuestionLower + " " + faqAnswerLower)

	// 如果问题主要是疑问词，而FAQ是技术性的，直接拒绝
	if questionWordType == "question_heavy" && faqWordType == "technical" {
		log.Printf("🚫 词汇类型不匹配: 问题(%s) vs FAQ(%s)", questionWordType, faqWordType)
		return false
	}

	// 检查技术词汇匹配
	if !swm.HasSignificantTechnicalWords(questionLower) && swm.HasSignificantTechnicalWords(faqQuestionLower+" "+faqAnswerLower) {
		log.Printf("🚫 技术词汇不匹配: 问题无技术词汇，FAQ有技术词汇")
		return false
	}

	return true
}

// testTopicConflict 测试主题冲突
func testTopicConflict(question string, faq mysql.FAQ, swm *nlp.StopWordsManager) bool {
	questionTopic := identifyTopicTest(question, swm)
	faqTopic := identifyTopicTest(faq.Question+" "+faq.Answer, swm)

	// 特殊处理：通用疑问与具体技术主题的冲突
	if questionTopic == "generic_question" && faqTopic != "general" && faqTopic != "generic_question" {
		return true
	}
	if faqTopic == "generic_question" && questionTopic != "general" && questionTopic != "generic_question" {
		return true
	}

	// 定义冲突的主题组
	conflictGroups := [][]string{
		{"go", "python", "java", "javascript", "cpp", "csharp", "rust", "php", "ruby"}, // 编程语言
	}

	for _, group := range conflictGroups {
		found1, found2 := false, false
		for _, item := range group {
			if questionTopic == item {
				found1 = true
			}
			if faqTopic == item {
				found2 = true
			}
		}
		// 如果两个主题在同一组但不相同，则冲突
		if found1 && found2 && questionTopic != faqTopic {
			return true
		}
	}

	return false
}
