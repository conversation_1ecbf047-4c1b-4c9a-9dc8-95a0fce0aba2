package crawler

import (
	"fmt"
	"log"
	"strings"

	"faq-system/internal/learning"
)

// extractKeywordsFromContent 从内容中提取关键词
func (sc *SmartKnowledgeCrawler) extractKeywordsFromContent(content string, topics []TopicInfo) []string {
	keywords := []string{}

	// 从话题中提取关键词
	for _, topic := range topics {
		// 提取话题标题中的关键词
		words := strings.Fields(topic.Title)
		for _, word := range words {
			word = strings.TrimSpace(word)
			if len(word) >= 2 && !sc.isStopWord(word) {
				keywords = append(keywords, word)
			}
		}

		// 添加分类作为关键词
		if topic.Category != "" {
			keywords = append(keywords, topic.Category)
		}
	}

	// 从内容中提取额外关键词
	contentKeywords := sc.extractKeywordsFromText(content)
	keywords = append(keywords, contentKeywords...)

	// 去重并限制数量
	return sc.deduplicateAndLimit(keywords, 10)
}

// extractKeywordsFromText 从文本中提取关键词
func (sc *SmartKnowledgeCrawler) extractKeywordsFromText(text string) []string {
	keywords := []string{}

	// 简单的关键词提取：查找常见的实体词汇
	entityPatterns := []string{
		"小说", "电影", "电视剧", "游戏", "体育", "新闻", "科技",
		"娱乐", "财经", "汽车", "房产", "教育", "健康", "旅游",
		"美食", "时尚", "军事", "历史", "文化", "艺术", "音乐",
	}

	for _, pattern := range entityPatterns {
		if strings.Contains(text, pattern) {
			keywords = append(keywords, pattern)
		}
	}

	return keywords
}

// isStopWord 判断是否为停用词
func (sc *SmartKnowledgeCrawler) isStopWord(word string) bool {
	stopWords := []string{
		"的", "了", "在", "是", "我", "有", "和", "就", "不", "人",
		"都", "一", "一个", "上", "也", "很", "到", "说", "要", "去",
		"你", "会", "着", "没有", "看", "好", "自己", "这", "那", "什么",
		"百度", "搜索", "首页", "登录", "注册", "更多", "展开", "收起",
	}

	for _, stopWord := range stopWords {
		if word == stopWord {
			return true
		}
	}

	return false
}

// deduplicateAndLimit 去重并限制数量
func (sc *SmartKnowledgeCrawler) deduplicateAndLimit(items []string, limit int) []string {
	seen := make(map[string]bool)
	result := []string{}

	for _, item := range items {
		if !seen[item] && len(item) > 0 {
			seen[item] = true
			result = append(result, item)
			if len(result) >= limit {
				break
			}
		}
	}

	return result
}

// evaluateContentQuality 评估内容质量
func (sc *SmartKnowledgeCrawler) evaluateContentQuality(analysis *PageAnalysis) float64 {
	score := 0.0

	// 1. 主要内容数量评分 (0-0.3)
	contentCount := len(analysis.MainContent)
	if contentCount >= 10 {
		score += 0.3
	} else if contentCount >= 5 {
		score += 0.2
	} else if contentCount >= 1 {
		score += 0.1
	}

	// 2. 话题数量评分 (0-0.3)
	topicCount := len(analysis.Topics)
	if topicCount >= 10 {
		score += 0.3
	} else if topicCount >= 5 {
		score += 0.2
	} else if topicCount >= 1 {
		score += 0.1
	}

	// 3. 关键词丰富度评分 (0-0.2)
	keywordCount := len(analysis.Keywords)
	if keywordCount >= 8 {
		score += 0.2
	} else if keywordCount >= 4 {
		score += 0.1
	} else if keywordCount >= 1 {
		score += 0.05
	}

	// 4. 页面类型特定评分 (0-0.2)
	switch analysis.PageType {
	case "realtime", "novel", "movie", "teleplay":
		score += 0.2 // 专门的分类页面质量更高
	case "homepage":
		score += 0.1 // 首页内容相对通用
	default:
		score += 0.05 // 通用页面
	}

	return score
}

// determineContentType 确定内容类型
func (sc *SmartKnowledgeCrawler) determineContentType(analysis *PageAnalysis) string {
	if len(analysis.Topics) > 0 {
		return "list" // 列表类型
	}

	if len(analysis.MainContent) > 5 {
		return "article" // 文章类型
	}

	return "general" // 通用类型
}

// generateKnowledgeFromAnalysis 根据页面分析结果生成知识
func (sc *SmartKnowledgeCrawler) generateKnowledgeFromAnalysis(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	log.Printf("   🎯 开始生成知识")
	log.Printf("     页面类型: %s, 质量评分: %.2f", analysis.PageType, analysis.Quality)
	log.Printf("     话题数量: %d, 主要内容数量: %d", len(analysis.Topics), len(analysis.MainContent))

	if analysis.Quality < 0.3 {
		log.Printf("     ❌ 质量评分过低 (%.2f < 0.3)，不生成知识", analysis.Quality)
		return nil // 质量太低，不生成知识
	}

	// 根据页面类型生成不同的问题和答案
	var knowledge *learning.LearnedKnowledge
	switch analysis.PageType {
	case "realtime":
		log.Printf("     🔥 生成实时热点知识")
		knowledge = sc.generateRealtimeKnowledge(analysis, result, target)
	case "novel":
		log.Printf("     📚 生成小说知识")
		knowledge = sc.generateNovelKnowledge(analysis, result, target)
	case "movie":
		log.Printf("     🎬 生成电影知识")
		knowledge = sc.generateMovieKnowledge(analysis, result, target)
	case "teleplay":
		log.Printf("     📺 生成电视剧知识")
		knowledge = sc.generateTeleplayKnowledge(analysis, result, target)
	case "homepage":
		log.Printf("     🏠 生成首页知识")
		knowledge = sc.generateHomepageKnowledge(analysis, result, target)
	default:
		log.Printf("     📄 生成通用知识")
		knowledge = sc.generateGeneralKnowledge(analysis, result, target)
	}

	if knowledge != nil {
		log.Printf("     ✅ 成功生成知识: %s", knowledge.Question)
	} else {
		log.Printf("     ❌ 未能生成有效知识")
	}

	return knowledge
}

// generateRealtimeKnowledge 生成实时热点知识
func (sc *SmartKnowledgeCrawler) generateRealtimeKnowledge(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	if len(analysis.Topics) == 0 {
		return nil
	}

	// 生成问题
	question := "当前实时热点有哪些？"

	// 生成答案
	topicTitles := []string{}
	for i, topic := range analysis.Topics {
		if i >= 10 { // 最多显示10个
			break
		}

		topicStr := fmt.Sprintf("%d. %s", topic.Rank, topic.Title)
		if topic.HotValue != "" {
			topicStr += fmt.Sprintf(" (热度: %s)", topic.HotValue)
		}
		topicTitles = append(topicTitles, topicStr)
	}

	answer := fmt.Sprintf("根据最新的实时热点榜，当前热门话题包括：\n%s", strings.Join(topicTitles, "\n"))

	return sc.createKnowledge(question, answer, result, target, "realtime_analysis")
}

// generateNovelKnowledge 生成小说知识
func (sc *SmartKnowledgeCrawler) generateNovelKnowledge(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	if len(analysis.Topics) == 0 {
		return nil
	}

	question := "当前热门小说有哪些？"

	novelTitles := []string{}
	for i, topic := range analysis.Topics {
		if i >= 10 {
			break
		}
		novelTitles = append(novelTitles, fmt.Sprintf("%d. %s", topic.Rank, topic.Title))
	}

	answer := fmt.Sprintf("根据最新的小说榜单，当前热门小说包括：\n%s", strings.Join(novelTitles, "\n"))

	return sc.createKnowledge(question, answer, result, target, "novel_analysis")
}

// generateMovieKnowledge 生成电影知识
func (sc *SmartKnowledgeCrawler) generateMovieKnowledge(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	if len(analysis.Topics) == 0 {
		return nil
	}

	question := "当前热门电影有哪些？"

	movieTitles := []string{}
	for i, topic := range analysis.Topics {
		if i >= 10 {
			break
		}
		movieTitles = append(movieTitles, fmt.Sprintf("%d. %s", topic.Rank, topic.Title))
	}

	answer := fmt.Sprintf("根据最新的电影榜单，当前热门电影包括：\n%s", strings.Join(movieTitles, "\n"))

	return sc.createKnowledge(question, answer, result, target, "movie_analysis")
}

// generateTeleplayKnowledge 生成电视剧知识
func (sc *SmartKnowledgeCrawler) generateTeleplayKnowledge(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	if len(analysis.Topics) == 0 {
		return nil
	}

	question := "当前热门电视剧有哪些？"

	teleplayTitles := []string{}
	for i, topic := range analysis.Topics {
		if i >= 10 {
			break
		}
		teleplayTitles = append(teleplayTitles, fmt.Sprintf("%d. %s", topic.Rank, topic.Title))
	}

	answer := fmt.Sprintf("根据最新的电视剧榜单，当前热门电视剧包括：\n%s", strings.Join(teleplayTitles, "\n"))

	return sc.createKnowledge(question, answer, result, target, "teleplay_analysis")
}

// generateHomepageKnowledge 生成首页知识
func (sc *SmartKnowledgeCrawler) generateHomepageKnowledge(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	if len(analysis.Topics) == 0 {
		return nil
	}

	question := "当前综合热搜榜上有哪些热门话题？"

	topicTitles := []string{}
	for i, topic := range analysis.Topics {
		if i >= 15 { // 首页显示更多内容
			break
		}

		topicStr := fmt.Sprintf("%d. %s", topic.Rank, topic.Title)
		if topic.Category != "" && topic.Category != "热门话题" {
			topicStr += fmt.Sprintf(" [%s]", topic.Category)
		}
		if topic.HotValue != "" {
			topicStr += fmt.Sprintf(" (热度: %s)", topic.HotValue)
		}
		topicTitles = append(topicTitles, topicStr)
	}

	answer := fmt.Sprintf("根据最新的综合热搜榜，当前热门话题包括：\n%s", strings.Join(topicTitles, "\n"))

	return sc.createKnowledge(question, answer, result, target, "homepage_analysis")
}

// generateGeneralKnowledge 生成通用知识
func (sc *SmartKnowledgeCrawler) generateGeneralKnowledge(analysis *PageAnalysis, result *PageInfo, target *SmartCrawlTarget) *learning.LearnedKnowledge {
	if len(analysis.MainContent) == 0 {
		return nil
	}

	// 基于主要内容生成问题
	question := fmt.Sprintf("关于%s有什么信息？", analysis.Category)

	// 生成答案
	answer := strings.Join(analysis.MainContent[:min(5, len(analysis.MainContent))], "\n")

	if len(answer) < 20 {
		return nil
	}

	return sc.createKnowledge(question, answer, result, target, "general_analysis")
}
