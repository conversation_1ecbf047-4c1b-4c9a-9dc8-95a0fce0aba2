package recommendation

import (
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"sort"
	"strings"
)

// SemanticBasedStrategy 基于语义的推荐策略
type SemanticBasedStrategy struct {
	knowledgeLearner *learning.KnowledgeLearner
	config           *RecommendationConfig
}

// NewSemanticBasedStrategy 创建基于语义的推荐策略
func NewSemanticBasedStrategy(knowledgeLearner *learning.KnowledgeLearner, config *RecommendationConfig) *SemanticBasedStrategy {
	return &SemanticBasedStrategy{
		knowledgeLearner: knowledgeLearner,
		config:           config,
	}
}

// GetStrategyName 获取策略名称
func (s *SemanticBasedStrategy) GetStrategyName() string {
	return "semantic_based"
}

// IsApplicable 判断策略是否适用
func (s *SemanticBasedStrategy) IsApplicable(ctx *RecommendationContext) bool {
	// 需要有问题文本进行语义分析
	return ctx.Question != ""
}

// GetRecommendations 获取推荐
func (s *SemanticBasedStrategy) GetRecommendations(ctx *RecommendationContext) (*RecommendationResult, error) {
	logger.Infof("🧠 基于语义推荐: 问题='%s'", ctx.Question)

	// 使用知识搜索功能进行语义相似度搜索
	// 这里可以调用现有的搜索功能，但需要排除当前知识
	searchResults, err := s.performSemanticSearch(ctx.Question, ctx.KnowledgeID)
	if err != nil {
		logger.Warnf("❌ 语义搜索失败: %v", err)
		return nil, err
	}

	if len(searchResults) == 0 {
		logger.Infof("❌ 语义搜索没有找到相关知识")
		return &RecommendationResult{
			Success:            false,
			RecommendationType: s.GetStrategyName(),
			KnowledgeTopic:     ctx.KnowledgeTopic,
			Items:              []*RecommendationItem{},
			FormattedText:      "",
			Metadata: map[string]interface{}{
				"reason": "no_semantic_matches",
				"query":  ctx.Question,
			},
		}, nil
	}

	// 转换为推荐项目并计算相关性分数
	items := make([]*RecommendationItem, 0, len(searchResults))
	for i, knowledge := range searchResults {
		// 过滤低置信度的知识
		if float64(knowledge.Confidence) < s.config.MinConfidence {
			logger.Infof("⏭️ 跳过低置信度知识: ID=%d, 置信度=%.2f", knowledge.ID, knowledge.Confidence)
			continue
		}

		// 排除当前知识
		if knowledge.ID == ctx.KnowledgeID {
			continue
		}

		// 计算相关性分数（基于搜索排序）
		relevance := 1.0 - (float64(i) / float64(len(searchResults)))

		item := &RecommendationItem{
			ID:         knowledge.ID,
			Question:   knowledge.Question,
			Answer:     knowledge.Answer,
			Confidence: float64(knowledge.Confidence),
			Relevance:  relevance,
			Topic:      knowledge.KnowledgeTopic,
			Source:     knowledge.Source,
			CreatedAt:  knowledge.CreatedAt,
			Knowledge:  knowledge,
		}
		items = append(items, item)

		// 限制推荐数量
		if len(items) >= s.config.MaxRecommendations {
			break
		}
	}

	if len(items) == 0 {
		logger.Infof("❌ 过滤后没有符合条件的知识")
		return &RecommendationResult{
			Success:            false,
			RecommendationType: s.GetStrategyName(),
			KnowledgeTopic:     ctx.KnowledgeTopic,
			Items:              []*RecommendationItem{},
			FormattedText:      "",
			Metadata: map[string]interface{}{
				"reason":         "low_confidence_filtered",
				"query":          ctx.Question,
				"min_confidence": s.config.MinConfidence,
			},
		}, nil
	}

	// 按相关性分数排序
	sort.Slice(items, func(i, j int) bool {
		return items[i].Relevance > items[j].Relevance
	})

	logger.Infof("✅ 基于语义推荐成功: 找到 %d 个相关知识", len(items))

	// 确定知识点标志（使用最相关的知识的标志）
	knowledgeTopic := ctx.KnowledgeTopic
	if knowledgeTopic == "" && len(items) > 0 {
		knowledgeTopic = items[0].Topic
	}

	return &RecommendationResult{
		Success:            true,
		RecommendationType: s.GetStrategyName(),
		KnowledgeTopic:     knowledgeTopic,
		Items:              items,
		FormattedText:      "", // 将由服务层格式化
		Metadata: map[string]interface{}{
			"strategy":       s.GetStrategyName(),
			"query":          ctx.Question,
			"total_found":    len(searchResults),
			"after_filter":   len(items),
			"min_confidence": s.config.MinConfidence,
		},
	}, nil
}

// performSemanticSearch 执行语义搜索
func (s *SemanticBasedStrategy) performSemanticSearch(query string, excludeID int) ([]*learning.LearnedKnowledge, error) {
	// 这里可以调用现有的知识搜索功能
	// 为了简化，我们先使用简单的文本搜索
	// 在实际实现中，可以集成向量搜索或NLP语义分析

	// 获取最近的知识（包含已批准的）
	allKnowledge, err := s.knowledgeLearner.GetRecentKnowledge(s.config.MaxRecommendations * 5) // 获取更多以便过滤
	if err != nil {
		return nil, err
	}

	// 简单的关键词匹配（后续可以改进为语义搜索）
	var results []*learning.LearnedKnowledge
	queryLower := strings.ToLower(query)

	for _, knowledge := range allKnowledge {
		// 只处理已批准的知识
		if knowledge.Status != "approved" {
			continue
		}

		// 排除当前知识
		if knowledge.ID == excludeID {
			continue
		}

		// 简单的文本匹配
		questionLower := strings.ToLower(knowledge.Question)
		answerLower := strings.ToLower(knowledge.Answer)

		// 检查是否包含相关关键词
		if s.containsRelevantKeywords(queryLower, questionLower, answerLower) {
			results = append(results, knowledge)
		}
	}

	return results, nil
}

// containsRelevantKeywords 检查是否包含相关关键词
func (s *SemanticBasedStrategy) containsRelevantKeywords(query, question, answer string) bool {
	// 简单的关键词匹配逻辑
	// 后续可以改进为更复杂的语义分析

	// 提取查询中的关键词（简化版本）
	queryWords := strings.Fields(query)

	for _, word := range queryWords {
		if len(word) > 1 { // 忽略单字符
			if strings.Contains(question, word) || strings.Contains(answer, word) {
				return true
			}
		}
	}

	return false
}
