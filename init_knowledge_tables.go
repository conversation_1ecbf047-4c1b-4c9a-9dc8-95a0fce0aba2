package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 数据库连接
	dsn := "root:root123@tcp(localhost:3306)/faq_system?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatalf("连接数据库失败: %v", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.<PERSON>(); err != nil {
		log.Fatalf("数据库连接测试失败: %v", err)
	}

	fmt.Println("🔗 数据库连接成功")

	// 创建learned_knowledge表
	createLearnedKnowledgeTable := `
	CREATE TABLE IF NOT EXISTS learned_knowledge (
		id INT AUTO_INCREMENT PRIMARY KEY,
		question TEXT NOT NULL COMMENT '学习到的问题',
		answer TEXT NOT NULL COMMENT '学习到的答案',
		source VARCHAR(100) NOT NULL DEFAULT 'conversation' COMMENT '知识来源',
		confidence FLOAT NOT NULL DEFAULT 0.0 COMMENT '知识置信度',
		category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
		keywords JSON COMMENT '关键词列表',
		context TEXT COMMENT '学习上下文',
		learned_from VARCHAR(255) COMMENT '从哪个用户或系统学习',
		status ENUM('pending', 'approved', 'rejected') DEFAULT 'pending' COMMENT '知识状态',
		metadata JSON COMMENT '元数据信息',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		
		INDEX idx_source (source),
		INDEX idx_confidence (confidence),
		INDEX idx_category (category),
		INDEX idx_status (status),
		INDEX idx_created_at (created_at),
		INDEX idx_learned_from (learned_from),
		FULLTEXT idx_question (question),
		FULLTEXT idx_answer (answer)
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='学习知识表'`

	if _, err := db.Exec(createLearnedKnowledgeTable); err != nil {
		log.Fatalf("创建learned_knowledge表失败: %v", err)
	}
	fmt.Println("✅ learned_knowledge表创建成功")

	// 创建knowledge_vectors表
	createKnowledgeVectorsTable := `
	CREATE TABLE IF NOT EXISTS knowledge_vectors (
		id INT AUTO_INCREMENT PRIMARY KEY,
		knowledge_id INT NOT NULL COMMENT '关联的知识ID',
		vector_data JSON NOT NULL COMMENT '向量数据',
		vector_dimension INT DEFAULT 128 COMMENT '向量维度',
		created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
		updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
		
		UNIQUE KEY unique_knowledge (knowledge_id),
		INDEX idx_created_at (created_at),
		FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
	) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识向量表'`

	if _, err := db.Exec(createKnowledgeVectorsTable); err != nil {
		log.Fatalf("创建knowledge_vectors表失败: %v", err)
	}
	fmt.Println("✅ knowledge_vectors表创建成功")

	// 更新user_feedback表的feedback_type枚举
	updateUserFeedbackTable := `
	ALTER TABLE user_feedback 
	MODIFY COLUMN feedback_type ENUM('helpful', 'not_helpful', 'partially_helpful', 'positive', 'negative', 'correction') NOT NULL`

	if _, err := db.Exec(updateUserFeedbackTable); err != nil {
		fmt.Printf("⚠️ 更新user_feedback表失败 (可能已存在): %v\n", err)
	} else {
		fmt.Println("✅ user_feedback表更新成功")
	}

	// 为system_responses表添加response_content字段
	addResponseContentColumn := `
	ALTER TABLE system_responses 
	ADD COLUMN IF NOT EXISTS response_content TEXT COMMENT '响应内容' AFTER response_text`

	if _, err := db.Exec(addResponseContentColumn); err != nil {
		fmt.Printf("⚠️ 添加response_content字段失败 (可能已存在): %v\n", err)
	} else {
		fmt.Println("✅ response_content字段添加成功")
	}

	// 复制response_text到response_content
	copyResponseContent := `
	UPDATE system_responses 
	SET response_content = response_text 
	WHERE response_content IS NULL OR response_content = ''`

	if _, err := db.Exec(copyResponseContent); err != nil {
		fmt.Printf("⚠️ 复制响应内容失败: %v\n", err)
	} else {
		fmt.Println("✅ 响应内容复制成功")
	}

	// 插入学习配置
	insertLearningConfig := `
	INSERT IGNORE INTO learning_config (config_key, config_value, config_type, description) VALUES
	('knowledge_learning_enabled', 'true', 'boolean', '是否启用知识学习功能'),
	('min_knowledge_confidence', '0.3', 'number', '知识学习最小置信度'),
	('max_knowledge_per_session', '50', 'number', '每次学习会话最大知识数量'),
	('knowledge_auto_approve_threshold', '0.8', 'number', '知识自动批准阈值'),
	('vector_generation_enabled', 'true', 'boolean', '是否启用向量生成'),
	('vector_dimension', '128', 'number', '向量维度'),
	('knowledge_deduplication_threshold', '0.85', 'number', '知识去重相似度阈值'),
	('content_safety_check_enabled', 'true', 'boolean', '是否启用内容安全检查')`

	if _, err := db.Exec(insertLearningConfig); err != nil {
		fmt.Printf("⚠️ 插入学习配置失败: %v\n", err)
	} else {
		fmt.Println("✅ 学习配置插入成功")
	}

	// 检查表是否创建成功
	var tableCount int
	checkTablesQuery := `
	SELECT COUNT(*) 
	FROM INFORMATION_SCHEMA.TABLES 
	WHERE TABLE_SCHEMA = 'faq_system' 
	AND TABLE_NAME IN ('learned_knowledge', 'knowledge_vectors')`

	if err := db.QueryRow(checkTablesQuery).Scan(&tableCount); err != nil {
		log.Fatalf("检查表创建状态失败: %v", err)
	}

	if tableCount == 2 {
		fmt.Println("🎉 所有知识学习表创建成功！")
		
		// 显示表结构
		fmt.Println("\n📋 表结构信息:")
		showTableInfo(db, "learned_knowledge")
		showTableInfo(db, "knowledge_vectors")
	} else {
		fmt.Printf("⚠️ 只创建了 %d/2 个表\n", tableCount)
	}
}

func showTableInfo(db *sql.DB, tableName string) {
	query := `
	SELECT 
		TABLE_NAME,
		TABLE_COMMENT,
		TABLE_ROWS
	FROM INFORMATION_SCHEMA.TABLES 
	WHERE TABLE_SCHEMA = 'faq_system' 
	AND TABLE_NAME = ?`

	var name, comment string
	var rows sql.NullInt64

	if err := db.QueryRow(query, tableName).Scan(&name, &comment, &rows); err != nil {
		fmt.Printf("❌ 获取表 %s 信息失败: %v\n", tableName, err)
		return
	}

	rowCount := int64(0)
	if rows.Valid {
		rowCount = rows.Int64
	}

	fmt.Printf("  📊 %s: %s (行数: %d)\n", name, comment, rowCount)
}
