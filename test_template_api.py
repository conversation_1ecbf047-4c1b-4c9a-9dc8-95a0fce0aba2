#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据模板功能API测试脚本
"""

import requests
import json
import time

# API配置
BASE_URL = "http://localhost:8080"
ASK_URL = f"{BASE_URL}/ask"

def test_data_template():
    """测试数据模板功能"""
    print("🧪 开始测试数据模板功能...")
    
    # 测试用例
    test_cases = [
        {
            "name": "车场配置测试",
            "query": "我想配置车场",
            "expected_keywords": ["车场", "配置", "区域"]
        },
        {
            "name": "用户管理测试", 
            "query": "新增用户",
            "expected_keywords": ["用户", "管理", "角色"]
        },
        {
            "name": "设备配置测试",
            "query": "设备配置",
            "expected_keywords": ["设备", "配置", "类型"]
        },
        {
            "name": "无关请求测试",
            "query": "今天天气怎么样",
            "expected_keywords": []
        }
    ]
    
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n=== 测试 {i}: {test_case['name']} ===")
        print(f"查询: {test_case['query']}")
        
        try:
            # 发送请求
            response = requests.post(ASK_URL, json={
                "query": test_case["query"]
            }, timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                print(f"✅ 请求成功")
                print(f"回答: {data.get('answer', 'N/A')}")
                print(f"意图: {data.get('intent', 'N/A')}")
                print(f"置信度: {data.get('confidence', 'N/A')}")
                print(f"来源: {data.get('source', 'N/A')}")
                
                # 检查是否包含期望的关键词
                answer = data.get('answer', '').lower()
                found_keywords = []
                for keyword in test_case['expected_keywords']:
                    if keyword.lower() in answer:
                        found_keywords.append(keyword)
                
                if test_case['expected_keywords']:
                    if found_keywords:
                        print(f"✅ 找到期望关键词: {found_keywords}")
                    else:
                        print(f"⚠️ 未找到期望关键词: {test_case['expected_keywords']}")
                else:
                    print("ℹ️ 无关请求测试，不检查关键词")
                    
            else:
                print(f"❌ 请求失败: {response.status_code}")
                print(f"错误信息: {response.text}")
                
        except requests.exceptions.RequestException as e:
            print(f"❌ 网络错误: {e}")
        except Exception as e:
            print(f"❌ 其他错误: {e}")
        
        # 等待一下避免请求过快
        time.sleep(1)

def check_server_health():
    """检查服务器健康状态"""
    try:
        response = requests.get(f"{BASE_URL}/health", timeout=5)
        if response.status_code == 200:
            print("✅ 服务器运行正常")
            return True
        else:
            print(f"⚠️ 服务器状态异常: {response.status_code}")
            return False
    except Exception as e:
        print(f"❌ 无法连接到服务器: {e}")
        return False

if __name__ == "__main__":
    print("🚀 数据模板功能测试开始")
    print("=" * 50)
    
    # 检查服务器状态
    if not check_server_health():
        print("❌ 服务器未运行，请先启动FAQ系统")
        print("💡 启动命令: go run main.go")
        exit(1)
    
    # 执行测试
    test_data_template()
    
    print("\n" + "=" * 50)
    print("✅ 数据模板功能测试完成")
    print("\n💡 如需测试完整对话流程，请手动在Web界面中测试：")
    print("   1. 输入：我想配置车场")
    print("   2. 按提示逐步输入车场信息")
    print("   3. 观察系统生成的SQL语句")
