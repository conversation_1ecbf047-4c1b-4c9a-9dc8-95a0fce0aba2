package template

import (
	"faq-system/internal/logger"
	"faq-system/internal/nlp"
	"fmt"
	"strings"
	"time"

	_ "github.com/go-sql-driver/mysql"
)

// DataTemplateSystem 数据模板系统
type DataTemplateSystem struct {
	nlpProcessor *nlp.IntegratedProcessor
	templates    map[string]*DataTemplate
	sessions     map[string]*TemplateSession // 内存中的会话存储
}

// DataTemplate 数据模板定义
type DataTemplate struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Keywords    []string               `json:"keywords"`
	Fields      []TemplateField        `json:"fields"`
	SQLTemplate string                 `json:"sql_template"`
	DBConfig    DatabaseConfig         `json:"db_config"`
	Validation  map[string]interface{} `json:"validation"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
}

// TemplateField 模板字段定义
type TemplateField struct {
	Name         string      `json:"name"`
	DisplayName  string      `json:"display_name"`
	Type         string      `json:"type"` // string, int, float, bool, select, multi_select
	Required     bool        `json:"required"`
	Description  string      `json:"description"`
	Options      []string    `json:"options,omitempty"` // 用于select类型
	DefaultValue interface{} `json:"default_value,omitempty"`
	Validation   string      `json:"validation,omitempty"` // 验证规则
	Prompt       string      `json:"prompt"`               // 用于引导用户输入的提示
}

// DatabaseConfig 数据库配置
type DatabaseConfig struct {
	Host     string `json:"host"`
	Port     string `json:"port"`
	Database string `json:"database"`
	Username string `json:"username"`
	Password string `json:"password"`
}

// TemplateSession 模板会话
type TemplateSession struct {
	ID            string                 `json:"id"`
	TemplateID    string                 `json:"template_id"`
	UserID        string                 `json:"user_id"`
	CurrentField  int                    `json:"current_field"`
	CollectedData map[string]interface{} `json:"collected_data"`
	Status        string                 `json:"status"` // collecting, completed, cancelled
	CreatedAt     time.Time              `json:"created_at"`
	UpdatedAt     time.Time              `json:"updated_at"`
}

// TemplateMatchResult 模板匹配结果
type TemplateMatchResult struct {
	Template   *DataTemplate `json:"template"`
	Confidence float64       `json:"confidence"`
	Reason     string        `json:"reason"`
}

// TemplateResponse 模板响应
type TemplateResponse struct {
	Type       string           `json:"type"` // match, collect, execute, complete, error
	Message    string           `json:"message"`
	Template   *DataTemplate    `json:"template,omitempty"`
	Session    *TemplateSession `json:"session,omitempty"`
	NextPrompt string           `json:"next_prompt,omitempty"`
	Options    []string         `json:"options,omitempty"`
	SQLResult  interface{}      `json:"sql_result,omitempty"`
	Error      string           `json:"error,omitempty"`
}

// NewDataTemplateSystem 创建数据模板系统
func NewDataTemplateSystem(nlpProcessor *nlp.IntegratedProcessor) *DataTemplateSystem {
	system := &DataTemplateSystem{
		nlpProcessor: nlpProcessor,
		templates:    make(map[string]*DataTemplate),
		sessions:     make(map[string]*TemplateSession),
	}

	// 初始化内置模板
	system.initializeBuiltinTemplates()

	return system
}

// initializeBuiltinTemplates 初始化内置模板
func (dts *DataTemplateSystem) initializeBuiltinTemplates() {
	logger.Info("🏗️ 初始化数据模板系统...")

	// 车场配置模板
	parkingTemplate := &DataTemplate{
		ID:          "parking_config",
		Name:        "车场配置",
		Description: "用于配置停车场的区域、车道和相机信息",
		Category:    "parking",
		Keywords:    []string{"车场", "停车场", "配置", "区域", "车道", "相机", "内场", "外场"},
		Fields: []TemplateField{
			{
				Name:        "parking_name",
				DisplayName: "车场名称",
				Type:        "string",
				Required:    true,
				Description: "停车场的名称",
				Prompt:      "请输入车场名称（例如：万达广场停车场）",
			},
			{
				Name:        "area_type",
				DisplayName: "区域类型",
				Type:        "select",
				Required:    true,
				Description: "车场是否有内外场区域",
				Options:     []string{"仅外场", "内外场都有"},
				Prompt:      "请选择车场区域类型：1) 仅外场  2) 内外场都有",
			},
			{
				Name:        "area_count",
				DisplayName: "区域数量",
				Type:        "int",
				Required:    true,
				Description: "车场总共有多少个区域",
				Prompt:      "请输入车场总共有多少个区域（数字）",
				Validation:  "min:1,max:50",
			},
			{
				Name:        "lanes_per_area",
				DisplayName: "每个区域的车道数",
				Type:        "string",
				Required:    true,
				Description: "每个区域分别有多少个车道（用逗号分隔）",
				Prompt:      "请输入每个区域的车道数，用逗号分隔（例如：10,8,12）",
			},
			{
				Name:         "camera_per_lane",
				DisplayName:  "每车道相机数",
				Type:         "int",
				Required:     false,
				Description:  "每个车道配置的相机数量",
				DefaultValue: 1,
				Prompt:       "请输入每个车道的相机数量（默认为1）",
			},
			{
				Name:        "additional_info",
				DisplayName: "附加信息",
				Type:        "string",
				Required:    false,
				Description: "其他需要记录的信息",
				Prompt:      "是否有其他需要记录的信息？（可选）",
			},
		},
		SQLTemplate: `
INSERT INTO parking_config (name, area_type, total_areas, area_details, created_at) 
VALUES (?, ?, ?, ?, NOW());

INSERT INTO parking_areas (parking_id, area_name, area_type, lane_count, created_at)
VALUES %s;

INSERT INTO parking_lanes (area_id, lane_number, camera_count, created_at)
VALUES %s;
		`,
		DBConfig: DatabaseConfig{
			Host:     "localhost",
			Port:     "3306",
			Database: "parking_system",
			Username: "root",
			Password: "",
		},
	}

	// 用户管理模板
	userTemplate := &DataTemplate{
		ID:          "user_management",
		Name:        "用户管理",
		Description: "用于创建和管理系统用户信息",
		Category:    "user",
		Keywords:    []string{"用户", "账号", "权限", "角色", "管理员", "员工"},
		Fields: []TemplateField{
			{
				Name:        "username",
				DisplayName: "用户名",
				Type:        "string",
				Required:    true,
				Description: "用户登录名",
				Prompt:      "请输入用户名",
				Validation:  "min:3,max:20,pattern:^[a-zA-Z0-9_]+$",
			},
			{
				Name:        "real_name",
				DisplayName: "真实姓名",
				Type:        "string",
				Required:    true,
				Description: "用户的真实姓名",
				Prompt:      "请输入用户的真实姓名",
			},
			{
				Name:        "email",
				DisplayName: "邮箱",
				Type:        "string",
				Required:    true,
				Description: "用户邮箱地址",
				Prompt:      "请输入用户邮箱地址",
				Validation:  "email",
			},
			{
				Name:        "role",
				DisplayName: "用户角色",
				Type:        "select",
				Required:    true,
				Description: "用户在系统中的角色",
				Options:     []string{"管理员", "操作员", "查看者", "审核员"},
				Prompt:      "请选择用户角色：1) 管理员  2) 操作员  3) 查看者  4) 审核员",
			},
			{
				Name:        "department",
				DisplayName: "部门",
				Type:        "string",
				Required:    false,
				Description: "用户所属部门",
				Prompt:      "请输入用户所属部门（可选）",
			},
		},
		SQLTemplate: `
INSERT INTO users (username, real_name, email, role, department, created_at, status) 
VALUES (?, ?, ?, ?, ?, NOW(), 'active');
		`,
	}

	// 设备配置模板
	deviceTemplate := &DataTemplate{
		ID:          "device_config",
		Name:        "设备配置",
		Description: "用于配置各种设备信息",
		Category:    "device",
		Keywords:    []string{"设备", "配置", "硬件", "传感器", "摄像头", "服务器"},
		Fields: []TemplateField{
			{
				Name:        "device_name",
				DisplayName: "设备名称",
				Type:        "string",
				Required:    true,
				Description: "设备的名称或型号",
				Prompt:      "请输入设备名称或型号",
			},
			{
				Name:        "device_type",
				DisplayName: "设备类型",
				Type:        "select",
				Required:    true,
				Description: "设备的类型分类",
				Options:     []string{"摄像头", "传感器", "服务器", "网络设备", "存储设备", "其他"},
				Prompt:      "请选择设备类型：1) 摄像头  2) 传感器  3) 服务器  4) 网络设备  5) 存储设备  6) 其他",
			},
			{
				Name:        "location",
				DisplayName: "安装位置",
				Type:        "string",
				Required:    true,
				Description: "设备的安装位置",
				Prompt:      "请输入设备的安装位置",
			},
			{
				Name:        "ip_address",
				DisplayName: "IP地址",
				Type:        "string",
				Required:    false,
				Description: "设备的IP地址（如果适用）",
				Prompt:      "请输入设备的IP地址（可选）",
				Validation:  "ip",
			},
		},
		SQLTemplate: `
INSERT INTO devices (name, type, location, ip_address, created_at, status) 
VALUES (?, ?, ?, ?, NOW(), 'active');
		`,
	}

	// 注册模板
	dts.templates[parkingTemplate.ID] = parkingTemplate
	dts.templates[userTemplate.ID] = userTemplate
	dts.templates[deviceTemplate.ID] = deviceTemplate

	logger.Info("✅ 数据模板系统初始化完成，加载了 %d 个模板", len(dts.templates))
}

// ProcessInput 处理用户输入，识别模板意图并执行相应操作
func (dts *DataTemplateSystem) ProcessInput(userID, input string, sessionID string) *TemplateResponse {
	logger.Info("🔍 处理数据模板输入: %s", input)

	// 1. 检查是否有活跃的会话
	if sessionID != "" {
		if session := dts.getActiveSession(sessionID); session != nil {
			return dts.continueSession(session, input)
		}
	}

	// 2. 尝试匹配模板
	matches := dts.matchTemplates(input)
	if len(matches) == 0 {
		return &TemplateResponse{
			Type:    "error",
			Message: "未找到匹配的数据模板。请尝试更具体的描述，例如：'我想配置车场'、'新增用户'、'设备配置'等。",
		}
	}

	// 3. 选择最佳匹配
	bestMatch := matches[0]
	if bestMatch.Confidence < 0.6 {
		return &TemplateResponse{
			Type:    "error",
			Message: "模板匹配置信度较低，请提供更明确的需求描述。",
		}
	}

	// 4. 创建新的会话
	session := dts.createSession(userID, bestMatch.Template.ID)

	// 5. 开始数据收集
	return dts.startDataCollection(session)
}

// matchTemplates 匹配数据模板
func (dts *DataTemplateSystem) matchTemplates(input string) []TemplateMatchResult {
	var results []TemplateMatchResult
	inputLower := strings.ToLower(input)

	for _, template := range dts.templates {
		confidence := dts.calculateTemplateConfidence(inputLower, template)
		if confidence > 0.6 { // 🛡️ 提高模板匹配阈值
			results = append(results, TemplateMatchResult{
				Template:   template,
				Confidence: confidence,
				Reason:     fmt.Sprintf("关键词匹配度: %.2f", confidence),
			})
		}
	}

	// 按置信度排序
	for i := 0; i < len(results)-1; i++ {
		for j := i + 1; j < len(results); j++ {
			if results[i].Confidence < results[j].Confidence {
				results[i], results[j] = results[j], results[i]
			}
		}
	}

	return results
}

// calculateTemplateConfidence 计算模板匹配置信度
func (dts *DataTemplateSystem) calculateTemplateConfidence(input string, template *DataTemplate) float64 {
	var score float64
	keywordCount := 0

	// 检查关键词匹配
	for _, keyword := range template.Keywords {
		if strings.Contains(input, strings.ToLower(keyword)) {
			score += 1.0
			keywordCount++
		}
	}

	// 检查模板名称匹配
	if strings.Contains(input, strings.ToLower(template.Name)) {
		score += 2.0
	}

	// 检查描述匹配
	descWords := strings.Fields(strings.ToLower(template.Description))
	for _, word := range descWords {
		if len(word) > 2 && strings.Contains(input, word) {
			score += 0.5
		}
	}

	// 使用NLP处理器进行语义匹配（如果可用）
	if dts.nlpProcessor != nil && dts.nlpProcessor.IsInitialized() {
		nlpResult := dts.nlpProcessor.ProcessText(input)
		if nlpResult.Intent != nil {
			// 检查意图匹配
			intentKeywords := []string{"配置", "新增", "创建", "设置", "管理"}
			for _, keyword := range intentKeywords {
				if strings.Contains(strings.ToLower(nlpResult.Intent.Intent), keyword) {
					score += 0.8
					break
				}
			}
		}

		// 检查关键词匹配
		for _, nlpKeyword := range nlpResult.Keywords {
			for _, templateKeyword := range template.Keywords {
				if strings.Contains(strings.ToLower(nlpKeyword), strings.ToLower(templateKeyword)) ||
					strings.Contains(strings.ToLower(templateKeyword), strings.ToLower(nlpKeyword)) {
					score += 0.6
				}
			}
		}
	}

	// 归一化分数
	maxScore := float64(len(template.Keywords)) + 2.0 + 3.0 // 关键词 + 名称 + NLP加分
	if maxScore > 0 {
		return score / maxScore
	}

	return 0.0
}

// createSession 创建新的模板会话
func (dts *DataTemplateSystem) createSession(userID, templateID string) *TemplateSession {
	sessionID := fmt.Sprintf("tpl_%d_%s", time.Now().Unix(), userID)

	session := &TemplateSession{
		ID:            sessionID,
		TemplateID:    templateID,
		UserID:        userID,
		CurrentField:  0,
		CollectedData: make(map[string]interface{}),
		Status:        "collecting",
		CreatedAt:     time.Now(),
		UpdatedAt:     time.Now(),
	}

	// 这里应该保存到数据库或内存存储
	// 为了简化，我们暂时使用内存存储
	dts.saveSession(session)

	return session
}

// startDataCollection 开始数据收集
func (dts *DataTemplateSystem) startDataCollection(session *TemplateSession) *TemplateResponse {
	template := dts.templates[session.TemplateID]
	if template == nil {
		return &TemplateResponse{
			Type:    "error",
			Message: "模板不存在",
		}
	}

	if session.CurrentField >= len(template.Fields) {
		return dts.executeTemplate(session)
	}

	currentField := template.Fields[session.CurrentField]

	response := &TemplateResponse{
		Type:       "collect",
		Message:    fmt.Sprintf("开始配置：%s", template.Name),
		Template:   template,
		Session:    session,
		NextPrompt: currentField.Prompt,
	}

	// 如果是选择类型，提供选项
	if currentField.Type == "select" && len(currentField.Options) > 0 {
		response.Options = currentField.Options
	}

	return response
}

// getActiveSession 获取活跃的会话
func (dts *DataTemplateSystem) getActiveSession(sessionID string) *TemplateSession {
	if session, exists := dts.sessions[sessionID]; exists && session.Status == "collecting" {
		return session
	}
	return nil
}

// saveSession 保存会话
func (dts *DataTemplateSystem) saveSession(session *TemplateSession) {
	dts.sessions[session.ID] = session
}

// continueSession 继续会话，处理用户输入的字段值
func (dts *DataTemplateSystem) continueSession(session *TemplateSession, input string) *TemplateResponse {
	template := dts.templates[session.TemplateID]
	if template == nil {
		return &TemplateResponse{
			Type:    "error",
			Message: "模板不存在",
		}
	}

	if session.CurrentField >= len(template.Fields) {
		return dts.executeTemplate(session)
	}

	currentField := template.Fields[session.CurrentField]

	// 验证和处理输入
	value, err := dts.validateAndProcessInput(input, currentField)
	if err != nil {
		return &TemplateResponse{
			Type:       "collect",
			Message:    fmt.Sprintf("输入验证失败: %s", err.Error()),
			Session:    session,
			NextPrompt: fmt.Sprintf("请重新输入%s: %s", currentField.DisplayName, currentField.Prompt),
		}
	}

	// 保存字段值
	session.CollectedData[currentField.Name] = value
	session.CurrentField++
	session.UpdatedAt = time.Now()
	dts.saveSession(session)

	// 检查是否还有更多字段需要收集
	if session.CurrentField >= len(template.Fields) {
		return dts.executeTemplate(session)
	}

	// 继续收集下一个字段
	nextField := template.Fields[session.CurrentField]
	response := &TemplateResponse{
		Type:       "collect",
		Message:    fmt.Sprintf("已记录%s: %v", currentField.DisplayName, value),
		Session:    session,
		NextPrompt: nextField.Prompt,
	}

	// 如果是选择类型，提供选项
	if nextField.Type == "select" && len(nextField.Options) > 0 {
		response.Options = nextField.Options
	}

	return response
}

// validateAndProcessInput 验证和处理输入
func (dts *DataTemplateSystem) validateAndProcessInput(input string, field TemplateField) (interface{}, error) {
	input = strings.TrimSpace(input)

	// 检查必填字段
	if field.Required && input == "" {
		return nil, fmt.Errorf("%s是必填字段", field.DisplayName)
	}

	// 如果是空值且有默认值，使用默认值
	if input == "" && field.DefaultValue != nil {
		return field.DefaultValue, nil
	}

	// 根据字段类型处理
	switch field.Type {
	case "string":
		return input, nil

	case "int":
		if input == "" {
			return 0, nil
		}
		var value int
		if _, err := fmt.Sscanf(input, "%d", &value); err != nil {
			return nil, fmt.Errorf("请输入有效的数字")
		}
		return value, nil

	case "float":
		if input == "" {
			return 0.0, nil
		}
		var value float64
		if _, err := fmt.Sscanf(input, "%f", &value); err != nil {
			return nil, fmt.Errorf("请输入有效的小数")
		}
		return value, nil

	case "bool":
		lower := strings.ToLower(input)
		if lower == "true" || lower == "是" || lower == "1" || lower == "yes" {
			return true, nil
		} else if lower == "false" || lower == "否" || lower == "0" || lower == "no" {
			return false, nil
		}
		return nil, fmt.Errorf("请输入 true/false 或 是/否")

	case "select":
		// 检查是否是有效选项
		for i, option := range field.Options {
			if strings.EqualFold(input, option) || input == fmt.Sprintf("%d", i+1) {
				return option, nil
			}
		}
		return nil, fmt.Errorf("请选择有效选项: %s", strings.Join(field.Options, ", "))

	default:
		return input, nil
	}
}

// executeTemplate 执行模板，生成SQL并执行
func (dts *DataTemplateSystem) executeTemplate(session *TemplateSession) *TemplateResponse {
	template := dts.templates[session.TemplateID]
	if template == nil {
		return &TemplateResponse{
			Type:    "error",
			Message: "模板不存在",
		}
	}

	logger.Info("🚀 执行数据模板: %s", template.Name)

	// 标记会话完成
	session.Status = "completed"
	session.UpdatedAt = time.Now()
	dts.saveSession(session)

	// 生成SQL语句
	sqlStatements, err := dts.generateSQL(template, session.CollectedData)
	if err != nil {
		return &TemplateResponse{
			Type:    "error",
			Message: fmt.Sprintf("生成SQL失败: %s", err.Error()),
			Session: session,
		}
	}

	// 执行SQL（这里为了演示，我们只是返回生成的SQL）
	// 在实际应用中，这里会连接到指定的数据库执行SQL
	result := map[string]interface{}{
		"template_name":    template.Name,
		"collected_data":   session.CollectedData,
		"generated_sql":    sqlStatements,
		"execution_status": "simulated", // 在实际应用中这里会是真实的执行结果
		"message":          "SQL已生成，实际应用中会执行到目标数据库",
	}

	return &TemplateResponse{
		Type:      "complete",
		Message:   fmt.Sprintf("✅ %s配置完成！", template.Name),
		Session:   session,
		SQLResult: result,
	}
}

// generateSQL 根据模板和收集的数据生成SQL语句
func (dts *DataTemplateSystem) generateSQL(template *DataTemplate, data map[string]interface{}) ([]string, error) {
	switch template.ID {
	case "parking_config":
		return dts.generateParkingSQL(data)
	case "user_management":
		return dts.generateUserSQL(data)
	case "device_config":
		return dts.generateDeviceSQL(data)
	default:
		return nil, fmt.Errorf("未知的模板类型: %s", template.ID)
	}
}

// generateParkingSQL 生成车场配置SQL
func (dts *DataTemplateSystem) generateParkingSQL(data map[string]interface{}) ([]string, error) {
	var statements []string

	parkingName := data["parking_name"].(string)
	areaType := data["area_type"].(string)
	areaCount := data["area_count"].(int)
	lanesPerAreaStr := data["lanes_per_area"].(string)
	cameraPerLane := 1
	if val, ok := data["camera_per_lane"]; ok && val != nil {
		cameraPerLane = val.(int)
	}

	// 解析每个区域的车道数
	lanesList := strings.Split(lanesPerAreaStr, ",")
	if len(lanesList) != areaCount {
		return nil, fmt.Errorf("区域数量与车道数配置不匹配")
	}

	// 1. 创建车场主记录
	mainSQL := fmt.Sprintf(`INSERT INTO parking_config (name, area_type, total_areas, area_details, created_at)
VALUES ('%s', '%s', %d, '%s', NOW());`,
		parkingName, areaType, areaCount, lanesPerAreaStr)
	statements = append(statements, mainSQL)

	// 2. 创建区域记录
	for i := 0; i < areaCount; i++ {
		areaName := fmt.Sprintf("%s-区域%d", parkingName, i+1)
		areaTypeDetail := "外场"
		if areaType == "内外场都有" && i == 0 {
			areaTypeDetail = "内场"
		}

		lanes := strings.TrimSpace(lanesList[i])
		areaSQL := fmt.Sprintf(`INSERT INTO parking_areas (parking_id, area_name, area_type, lane_count, created_at)
VALUES (LAST_INSERT_ID(), '%s', '%s', %s, NOW());`,
			areaName, areaTypeDetail, lanes)
		statements = append(statements, areaSQL)

		// 3. 创建车道记录
		var laneCount int
		fmt.Sscanf(lanes, "%d", &laneCount)
		for j := 1; j <= laneCount; j++ {
			laneSQL := fmt.Sprintf(`INSERT INTO parking_lanes (area_id, lane_number, camera_count, created_at)
VALUES (LAST_INSERT_ID(), %d, %d, NOW());`,
				j, cameraPerLane)
			statements = append(statements, laneSQL)
		}
	}

	return statements, nil
}

// generateUserSQL 生成用户管理SQL
func (dts *DataTemplateSystem) generateUserSQL(data map[string]interface{}) ([]string, error) {
	username := data["username"].(string)
	realName := data["real_name"].(string)
	email := data["email"].(string)
	role := data["role"].(string)

	department := ""
	if val, ok := data["department"]; ok && val != nil {
		department = val.(string)
	}

	sql := fmt.Sprintf(`INSERT INTO users (username, real_name, email, role, department, created_at, status)
VALUES ('%s', '%s', '%s', '%s', '%s', NOW(), 'active');`,
		username, realName, email, role, department)

	return []string{sql}, nil
}

// generateDeviceSQL 生成设备配置SQL
func (dts *DataTemplateSystem) generateDeviceSQL(data map[string]interface{}) ([]string, error) {
	deviceName := data["device_name"].(string)
	deviceType := data["device_type"].(string)
	location := data["location"].(string)

	ipAddress := ""
	if val, ok := data["ip_address"]; ok && val != nil {
		ipAddress = val.(string)
	}

	sql := fmt.Sprintf(`INSERT INTO devices (name, type, location, ip_address, created_at, status)
VALUES ('%s', '%s', '%s', '%s', NOW(), 'active');`,
		deviceName, deviceType, location, ipAddress)

	return []string{sql}, nil
}
