#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FAQ系统的道歉和错误识别能力
"""

import requests
import json
import time

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/ask"

def test_conversation(question, expected_keywords=None):
    """测试对话功能"""
    print(f"\n🔍 测试问题: {question}")
    print("-" * 50)
    
    try:
        response = requests.post(ASK_ENDPOINT, 
                               json={"question": question},
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get("answer", "")
            intent = data.get("intent", "")
            confidence = data.get("confidence", 0)
            
            print(f"✅ 回答: {answer}")
            print(f"📊 意图: {intent} (置信度: {confidence:.2f})")
            
            # 检查期望的关键词
            if expected_keywords:
                found_keywords = []
                for keyword in expected_keywords:
                    if keyword in answer:
                        found_keywords.append(keyword)
                
                if found_keywords:
                    print(f"🎯 包含期望关键词: {', '.join(found_keywords)}")
                else:
                    print(f"⚠️ 未找到期望关键词: {', '.join(expected_keywords)}")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🙏 开始测试FAQ系统的道歉和错误识别能力")
    print("=" * 60)
    
    # 测试用例列表
    test_cases = [
        # 基础问候语测试
        ("你好", ["您好", "欢迎", "智能FAQ"]),
        ("大家好", ["大家好", "欢迎", "技术助手"]),
        ("早上好", ["早上好", "欢迎"]),
        ("晚安", ["晚安", "休息"]),
        ("注意身体", ["身体", "健康", "保重"]),
        
        # 道歉类测试
        ("对不起", ["没关系", "不用道歉", "合作伙伴"]),
        ("抱歉", ["没关系", "不用道歉", "自由交流"]),
        ("不好意思", ["不用不好意思", "正常", "学习过程"]),
        ("sorry", ["No need to apologize", "totally fine", "Here to help"]),
        ("真的对不起", ["不用道歉", "没有做错", "温暖交流"]),
        ("非常抱歉", ["不用道歉", "专业服务", "积极态度"]),
        ("我道歉", ["不用道歉", "合作伙伴", "技术问题"]),
        ("请原谅", ["不用道歉", "共同探索", "专业职责"]),
        
        # 自我错误承认类测试
        ("我错了", ["感谢", "坦诚", "学习机会"]),
        ("是我错了", ["承认错误", "勇气", "成长过程"]),
        ("我的错", ["学习机会", "改进动力", "智慧积累"]),
        ("我弄错了", ["学习机会", "成长", "解决方案"]),
        ("我搞错了", ["学习机会", "改进", "帮助纠正"]),
        ("我理解错了", ["学习机会", "成长", "正确"]),
        ("我说错了", ["学习机会", "改进", "解决方案"]),
        
        # 指出他人错误类测试
        ("你错了", ["谢谢", "指正", "理解有误"]),
        ("你弄错了", ["感激", "纠正", "持续学习"]),
        ("你搞错了", ["反馈", "改进", "准确服务"]),
        ("这不对", ["重新审视", "具体指出", "详细说明"]),
        ("这是错的", ["重新审视", "哪里不对", "正确的情况"]),
        ("不是这样的", ["重新审视", "深入理解", "重新分析"]),
        
        # 一般错误类测试
        ("都错了", ["重新开始", "纠正", "重新理解"]),
        ("全错了", ["重新开始", "深入学习", "全面改进"]),
        ("错了", ["一起解决", "仔细分析", "寻找答案"]),
        ("有错", ["一起解决", "学习改进", "精准服务"]),
        ("出错了", ["一起解决", "细节", "解决好"]),
        ("不对", ["一起解决", "分析问题", "正确"]),
        
        # 混合测试 - 问候语 + 道歉
        ("你好，对不起", ["您好", "没关系", "不用道歉"]),
        ("早上好，抱歉", ["早上好", "没关系", "自由交流"]),
        
        # 混合测试 - 技术问题（应该不被误判为道歉）
        ("MySQL错误", ["MySQL", "数据库", "技术"]),
        ("代码有问题", ["代码", "问题", "技术"]),
        ("API接口不对", ["API", "接口", "技术"]),
        
        # 边界测试
        ("对不起！", ["没关系", "不用道歉", "合作"]),
        ("我错了。", ["感谢", "坦诚", "学习"]),
        ("你错了？", ["谢谢", "指正", "理解"]),
        ("都错了！！", ["重新开始", "纠正", "改进"]),
    ]
    
    # 执行测试
    success_count = 0
    total_count = len(test_cases)
    
    for question, expected_keywords in test_cases:
        if test_conversation(question, expected_keywords):
            success_count += 1
        time.sleep(0.5)  # 避免请求过快
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试完成: {success_count}/{total_count} 成功")
    print(f"✅ 成功率: {success_count/total_count*100:.1f}%")
    
    if success_count == total_count:
        print("🎉 所有测试用例都通过了！")
    else:
        print(f"⚠️ 有 {total_count - success_count} 个测试用例需要优化")

if __name__ == "__main__":
    main()
