<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>爬虫管理仪表板 - FAQ系统</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1200px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header p {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .dashboard {
            padding: 30px;
        }

        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 10px;
            text-align: center;
            border-left: 4px solid #4facfe;
        }

        .stat-card h3 {
            color: #333;
            font-size: 2em;
            margin-bottom: 10px;
        }

        .stat-card p {
            color: #666;
            font-size: 1.1em;
        }

        .controls {
            display: flex;
            gap: 15px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 1em;
            font-weight: 500;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }

        .btn-primary {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }

        .btn-success {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }

        .btn-danger {
            background: linear-gradient(135deg, #fa709a 0%, #fee140 100%);
            color: white;
        }

        .btn-warning {
            background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
            color: #333;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }

        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
        }

        .btn:disabled {
            opacity: 0.6;
            cursor: not-allowed;
            transform: none;
        }

        .btn:disabled:hover {
            transform: none;
            box-shadow: none;
        }

        .targets-section {
            margin-top: 30px;
        }

        .section-title {
            font-size: 1.5em;
            color: #333;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 2px solid #eee;
        }

        .targets-grid {
            display: grid;
            gap: 20px;
        }

        .target-card {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            border-left: 4px solid #28a745;
        }

        .target-card.disabled {
            border-left-color: #dc3545;
            opacity: 0.7;
        }

        .target-header {
            display: flex;
            justify-content: between;
            align-items: center;
            margin-bottom: 15px;
        }

        .target-name {
            font-size: 1.2em;
            font-weight: 600;
            color: #333;
        }

        .target-status {
            padding: 4px 12px;
            border-radius: 20px;
            font-size: 0.9em;
            font-weight: 500;
        }

        .status-active {
            background: #d4edda;
            color: #155724;
        }

        .status-inactive {
            background: #f8d7da;
            color: #721c24;
        }

        .target-info {
            color: #666;
            margin-bottom: 10px;
        }

        .target-actions {
            display: flex;
            gap: 10px;
            margin-top: 15px;
        }

        .btn-sm {
            padding: 6px 12px;
            font-size: 0.9em;
        }

        .loading {
            text-align: center;
            padding: 40px;
            color: #666;
        }

        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
        }

        /* 爬取中的动画效果 */
        @keyframes crawling {
            0% { opacity: 1; }
            50% { opacity: 0.6; }
            100% { opacity: 1; }
        }

        .target-card.crawling {
            animation: crawling 2s infinite;
            border-left-color: #ffc107 !important;
        }

        .status-crawling {
            background: #fff3cd;
            color: #856404;
            animation: crawling 2s infinite;
        }

        /* 日志面板样式 */
        .logs-section {
            margin-top: 30px;
        }

        .logs-container {
            background: #1e1e1e;
            color: #f8f8f2;
            border-radius: 10px;
            padding: 20px;
            font-family: 'Consolas', 'Monaco', 'Courier New', monospace;
            font-size: 13px;
            line-height: 1.4;
            max-height: 400px;
            overflow-y: auto;
            border: 1px solid #333;
        }

        .logs-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
            padding-bottom: 10px;
            border-bottom: 1px solid #333;
        }

        .logs-controls {
            display: flex;
            gap: 10px;
        }

        .log-entry {
            margin-bottom: 2px;
            padding: 2px 0;
            word-wrap: break-word;
        }

        .log-timestamp {
            color: #6272a4;
            margin-right: 8px;
        }

        .log-level-info {
            color: #8be9fd;
        }

        .log-level-success {
            color: #50fa7b;
        }

        .log-level-warning {
            color: #ffb86c;
        }

        .log-level-error {
            color: #ff5555;
        }

        .log-level-debug {
            color: #bd93f9;
        }

        .log-icon {
            margin-right: 5px;
        }

        /* 自动滚动指示器 */
        .auto-scroll-indicator {
            position: absolute;
            bottom: 10px;
            right: 10px;
            background: rgba(0,0,0,0.7);
            color: white;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            display: none;
        }

        .logs-container {
            position: relative;
        }

        /* 日志过滤按钮 */
        .log-filter {
            padding: 4px 8px;
            border: 1px solid #555;
            background: #2d2d2d;
            color: #f8f8f2;
            border-radius: 4px;
            font-size: 11px;
            cursor: pointer;
            transition: all 0.2s;
        }

        .log-filter.active {
            background: #4facfe;
            border-color: #4facfe;
        }

        .log-filter:hover {
            background: #3d3d3d;
        }

        .log-filter.active:hover {
            background: #3d8bfe;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🕷️ 爬虫管理仪表板</h1>
            <p>智能知识爬取与管理系统</p>
        </div>

        <div class="dashboard">
            <!-- 统计信息 -->
            <div class="stats-grid">
                <div class="stat-card">
                    <h3 id="totalTargets">-</h3>
                    <p>爬取目标</p>
                </div>
                <div class="stat-card">
                    <h3 id="activeTargets">-</h3>
                    <p>活跃目标</p>
                </div>
                <div class="stat-card">
                    <h3 id="crawlingNow">-</h3>
                    <p>正在爬取</p>
                </div>
                <div class="stat-card">
                    <h3 id="successRate">-</h3>
                    <p>成功率</p>
                </div>
            </div>

            <!-- 控制按钮 -->
            <div class="controls">
                <button class="btn btn-success" onclick="startCrawler()">🚀 启动爬虫</button>
                <button class="btn btn-danger" onclick="stopCrawler()">⏹️ 停止爬虫</button>
                <button class="btn btn-primary" onclick="refreshData()">🔄 刷新数据</button>
                <button class="btn btn-primary" onclick="showAddTargetForm()">➕ 添加目标</button>
            </div>

            <!-- 消息显示区域 -->
            <div id="messageArea"></div>

            <!-- 正在爬取的目标 -->
            <div class="targets-section">
                <h2 class="section-title">🕷️ 正在爬取</h2>
                <div id="activeCrawlsContainer" class="loading">
                    正在加载活跃爬取...
                </div>
            </div>

            <!-- 爬取目标列表 -->
            <div class="targets-section">
                <h2 class="section-title">📋 爬取目标</h2>
                <div id="targetsContainer" class="loading">
                    正在加载爬取目标...
                </div>
            </div>

            <!-- 实时日志 -->
            <div class="logs-section">
                <h2 class="section-title">📊 实时日志</h2>
                <div class="logs-container">
                    <div class="logs-header">
                        <div>
                            <span style="color: #50fa7b;">●</span> 爬虫日志监控
                        </div>
                        <div class="logs-controls">
                            <button class="log-filter active" data-level="all">全部</button>
                            <button class="log-filter" data-level="info">信息</button>
                            <button class="log-filter" data-level="success">成功</button>
                            <button class="log-filter" data-level="warning">警告</button>
                            <button class="log-filter" data-level="error">错误</button>
                            <button class="btn btn-sm" onclick="clearLogs()" style="padding: 4px 8px; font-size: 11px;">清空</button>
                            <button class="btn btn-sm" onclick="toggleAutoScroll()" id="autoScrollBtn" style="padding: 4px 8px; font-size: 11px;">🔒 锁定底部</button>
                        </div>
                    </div>
                    <div id="logsContent">
                        <div class="log-entry log-level-info">
                            <span class="log-timestamp">[等待中]</span>
                            <span class="log-icon">📡</span>
                            等待爬虫日志数据...
                        </div>
                    </div>
                    <div class="auto-scroll-indicator" id="autoScrollIndicator">
                        自动滚动已启用
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 全局变量
        let crawlerStatus = { running: false, total_targets: 0, active_targets: 0, crawling_now: 0, active_crawls: [] };
        let targets = [];
        let activeCrawls = [];

        // 日志相关变量
        let logs = [];
        let maxLogs = 1000; // 最大日志条数
        let autoScroll = true;
        let currentLogFilter = 'all';
        let logWebSocket = null;

        // 页面加载时初始化
        document.addEventListener('DOMContentLoaded', function() {
            refreshData();
            initLogMonitoring();
            setupLogFilters();
            // 每30秒自动刷新一次
            setInterval(refreshData, 30000);
        });

        // 刷新数据
        async function refreshData() {
            try {
                await Promise.all([
                    loadCrawlerStatus(),
                    loadTargets(),
                    loadStatistics()
                ]);
            } catch (error) {
                showMessage('加载数据失败: ' + error.message, 'error');
            }
        }

        // 加载爬虫状态
        async function loadCrawlerStatus() {
            try {
                const response = await fetch('/api/crawler/status');
                if (response.ok) {
                    const data = await response.json();
                    crawlerStatus = data.data || {};
                    activeCrawls = crawlerStatus.active_crawls || [];
                    renderActiveCrawls();
                }
            } catch (error) {
                console.error('加载爬虫状态失败:', error);
            }
        }

        // 加载爬取目标
        async function loadTargets() {
            try {
                const response = await fetch('/api/crawler/targets');
                if (response.ok) {
                    const data = await response.json();
                    targets = data.data || [];
                    renderTargets();
                } else {
                    document.getElementById('targetsContainer').innerHTML = 
                        '<div class="error">无法加载爬取目标，请检查爬虫服务是否正常运行</div>';
                }
            } catch (error) {
                document.getElementById('targetsContainer').innerHTML = 
                    '<div class="error">连接爬虫服务失败，API可能尚未启用</div>';
            }
        }

        // 加载统计信息
        async function loadStatistics() {
            try {
                const response = await fetch('/api/crawler/statistics');
                if (response.ok) {
                    const data = await response.json();
                    updateStatistics(data.data);
                }
            } catch (error) {
                console.error('加载统计信息失败:', error);
                // 使用默认值
                updateStatistics({
                    total_targets: targets.length,
                    active_targets: targets.filter(t => t.enabled).length,
                    total_crawls: 0,
                    successful_crawls: 0
                });
            }
        }

        // 更新统计信息显示
        function updateStatistics(stats) {
            document.getElementById('totalTargets').textContent = crawlerStatus.total_targets || stats.total_targets || 0;
            document.getElementById('activeTargets').textContent = crawlerStatus.active_targets || stats.active_targets || 0;
            document.getElementById('crawlingNow').textContent = crawlerStatus.crawling_now || 0;

            const successRate = stats.total_crawls > 0
                ? Math.round((stats.successful_crawls / stats.total_crawls) * 100)
                : 0;
            document.getElementById('successRate').textContent = successRate + '%';
        }

        // 渲染爬取目标
        function renderTargets() {
            const container = document.getElementById('targetsContainer');

            if (targets.length === 0) {
                container.innerHTML = '<div class="loading">暂无爬取目标</div>';
                return;
            }

            const html = targets.map(target => {
                // 检查是否正在爬取中
                const isCrawling = activeCrawls.some(active => active.id === target.id);
                const crawlingIndicator = isCrawling ? '🕷️ ' : '';
                const crawlingClass = isCrawling ? 'crawling' : '';
                const crawlingStatus = isCrawling ? '<div class="target-status status-crawling">爬取中</div>' : '';

                return `
                <div class="target-card ${target.enabled ? '' : 'disabled'} ${crawlingClass}">
                    <div class="target-header">
                        <div class="target-name">${crawlingIndicator}${target.name}</div>
                        ${crawlingStatus || `<div class="target-status ${target.enabled ? 'status-active' : 'status-inactive'}">
                            ${target.enabled ? '启用' : '禁用'}
                        </div>`}
                    </div>
                    <div class="target-info">
                        <div><strong>URL:</strong> ${target.url}</div>
                        <div><strong>类型:</strong> ${target.type}</div>
                        <div><strong>分类:</strong> ${target.category}</div>
                        <div><strong>调度:</strong> ${getScheduleText(target.schedule)}</div>
                        ${target.last_crawled ? `<div><strong>最后爬取:</strong> ${new Date(target.last_crawled).toLocaleString()}</div>` : ''}
                    </div>
                    <div class="target-actions">
                        <button class="btn btn-primary btn-sm" onclick="manualCrawl(${target.id})" ${isCrawling ? 'disabled' : ''}>
                            ${isCrawling ? '爬取中...' : '手动爬取'}
                        </button>
                        <button class="btn btn-warning btn-sm" onclick="editTarget(${target.id})" ${isCrawling ? 'disabled' : ''}>✏️ 编辑</button>
                        <button class="btn btn-danger btn-sm" onclick="deleteTarget(${target.id})" ${isCrawling ? 'disabled' : ''}>🗑️ 删除</button>
                        <button class="btn btn-secondary btn-sm" onclick="toggleTarget(${target.id}, ${!target.enabled})" ${isCrawling ? 'disabled' : ''}>
                            ${target.enabled ? '🔴 禁用' : '🟢 启用'}
                        </button>
                    </div>
                </div>
            `;
            }).join('');

            container.innerHTML = html;
        }

        // 获取调度文本描述
        function getScheduleText(schedule) {
            const scheduleMap = {
                '* * * * *': '每分钟',
                '0 */1 * * *': '每小时',
                '0 */6 * * *': '每6小时',
                '0 0 * * *': '每天',
                '0 0 * * 0': '每周'
            };
            return scheduleMap[schedule] || schedule;
        }

        // 启动爬虫
        async function startCrawler() {
            const button = event.target;
            const originalText = button.textContent;

            // 显示加载状态
            button.disabled = true;
            button.textContent = '🔄 启动中...';
            showMessage('正在启动爬虫系统...', 'info');

            try {
                const response = await fetch('/api/crawler/start', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('🚀 爬虫系统启动成功！所有启用的目标将开始按计划爬取。', 'success');
                    refreshData();
                } else {
                    showMessage('❌ 启动失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage('❌ 启动爬虫失败: 爬虫API尚未启用或服务器连接失败', 'error');
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // 停止爬虫
        async function stopCrawler() {
            if (!confirm('确定要停止爬虫系统吗？这将停止所有正在进行的爬取任务。')) {
                return;
            }

            const button = event.target;
            const originalText = button.textContent;

            // 显示加载状态
            button.disabled = true;
            button.textContent = '🔄 停止中...';
            showMessage('正在停止爬虫系统...', 'info');

            try {
                const response = await fetch('/api/crawler/stop', { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage('⏹️ 爬虫系统已停止。所有爬取任务已终止。', 'success');
                    refreshData();
                } else {
                    showMessage('❌ 停止失败: ' + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage('❌ 停止爬虫失败: 爬虫API尚未启用或服务器连接失败', 'error');
            } finally {
                // 恢复按钮状态
                button.disabled = false;
                button.textContent = originalText;
            }
        }

        // 手动爬取
        async function manualCrawl(targetId) {
            const button = event.target;
            const originalText = button.textContent;

            // 获取目标名称用于显示
            const target = targets.find(t => t.id === targetId);
            const targetName = target ? target.name : `目标${targetId}`;

            // 显示加载状态
            button.disabled = true;
            button.textContent = '🕷️ 爬取中...';
            showMessage(`🚀 正在启动 "${targetName}" 的手动爬取任务...`, 'info');

            try {
                const response = await fetch(`/api/crawler/targets/${targetId}/crawl`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage(`✅ "${targetName}" 的手动爬取任务已成功启动！爬虫正在后台执行，请稍后查看结果。`, 'success');

                    // 立即刷新状态以显示正在爬取的目标
                    setTimeout(() => {
                        refreshData();
                    }, 500); // 500ms后刷新，给爬虫一点时间启动

                    // 再次刷新以更新最终状态
                    setTimeout(() => {
                        refreshData();
                    }, 5000); // 5秒后再次刷新
                } else {
                    showMessage(`❌ "${targetName}" 启动失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage(`❌ "${targetName}" 手动爬取失败: ` + error.message, 'error');
            } finally {
                // 恢复按钮状态
                setTimeout(() => {
                    button.disabled = false;
                    button.textContent = originalText;
                }, 2000); // 2秒后恢复按钮
            }
        }

        // 删除目标
        async function deleteTarget(targetId) {
            // 获取目标名称用于显示
            const target = targets.find(t => t.id === targetId);
            const targetName = target ? target.name : `目标${targetId}`;

            if (!confirm(`确定要删除爬取目标 "${targetName}" 吗？\n\n此操作不可撤销，将删除该目标的所有配置信息。`)) {
                return;
            }

            showMessage(`🗑️ 正在删除 "${targetName}"...`, 'info');

            try {
                const response = await fetch(`/api/crawler/targets/${targetId}`, { method: 'DELETE' });
                const data = await response.json();

                if (data.success) {
                    showMessage(`✅ 爬取目标 "${targetName}" 已成功删除`, 'success');
                    refreshData();
                } else {
                    showMessage(`❌ 删除 "${targetName}" 失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage(`❌ 删除目标 "${targetName}" 失败: 爬虫API尚未启用或服务器连接失败`, 'error');
            }
        }

        // 编辑目标
        async function editTarget(targetId) {
            // 获取目标名称用于显示
            const target = targets.find(t => t.id === targetId);
            const targetName = target ? target.name : `目标${targetId}`;

            showMessage(`📝 正在加载 "${targetName}" 的详细信息...`, 'info');

            try {
                // 获取目标详情
                const response = await fetch(`/api/crawler/targets/${targetId}`);
                const data = await response.json();

                if (!data.success) {
                    showMessage(`❌ 获取 "${targetName}" 信息失败: ` + (data.message || '未知错误'), 'error');
                    return;
                }

                // API返回的数据在data.data中，不是data.target
                const targetDetail = data.data;
                if (!targetDetail) {
                    showMessage(`❌ 获取 "${targetName}" 信息失败: 目标数据为空`, 'error');
                    return;
                }

                // 清除加载消息
                document.getElementById('messageArea').innerHTML = '';
                showEditTargetForm(targetDetail);
            } catch (error) {
                showMessage(`❌ 获取 "${targetName}" 信息失败: ` + error.message, 'error');
            }
        }

        // 启用/禁用目标
        async function toggleTarget(targetId, enable) {
            // 获取目标名称用于显示
            const target = targets.find(t => t.id === targetId);
            const targetName = target ? target.name : `目标${targetId}`;
            const actionText = enable ? '启用' : '禁用';
            const actionIcon = enable ? '🟢' : '🔴';

            showMessage(`${actionIcon} 正在${actionText} "${targetName}"...`, 'info');

            try {
                const action = enable ? 'enable' : 'disable';
                const response = await fetch(`/api/crawler/targets/${targetId}/${action}`, { method: 'POST' });
                const data = await response.json();

                if (data.success) {
                    showMessage(`✅ "${targetName}" 已成功${actionText}${enable ? '，将按计划开始爬取' : '，已停止自动爬取'}`, 'success');
                    refreshData();
                } else {
                    showMessage(`❌ ${actionText} "${targetName}" 失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage(`❌ ${actionText} "${targetName}" 失败: ` + error.message, 'error');
            }
        }

        // 渲染正在爬取的目标
        function renderActiveCrawls() {
            const container = document.getElementById('activeCrawlsContainer');

            if (activeCrawls.length === 0) {
                container.innerHTML = '<div class="loading">暂无正在爬取的目标</div>';
                return;
            }

            const html = activeCrawls.map(target => `
                <div class="target-card">
                    <div class="target-header">
                        <div class="target-name">🕷️ ${target.name}</div>
                        <div class="target-status status-active">爬取中</div>
                    </div>
                    <div class="target-info">
                        <div><strong>URL:</strong> ${target.url}</div>
                        <div><strong>类型:</strong> ${target.type}</div>
                        <div><strong>分类:</strong> ${target.category}</div>
                    </div>
                </div>
            `).join('');

            container.innerHTML = html;
        }

        // 显示添加目标表单
        function showAddTargetForm() {
            const formHtml = `
                <div id="addTargetModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 600px; max-height: 80%; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px;">➕ 添加爬取目标</h3>
                        <form id="addTargetForm">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标名称:</label>
                                <input type="text" id="targetName" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标URL:</label>
                                <input type="url" id="targetUrl" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">类型:</label>
                                <select id="targetType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="website">网站</option>
                                    <option value="api">API</option>
                                    <option value="rss">RSS</option>
                                    <option value="search_engine">搜索引擎</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">分类:</label>
                                <select id="targetCategory" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="technology">技术</option>
                                    <option value="programming">编程</option>
                                    <option value="database">数据库</option>
                                    <option value="devops">运维</option>
                                    <option value="ai">人工智能</option>
                                    <option value="general">通用</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">关键词 (用逗号分隔):</label>
                                <input type="text" id="targetKeywords" placeholder="例如: python, programming, tutorial" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">调度频率:</label>
                                <select id="targetSchedule" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="* * * * *">每分钟</option>
                                    <option value="0 */1 * * *">每小时</option>
                                    <option value="0 */6 * * *" selected>每6小时</option>
                                    <option value="0 0 * * *">每天</option>
                                    <option value="0 0 * * 0">每周</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" id="targetEnabled" checked style="margin-right: 8px;">
                                    启用此目标
                                </label>
                            </div>
                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeAddTargetForm()" class="btn" style="background: #6c757d; color: white;">取消</button>
                                <button type="submit" class="btn btn-primary">添加目标</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 绑定表单提交事件
            document.getElementById('addTargetForm').addEventListener('submit', async function(e) {
                e.preventDefault();
                await submitAddTargetForm();
            });
        }

        // 关闭添加目标表单
        function closeAddTargetForm() {
            const modal = document.getElementById('addTargetModal');
            if (modal) {
                modal.remove();
            }
        }

        // 提交添加目标表单
        async function submitAddTargetForm() {
            const name = document.getElementById('targetName').value;
            const url = document.getElementById('targetUrl').value;
            const type = document.getElementById('targetType').value;
            const category = document.getElementById('targetCategory').value;
            const keywordsStr = document.getElementById('targetKeywords').value;
            const schedule = document.getElementById('targetSchedule').value;
            const enabled = document.getElementById('targetEnabled').checked;

            // 表单验证
            if (!name.trim()) {
                showMessage('❌ 请输入目标名称', 'error');
                return;
            }
            if (!url.trim()) {
                showMessage('❌ 请输入目标URL', 'error');
                return;
            }

            const keywords = keywordsStr.split(',').map(k => k.trim()).filter(k => k);

            const targetData = {
                name: name.trim(),
                url: url.trim(),
                type,
                category,
                keywords,
                schedule,
                enabled,
                selectors: {
                    title: "title, h1, .title",
                    content: ".content, .post-content, .entry-content, article, main"
                },
                filters: {
                    min_content_length: 100
                }
            };

            showMessage(`➕ 正在添加爬取目标 "${name}"...`, 'info');

            try {
                const response = await fetch('/api/crawler/targets', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(targetData)
                });

                const data = await response.json();

                if (data.success) {
                    showMessage(`✅ 爬取目标 "${name}" 添加成功！${enabled ? '已启用，将按计划开始爬取。' : '已禁用，需要手动启用。'}`, 'success');
                    closeAddTargetForm();
                    refreshData();
                } else {
                    showMessage(`❌ 添加 "${name}" 失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage(`❌ 添加目标 "${name}" 失败: ` + error.message, 'error');
            }
        }

        // 显示消息
        function showMessage(message, type, duration = null) {
            const messageArea = document.getElementById('messageArea');
            const messageDiv = document.createElement('div');
            messageDiv.className = type;
            messageDiv.textContent = message;

            // 添加关闭按钮
            const closeBtn = document.createElement('span');
            closeBtn.innerHTML = ' ✕';
            closeBtn.style.cssText = 'float: right; cursor: pointer; font-weight: bold; margin-left: 10px;';
            closeBtn.onclick = () => messageDiv.remove();
            messageDiv.appendChild(closeBtn);

            messageArea.innerHTML = '';
            messageArea.appendChild(messageDiv);

            // 根据消息类型设置不同的自动隐藏时间
            let autoHideTime = duration;
            if (autoHideTime === null) {
                switch (type) {
                    case 'info':
                        autoHideTime = 2000; // 2秒
                        break;
                    case 'success':
                        autoHideTime = 4000; // 4秒
                        break;
                    case 'error':
                        autoHideTime = 6000; // 6秒
                        break;
                    default:
                        autoHideTime = 3000; // 3秒
                }
            }

            // 自动隐藏
            if (autoHideTime > 0) {
                setTimeout(() => {
                    if (messageDiv.parentNode) {
                        messageDiv.remove();
                    }
                }, autoHideTime);
            }
        }

        // 初始化日志监控
        function initLogMonitoring() {
            // 尝试建立WebSocket连接用于实时日志
            connectLogWebSocket();

            // 如果WebSocket不可用，使用轮询方式
            setInterval(pollLogs, 2000);
        }



        // 建立WebSocket连接
        function connectLogWebSocket() {
            try {
                const protocol = window.location.protocol === 'https:' ? 'wss:' : 'ws:';
                const wsUrl = `${protocol}//${window.location.host}/ws/logs`;

                logWebSocket = new WebSocket(wsUrl);

                logWebSocket.onopen = function() {
                    addLogEntry('WebSocket连接已建立，开始接收实时日志', 'success', '📡');
                };

                logWebSocket.onmessage = function(event) {
                    try {
                        const logData = JSON.parse(event.data);
                        // 解析时间戳
                        let timestamp = null;
                        if (logData.timestamp) {
                            try {
                                timestamp = new Date(logData.timestamp);
                                if (isNaN(timestamp)) {
                                    timestamp = new Date();
                                }
                            } catch (e) {
                                timestamp = new Date();
                            }
                        }
                        addLogEntry(logData.message, logData.level, logData.icon, timestamp);
                    } catch (e) {
                        // 如果不是JSON格式，直接作为文本处理
                        addLogEntry(event.data, 'info');
                    }
                };

                logWebSocket.onclose = function() {
                    addLogEntry('WebSocket连接已断开，切换到轮询模式', 'warning', '⚠️');
                    logWebSocket = null;
                };

                logWebSocket.onerror = function() {
                    addLogEntry('WebSocket连接失败，使用轮询模式', 'warning', '⚠️');
                };
            } catch (error) {
                addLogEntry('无法建立WebSocket连接，使用轮询模式', 'info', 'ℹ️');
            }
        }

        // 轮询获取日志
        async function pollLogs() {
            if (logWebSocket && logWebSocket.readyState === WebSocket.OPEN) {
                return; // WebSocket正常工作时不使用轮询
            }

            try {
                const response = await fetch('/api/crawler/logs?limit=50');
                if (response.ok) {
                    const data = await response.json();
                    if (data.success && data.data) {
                        data.data.forEach(log => {
                            // 解析时间戳
                            let timestamp = null;
                            if (log.timestamp) {
                                try {
                                    timestamp = new Date(log.timestamp);
                                } catch (e) {
                                    timestamp = new Date(); // 使用当前时间作为备用
                                }
                            }
                            addLogEntry(log.message, log.level, log.icon, timestamp);
                        });
                    }
                }
            } catch (error) {
                // 静默处理轮询错误
            }
        }

        // 添加日志条目
        function addLogEntry(message, level = 'info', icon = '', timestamp = null) {
            let now;
            if (timestamp && timestamp instanceof Date && !isNaN(timestamp)) {
                now = timestamp;
            } else if (timestamp && typeof timestamp === 'string') {
                try {
                    now = new Date(timestamp);
                    if (isNaN(now)) {
                        now = new Date();
                    }
                } catch (e) {
                    now = new Date();
                }
            } else {
                now = new Date();
            }

            const timeStr = now.toLocaleTimeString('zh-CN', {
                hour12: false,
                hour: '2-digit',
                minute: '2-digit',
                second: '2-digit'
            });

            // 解析日志消息，提取图标和级别
            const parsedLog = parseLogMessage(message);
            const finalLevel = level || parsedLog.level;
            const finalIcon = icon || parsedLog.icon;
            const finalMessage = parsedLog.message;

            // 检查是否是重复日志（相同消息且时间差小于2秒）
            const currentTime = now.getTime();
            const isDuplicate = logs.some(log => {
                if (log.message !== finalMessage) return false;

                // 解析日志时间戳
                const logTime = new Date();
                const timeParts = log.timestamp.split(':');
                if (timeParts.length === 3) {
                    logTime.setHours(parseInt(timeParts[0]), parseInt(timeParts[1]), parseInt(timeParts[2]), 0);
                    return Math.abs(currentTime - logTime.getTime()) < 2000; // 2秒内的重复
                }
                return false;
            });

            if (isDuplicate) {
                return; // 跳过重复日志
            }

            const logEntry = {
                id: Date.now() + Math.random(),
                timestamp: timeStr,
                level: finalLevel,
                icon: finalIcon,
                message: finalMessage,
                fullText: message
            };

            logs.push(logEntry);

            // 限制日志数量
            if (logs.length > maxLogs) {
                logs = logs.slice(-maxLogs);
            }

            renderLogs();
        }

        // 解析日志消息，提取图标和级别
        function parseLogMessage(message) {
            let level = 'info';
            let icon = 'ℹ️';
            let cleanMessage = message;

            // 根据消息内容判断级别和图标
            if (message.includes('✅') || message.includes('成功') || message.includes('完成')) {
                level = 'success';
                icon = '✅';
            } else if (message.includes('⚠️') || message.includes('警告') || message.includes('失败')) {
                level = 'warning';
                icon = '⚠️';
            } else if (message.includes('❌') || message.includes('错误') || message.includes('ERROR')) {
                level = 'error';
                icon = '❌';
            } else if (message.includes('🕷️') || message.includes('爬取')) {
                level = 'info';
                icon = '🕷️';
            } else if (message.includes('📊') || message.includes('处理')) {
                level = 'info';
                icon = '📊';
            } else if (message.includes('🔗') || message.includes('链接')) {
                level = 'info';
                icon = '🔗';
            } else if (message.includes('📝') || message.includes('保存')) {
                level = 'success';
                icon = '📝';
            } else if (message.includes('🧹') || message.includes('清理')) {
                level = 'debug';
                icon = '🧹';
            }

            return { level, icon, message: cleanMessage };
        }

        // 渲染日志
        function renderLogs() {
            const logsContent = document.getElementById('logsContent');
            const filteredLogs = logs.filter(log =>
                currentLogFilter === 'all' || log.level === currentLogFilter
            );

            if (filteredLogs.length === 0) {
                logsContent.innerHTML = `
                    <div class="log-entry log-level-info">
                        <span class="log-timestamp">[等待中]</span>
                        <span class="log-icon">📡</span>
                        ${currentLogFilter === 'all' ? '等待爬虫日志数据...' : `暂无${getLogLevelText(currentLogFilter)}日志`}
                    </div>
                `;
                return;
            }

            const html = filteredLogs.slice(-200).map(log => `
                <div class="log-entry log-level-${log.level}">
                    <span class="log-timestamp">[${log.timestamp}]</span>
                    <span class="log-icon">${log.icon}</span>
                    ${escapeHtml(log.message)}
                </div>
            `).join('');

            logsContent.innerHTML = html;

            // 自动滚动到底部
            if (autoScroll) {
                const logsContainer = logsContent.parentElement;
                logsContainer.scrollTop = logsContainer.scrollHeight;
            }
        }

        // 获取日志级别文本
        function getLogLevelText(level) {
            const levelMap = {
                'info': '信息',
                'success': '成功',
                'warning': '警告',
                'error': '错误',
                'debug': '调试'
            };
            return levelMap[level] || level;
        }

        // HTML转义
        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        // 设置日志过滤器
        function setupLogFilters() {
            const filterButtons = document.querySelectorAll('.log-filter');
            filterButtons.forEach(button => {
                button.addEventListener('click', function() {
                    // 移除所有active类
                    filterButtons.forEach(btn => btn.classList.remove('active'));
                    // 添加active类到当前按钮
                    this.classList.add('active');
                    // 设置过滤器
                    currentLogFilter = this.dataset.level;
                    renderLogs();
                });
            });
        }

        // 清空日志
        function clearLogs() {
            if (confirm('确定要清空所有日志吗？')) {
                logs = [];
                renderLogs();
                addLogEntry('日志已清空', 'info', '🧹');
            }
        }

        // 切换自动滚动
        function toggleAutoScroll() {
            autoScroll = !autoScroll;
            const btn = document.getElementById('autoScrollBtn');
            const indicator = document.getElementById('autoScrollIndicator');

            if (autoScroll) {
                btn.textContent = '🔒 锁定底部';
                indicator.style.display = 'block';
                // 立即滚动到底部
                const logsContainer = document.getElementById('logsContent').parentElement;
                logsContainer.scrollTop = logsContainer.scrollHeight;
                setTimeout(() => {
                    indicator.style.display = 'none';
                }, 2000);
            } else {
                btn.textContent = '🔓 解锁滚动';
                indicator.style.display = 'none';
            }
        }

        // 显示编辑目标表单
        function showEditTargetForm(target) {
            const formHtml = `
                <div id="editTargetModal" style="position: fixed; top: 0; left: 0; width: 100%; height: 100%; background: rgba(0,0,0,0.5); z-index: 1000; display: flex; align-items: center; justify-content: center;">
                    <div style="background: white; padding: 30px; border-radius: 10px; width: 90%; max-width: 600px; max-height: 80%; overflow-y: auto;">
                        <h3 style="margin-bottom: 20px;">✏️ 编辑爬取目标</h3>
                        <form id="editTargetForm">
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标名称:</label>
                                <input type="text" id="editTargetName" value="${target.name}" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">目标URL:</label>
                                <input type="url" id="editTargetUrl" value="${target.url}" required style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">类型:</label>
                                <select id="editTargetType" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="website" ${target.type === 'website' ? 'selected' : ''}>网站</option>
                                    <option value="api" ${target.type === 'api' ? 'selected' : ''}>API</option>
                                    <option value="rss" ${target.type === 'rss' ? 'selected' : ''}>RSS</option>
                                    <option value="search_engine" ${target.type === 'search_engine' ? 'selected' : ''}>搜索引擎</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">分类:</label>
                                <select id="editTargetCategory" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="technology" ${target.category === 'technology' ? 'selected' : ''}>技术</option>
                                    <option value="programming" ${target.category === 'programming' ? 'selected' : ''}>编程</option>
                                    <option value="database" ${target.category === 'database' ? 'selected' : ''}>数据库</option>
                                    <option value="devops" ${target.category === 'devops' ? 'selected' : ''}>运维</option>
                                    <option value="ai" ${target.category === 'ai' ? 'selected' : ''}>人工智能</option>
                                    <option value="general" ${target.category === 'general' ? 'selected' : ''}>通用</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">关键词 (用逗号分隔):</label>
                                <input type="text" id="editTargetKeywords" value="${target.keywords ? target.keywords.join(', ') : ''}" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                            </div>
                            <div style="margin-bottom: 15px;">
                                <label style="display: block; margin-bottom: 5px; font-weight: bold;">调度频率:</label>
                                <select id="editTargetSchedule" style="width: 100%; padding: 8px; border: 1px solid #ddd; border-radius: 4px;">
                                    <option value="* * * * *" ${target.schedule === '* * * * *' ? 'selected' : ''}>每分钟</option>
                                    <option value="0 */1 * * *" ${target.schedule === '0 */1 * * *' ? 'selected' : ''}>每小时</option>
                                    <option value="0 */6 * * *" ${target.schedule === '0 */6 * * *' ? 'selected' : ''}>每6小时</option>
                                    <option value="0 0 * * *" ${target.schedule === '0 0 * * *' ? 'selected' : ''}>每天</option>
                                    <option value="0 0 * * 0" ${target.schedule === '0 0 * * 0' ? 'selected' : ''}>每周</option>
                                </select>
                            </div>
                            <div style="margin-bottom: 20px;">
                                <label style="display: flex; align-items: center;">
                                    <input type="checkbox" id="editTargetEnabled" ${target.enabled ? 'checked' : ''} style="margin-right: 8px;">
                                    启用此目标
                                </label>
                            </div>
                            <div style="display: flex; gap: 10px; justify-content: flex-end;">
                                <button type="button" onclick="closeEditTargetForm()" class="btn" style="background: #6c757d; color: white;">取消</button>
                                <button type="submit" class="btn btn-primary">保存修改</button>
                            </div>
                        </form>
                    </div>
                </div>
            `;

            document.body.insertAdjacentHTML('beforeend', formHtml);

            // 绑定表单提交事件
            document.getElementById('editTargetForm').addEventListener('submit', async (e) => {
                e.preventDefault();
                await submitEditTargetForm(target.id);
            });
        }

        // 关闭编辑目标表单
        function closeEditTargetForm() {
            const modal = document.getElementById('editTargetModal');
            if (modal) {
                modal.remove();
            }
        }

        // 提交编辑目标表单
        async function submitEditTargetForm(targetId) {
            const name = document.getElementById('editTargetName').value;
            const url = document.getElementById('editTargetUrl').value;
            const type = document.getElementById('editTargetType').value;
            const category = document.getElementById('editTargetCategory').value;
            const keywordsStr = document.getElementById('editTargetKeywords').value;
            const schedule = document.getElementById('editTargetSchedule').value;
            const enabled = document.getElementById('editTargetEnabled').checked;

            // 表单验证
            if (!name.trim()) {
                showMessage('❌ 请输入目标名称', 'error');
                return;
            }
            if (!url.trim()) {
                showMessage('❌ 请输入目标URL', 'error');
                return;
            }

            const keywords = keywordsStr.split(',').map(k => k.trim()).filter(k => k);

            const targetData = {
                id: targetId,
                name: name.trim(),
                url: url.trim(),
                type,
                category,
                keywords,
                schedule,
                enabled,
                selectors: {
                    title: "title, h1, .title",
                    content: ".content, .post-content, .entry-content, article, main"
                },
                filters: {
                    min_content_length: 100
                }
            };

            showMessage(`💾 正在保存 "${name}" 的修改...`, 'info');

            try {
                const response = await fetch(`/api/crawler/targets/${targetId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(targetData)
                });

                const data = await response.json();

                if (data.success) {
                    showMessage(`✅ 目标 "${name}" 更新成功！修改已生效。`, 'success');
                    closeEditTargetForm();
                    refreshData();
                } else {
                    showMessage(`❌ 更新 "${name}" 失败: ` + (data.message || '未知错误'), 'error');
                }
            } catch (error) {
                showMessage(`❌ 更新目标 "${name}" 失败: ` + error.message, 'error');
            }
        }
    </script>
</body>
</html>
