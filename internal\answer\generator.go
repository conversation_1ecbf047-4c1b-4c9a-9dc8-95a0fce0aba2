package answer

import (
	"fmt"
	"strings"

	"faq-system/internal/mysql"
	"faq-system/internal/vectorstore"
)

// Generator 答案生成器 - 从原main.go迁移核心逻辑
type Generator struct {
	globalFAQs []mysql.FAQ
	templates  map[string]string
}

// NewAnswerGenerator 创建答案生成器 - 保持原有逻辑不变
func NewAnswerGenerator(faqs []mysql.FAQ) *Generator {
	templates := map[string]string{
		"high_confidence":   "%s",
		"medium_confidence": "%s\n\n💡 温馨提示：我对这个答案有 %.1f%% 的把握，如需更详细信息请继续提问。",
		"low_confidence":    "我找到了一些可能相关的信息（把握度：%.1f%%）：\n\n%s\n\n🤔 如果不完全符合您的需求，请：\n• 提供更多具体信息\n• 换个方式描述问题\n• 或告诉我您想了解的具体方面",
	}
	return &Generator{
		globalFAQs: faqs,
		templates:  templates,
	}
}

// GenerateIntelligentAnswer 基于向量搜索结果生成回答 - 保持原有逻辑不变
func (g *Generator) GenerateIntelligentAnswer(question string, results []vectorstore.SearchResult) (string, string) {
	// 注意：智能匹配已经在主函数中优先处理了，这里只处理向量搜索结果

	if len(results) == 0 {
		return g.GenerateFallbackAnswer(question), "系统异常"
	}

	// 根据搜索结果的ID获取对应的FAQ答案
	var answer string
	for _, faq := range g.globalFAQs {
		if faq.ID == results[0].ID {
			answer = faq.Answer
			break
		}
	}

	if answer == "" {
		return g.GenerateFallbackAnswer(question), "系统异常"
	}

	// 降低阈值，让系统更容易匹配
	// 高相似度（> 0.3）时，直接提供答案
	if results[0].Score > 0.3 {
		return answer, "FAQ数据库"
	}

	// 中等相似度（0.1-0.3）时，提供谨慎的回答
	if results[0].Score > 0.1 {
		return fmt.Sprintf("%s\n\n💡 温馨提示：我对这个答案有 %.1f%% 的把握，建议您可以进一步确认相关信息哦～",
			answer, results[0].Score*100), "FAQ数据库（高匹配）"
	}

	// 低相似度（0.05-0.1）时，提示不确定
	if results[0].Score > 0.05 {
		return fmt.Sprintf("嗯...我找到了一些可能相关的信息，不过我只有 %.1f%% 的把握：\n\n%s\n\n🤔 如果这个答案不太符合您的需求，您可以：\n• 换个方式问问看\n• 提供更多具体信息\n• 或者联系管理员获取更准确的答案",
			results[0].Score*100, answer), "FAQ数据库（低匹配）"
	}

	// 低相似度时，明确表示系统异常
	return g.GenerateFallbackAnswer(question), "系统异常"
}

// GenerateSmartAnswer 严密的智能匹配系统 - 保持原有逻辑不变
func (g *Generator) GenerateSmartAnswer(question string) string {
	original := strings.TrimSpace(question)
	q := strings.ToLower(original)

	// 第一层：处理空输入和极短输入
	if len(original) == 0 {
		return "🤔 您似乎没有输入任何内容，请告诉我您想了解什么技术问题吧！"
	}

	// 第二层：处理单字或极短输入（1-2个字符）
	if len([]rune(original)) <= 2 {
		// 常见单字处理
		switch q {
		case "你", "我", "他", "她", "它":
			return "🤔 请细说，希望能帮到您！我是技术FAQ助手，可以为您解答：\n\n• LocalAI和AI技术\n• MySQL数据库\n• 向量搜索\n• Go语言开发\n• 系统部署\n\n请详细描述您的问题～"
		case "好", "行", "嗯", "哦", "啊":
			return "😊 请细说您想了解的技术问题，我会尽力为您解答！"
		case "hi", "ok":
			return "👋 Hello! 请告诉我您想了解什么技术问题吧！"
		default:
			return "🤔 请细说，希望能帮到您！请详细描述您想了解的技术问题。"
		}
	}

	// 第三层：精确匹配问候语
	greetings := []string{"你好", "大家好", "各位好", "hello", "hi", "嗨", "您好"}
	for _, greeting := range greetings {
		if q == greeting || q == greeting+"！" || q == greeting+"!" {
			return "👋 您好！欢迎使用智能FAQ系统！\n\n我专注于技术领域，可以为您解答：\n• LocalAI和人工智能\n• MySQL数据库技术\n• 向量搜索原理\n• Go语言开发\n• 系统架构部署\n\n请随时提出您的技术问题！"
		}
	}

	// 第四层：精确匹配个人问题
	personalQuestions := []string{"你呢", "我呢", "你好吗", "你怎么样", "我怎么样"}
	for _, pq := range personalQuestions {
		if q == pq || q == pq+"？" || q == pq+"?" {
			return "😊 我是FAQ技术助手，专门为您解答技术问题！\n\n🤖 我的专长：\n• LocalAI技术咨询\n• 数据库解决方案\n• 编程语言指导\n• 系统架构建议\n\n有什么技术问题需要我帮助吗？"
		}
	}

	// 第五层：能力询问
	capabilityQuestions := []string{"你会什么", "你能做什么", "你有什么功能", "你的能力", "你可以", "你能够", "你会做什么", "你擅长什么", "你的专长", "你能帮我什么", "你有哪些技能", "你都会什么", "你能干什么", "你的本领", "你会哪些", "你懂什么"}
	for _, cq := range capabilityQuestions {
		if strings.Contains(q, cq) {
			return "🤖 我是一个多才多艺的智能技术助手！让我介绍一下我的核心能力：\n\n" +
				"💡 **智能对话能力**：\n" +
				"• 🎯 **意图识别** - 准确理解您的需求和问题类型\n" +
				"• 😊 **情感感知** - 根据您的情绪调整回应风格\n" +
				"• 💬 **多轮对话** - 记住上下文，进行连贯交流\n" +
				"• 🗣️ **自然对话** - 支持各种口语化表达\n\n" +
				"🔧 **技术专业能力**：\n" +
				"• 📚 **知识问答** - 回答各种技术问题\n" +
				"• 🔍 **智能搜索** - 向量化语义匹配技术\n" +
				"• 🧮 **算法计算** - 数学运算和算法解析\n" +
				"• 💾 **数据库技术** - MySQL、向量数据库等\n" +
				"• 🚀 **编程语言** - Go、Python、JavaScript等\n\n" +
				"🧠 **学习进化能力**：\n" +
				"• 📖 **知识学习** - 从交互中不断学习新知识\n" +
				"• 🎯 **个性化** - 根据您的习惯调整回应\n" +
				"• 📊 **反馈优化** - 基于您的反馈持续改进\n\n" +
				"🚀 **现在就试试我的能力吧！有什么问题尽管问我！**"
		}
	}

	// 第六层：请求发言
	requestSpeakPhrases := []string{"你说", "你请说", "你来说", "你讲", "你告诉我", "你解释", "你回答", "说说看", "讲讲", "谈谈", "聊聊", "介绍介绍", "说一下", "讲一下", "你先说", "你说说", "你讲讲", "请说", "请讲", "请告诉我"}
	for _, rsp := range requestSpeakPhrases {
		if q == rsp || q == rsp+"吧" || q == rsp+"呗" {
			return "😊 好的！我很乐意为您分享知识。请告诉我您想了解什么主题，比如：\n\n" +
				"• 🔧 **技术话题** - 编程、数据库、系统架构\n" +
				"• 🧮 **算法数学** - 计算、排序、搜索算法\n" +
				"• 🤖 **AI技术** - 机器学习、向量搜索、NLP\n" +
				"• 💡 **解决方案** - 具体问题的技术解决方案\n\n" +
				"🎯 请具体说明您感兴趣的方向，我会详细为您介绍！"
		}
	}

	// 第七层：教学请求
	teachingPhrases := []string{"让我告诉你", "我来告诉你", "我教你", "我来教你", "我跟你说", "我告诉你", "听我说", "我说给你听", "我来解释", "我来说明", "让我解释", "让我说明", "我知道", "我了解", "我懂", "我会", "我明白这个", "这个我知道"}
	for _, tp := range teachingPhrases {
		if strings.Contains(q, tp) {
			return "🎓 太好了！我很乐意向您学习新知识！\n\n" +
				"📚 **我的学习能力**：\n" +
				"• 🧠 **知识吸收** - 我可以学习和记住您教给我的内容\n" +
				"• 🔄 **知识应用** - 学到的知识会用于回答其他用户的问题\n" +
				"• 📊 **持续改进** - 基于反馈不断优化知识质量\n\n" +
				"💡 **请开始教我吧！您可以**：\n" +
				"• 分享专业知识和经验\n" +
				"• 提供问题的解决方案\n" +
				"• 纠正我的错误回答\n" +
				"• 补充相关信息\n\n" +
				"🚀 我已经准备好学习了，请开始您的教学！"
		}
	}

	// 第八层：求助请求
	helpPhrases := []string{"帮我", "帮助我", "协助我", "支持我", "指导我", "教我", "告诉我怎么", "help me", "help", "assist", "guide", "我需要帮助", "需要你的帮助", "求助", "请帮忙", "麻烦你", "拜托", "能帮我吗", "可以帮我吗"}
	for _, hp := range helpPhrases {
		if strings.Contains(q, hp) {
			return "🤝 当然！我很乐意帮助您！\n\n" +
				"💡 **为了更好地帮助您，请告诉我**：\n" +
				"• 您遇到了什么具体问题？\n" +
				"• 您希望达到什么目标？\n" +
				"• 您已经尝试过什么方法？\n" +
				"• 有什么特殊要求或限制？\n\n" +
				"🛠️ **我的帮助方式**：\n" +
				"• 🔍 **问题诊断** - 分析问题根本原因\n" +
				"• 💡 **解决方案** - 提供多种解决思路\n" +
				"• 📋 **步骤指导** - 详细的操作步骤\n" +
				"• 🎯 **最佳实践** - 推荐行业标准做法\n\n" +
				"🎯 请详细描述您的需求，我会为您提供最合适的解决方案！"
		}
	}

	// 第九层：礼貌用语
	politePhrases := []string{"请", "麻烦", "拜托", "劳驾", "打扰", "不好意思", "抱歉", "对不起", "excuse me", "please", "sorry", "pardon", "能否", "可否", "是否可以", "方便的话", "如果可以", "如果方便", "有空的话", "有时间的话"}
	for _, pp := range politePhrases {
		if q == pp || strings.HasPrefix(q, pp) {
			return "😊 您太客气了！我很乐意为您服务。\n\n" +
				"🎯 请告诉我您需要什么帮助，我会认真对待您的每一个请求。\n\n" +
				"💡 您可以直接提问，不需要太多客套话，我们就像朋友一样交流吧！\n\n" +
				"🚀 现在就告诉我您需要什么帮助吧！"
		}
	}

	// 第十层：确认和否定
	if q == "好的" || q == "好" || q == "行" || q == "可以" || q == "没问题" || q == "明白" || q == "知道了" || q == "了解" || q == "清楚" || q == "ok" || q == "okay" || q == "yes" || q == "是的" || q == "对" || q == "正确" || q == "嗯" || q == "嗯嗯" {
		return "😊 很好！我们继续吧，还有什么问题吗？如果您需要了解任何技术相关的内容，随时可以问我！"
	}

	if q == "不是" || q == "不对" || q == "不" || q == "错" || q == "错了" || q == "不正确" || q == "不对的" || q == "有误" || q == "不准确" || q == "no" || q == "not" || q == "wrong" || q == "incorrect" || q == "false" {
		return "🤔 好的，看来我理解有误。能告诉我正确的信息吗？我会认真学习并改正我的回答。"
	}

	// 第十一层：身份询问
	identityQuestions := []string{"你是谁", "你叫什么", "你的名字", "你叫啥", "你是哪个", "你是什么东西", "你到底是谁", "你究竟是什么", "告诉我你是谁", "说说你是谁", "你的真实身份", "你的身份是什么", "你是机器人吗", "你是AI吗", "你是人工智能吗", "你是真人吗", "你是程序吗", "你是软件吗", "你是什么系统", "你是什么助手", "你是什么机器人", "你从哪来", "你来自哪里", "你的创造者", "谁创造了你", "谁开发了你", "你的开发者", "你的制作者", "你的背景", "你的来历", "你的故事", "关于你", "你的简介", "你的资料", "认识一下", "自我介绍", "介绍下你自己", "说说你自己", "你自己介绍一下"}

	for _, iq := range identityQuestions {
		if q == iq || q == iq+"？" || q == iq+"?" || strings.Contains(q, iq) {
			// 根据不同的询问类型给出不同的回应
			if strings.Contains(q, "机器人") || strings.Contains(q, "AI") || strings.Contains(q, "人工智能") || strings.Contains(q, "程序") || strings.Contains(q, "软件") {
				return "🤖 是的，我是一个AI人工智能助手！\n\n" +
					"🔬 **技术特性**：\n" +
					"• 🧠 **AI技术** - 基于先进的自然语言处理技术\n" +
					"• 💾 **知识库** - 拥有丰富的技术知识储备\n" +
					"• 🔄 **学习能力** - 能够从交互中不断学习和改进\n" +
					"• 🎯 **专业领域** - 专注于技术问答和解决方案\n\n" +
					"🚀 **我可以帮您**：\n" +
					"• 解答各种技术问题\n" +
					"• 提供编程和开发指导\n" +
					"• 分析系统架构方案\n" +
					"• 推荐最佳实践方法\n\n" +
					"💬 有什么技术问题需要我帮助吗？"
			} else if strings.Contains(q, "真人") || strings.Contains(q, "人类") {
				return "😊 不，我不是真人，我是一个AI智能助手！\n\n" +
					"🤝 **人机协作的优势**：\n" +
					"• 🤖 **我的优势** - 24小时在线、知识丰富、反应迅速\n" +
					"• 👨‍💻 **人类优势** - 创造力、情感理解、复杂判断\n" +
					"• 🌟 **合作模式** - 我提供技术支持，您进行创新决策\n" +
					"• 💡 **共同目标** - 高效解决技术问题\n\n" +
					"🎯 让我们发挥各自的优势，一起解决技术挑战吧！"
			} else if strings.Contains(q, "创造") || strings.Contains(q, "开发") || strings.Contains(q, "制作") || strings.Contains(q, "来自") {
				return "🏗️ 我是由专业的技术团队开发的智能FAQ系统！\n\n" +
					"👥 **开发背景**：\n" +
					"• 🛠️ **技术栈** - Go语言 + LocalAI + 向量数据库\n" +
					"• 🎯 **设计目标** - 提供高质量的技术问答服务\n" +
					"• 📚 **知识来源** - 技术文档、最佳实践、专家经验\n" +
					"• 🔄 **持续优化** - 基于用户反馈不断改进\n\n" +
					"💡 我的存在就是为了让技术学习和问题解决变得更加高效！"
			} else if strings.Contains(q, "介绍") || strings.Contains(q, "自己") || strings.Contains(q, "认识") {
				return "😊 很高兴为您做自我介绍！\n\n" +
					"🌟 **全面介绍**：\n" +
					"• 🤖 **身份** - 智能FAQ技术助手，您的专业技术伙伴\n" +
					"• 🧠 **智能** - 具备意图识别、上下文理解、知识推理能力\n" +
					"• 💬 **交流** - 支持自然对话，理解各种表达方式\n" +
					"• 📚 **知识** - 涵盖编程、数据库、系统架构等技术领域\n" +
					"• 🎯 **目标** - 帮助您快速解决技术问题，提升工作效率\n\n" +
					"🚀 现在就开始我们的技术交流吧！有什么问题尽管问我！"
			} else {
				// 通用身份回应
				return "🤖 我是智能FAQ技术助手，很高兴认识您！\n\n" +
					"💡 **核心特点**：\n" +
					"• 🎯 **专业性** - 专注于技术领域的问答服务\n" +
					"• 🧠 **智能化** - 理解意图、记忆上下文、个性化回应\n" +
					"• 🔄 **学习型** - 从每次交互中学习，持续改进服务质量\n" +
					"• 🤝 **友好性** - 以人性化的方式与您交流\n\n" +
					"🚀 **我可以帮您**：\n" +
					"• 解答技术疑问\n" +
					"• 提供解决方案\n" +
					"• 推荐学习资源\n" +
					"• 分析技术选型\n\n" +
					"请告诉我您遇到的技术问题！"
			}
		}
	}

	// 第十二层：精确技术关键词匹配
	return g.matchTechnicalKeywords(q)
}

// matchTechnicalKeywords 匹配技术关键词 - 保持原有逻辑不变
func (g *Generator) matchTechnicalKeywords(q string) string {
	// LocalAI相关 - 严格匹配
	if q == "localai" || q == "local ai" || strings.HasPrefix(q, "什么是localai") || strings.HasPrefix(q, "localai是什么") {
		return "LocalAI是一个开源的本地AI推理引擎，让您可以在自己的设备上运行大语言模型。\n\n🌟 主要特点：\n• 完全本地化部署，保护数据隐私\n• 支持多种AI模型格式（GGML、GGUF等）\n• 兼容OpenAI API，易于集成\n• 可以运行文本生成、图像生成、语音识别等任务\n• 开源免费，社区活跃\n\n💡 简单来说，LocalAI就是让您在本地拥有自己的ChatGPT！"
	}

	// MySQL相关问题
	if strings.Contains(q, "mysql") || strings.Contains(q, "数据库") {
		return "MySQL是世界上最流行的开源关系型数据库管理系统！\n\n🗄️ 在这个FAQ系统中，MySQL负责：\n• 存储所有的FAQ问题和答案\n• 管理用户查询历史\n• 提供可靠的数据持久化\n• 支持复杂的数据查询操作\n\n✨ 系统会自动创建数据库结构，您无需手动配置，开箱即用！"
	}

	// 向量搜索相关
	if strings.Contains(q, "向量") || strings.Contains(q, "embedding") || strings.Contains(q, "搜索") {
		return "向量搜索是现代AI系统的核心技术，让计算机能够理解文本的语义！\n\n🧠 工作原理：\n• 将文本转换为高维数字向量（embedding）\n• 通过计算向量间的相似度找到相关内容\n• 即使用词不同，语义相似的内容也能被找到\n\n🎯 在这个FAQ系统中：\n• 每个FAQ都有对应的向量表示\n• 用户问题也会转换为向量\n• 通过余弦相似度等算法进行智能匹配\n\n这就是为什么系统能理解您问题背后的真实意图！"
	}

	// Go语言相关 - 使用更精确的匹配
	if strings.Contains(q, "golang") || strings.Contains(q, "go语言") || strings.Contains(q, "go 语言") ||
		(strings.Contains(q, "go") && (strings.Contains(q, "语言") || strings.Contains(q, "编程") || strings.Contains(q, "开发"))) {
		return "Go语言是Google开发的现代编程语言，这个FAQ系统就是用Go构建的！\n\n🚀 Go语言的优势：\n• 语法简洁清晰，学习曲线平缓\n• 内置强大的并发支持（goroutines）\n• 编译速度极快，运行效率高\n• 丰富的标准库，开发效率高\n• 跨平台支持，部署简单\n\n💪 特别适合构建：\n• Web服务和API\n• 微服务架构\n• 云原生应用\n• 分布式系统\n\n这就是为什么我们选择Go来构建这个智能FAQ系统！"
	}

	// 继续其他技术匹配...
	return g.handleAdvancedTechnical(q)
}

// handleAdvancedTechnical 处理高级技术关键词 - 保持原有逻辑不变
func (g *Generator) handleAdvancedTechnical(q string) string {
	// 人工智能相关通用知识
	if strings.Contains(q, "人工智能") || strings.Contains(q, "ai") || strings.Contains(q, "机器学习") || strings.Contains(q, "深度学习") {
		return "人工智能（AI）是当今最热门的技术领域之一！\n\n🤖 AI的核心概念：\n• 让机器模拟人类智能行为\n• 通过数据学习和模式识别\n• 能够自主决策和解决问题\n\n🔥 主要技术分支：\n• 机器学习：从数据中学习规律\n• 深度学习：模拟神经网络结构\n• 自然语言处理：理解和生成文本\n• 计算机视觉：识别和理解图像\n\n💡 实际应用：\n• 智能助手（如ChatGPT、Siri）\n• 自动驾驶汽车\n• 医疗诊断辅助\n• 金融风险控制\n• 推荐系统\n\n就像这个FAQ系统，也运用了AI技术来理解您的问题并提供智能回答！"
	}

	// 数据库相关通用知识
	if (strings.Contains(q, "数据库") || strings.Contains(q, "database")) && !strings.Contains(q, "mysql") {
		return "数据库是现代信息系统的核心基础设施！\n\n📊 数据库的作用：\n• 持久化存储数据\n• 提供高效的数据查询\n• 确保数据的一致性和完整性\n• 支持并发访问和事务处理\n\n🗄️ 主要类型：\n• 关系型数据库：MySQL、PostgreSQL、Oracle\n• 非关系型数据库：MongoDB、Redis、Cassandra\n• 图数据库：Neo4j、ArangoDB\n• 时序数据库：InfluxDB、TimescaleDB\n\n⚡ 选择建议：\n• 传统业务系统：选择MySQL/PostgreSQL\n• 高并发场景：考虑Redis缓存\n• 大数据分析：选择列式数据库\n• 复杂关系：考虑图数据库\n\n这个FAQ系统就使用MySQL来存储问答数据，保证数据的可靠性！"
	}

	// 编程相关通用知识
	if strings.Contains(q, "编程") || strings.Contains(q, "程序") || strings.Contains(q, "代码") || strings.Contains(q, "开发") {
		return "编程是创造数字世界的魔法！\n\n💻 编程的本质：\n• 用计算机能理解的语言描述解决方案\n• 将复杂问题分解为简单步骤\n• 通过逻辑思维构建软件系统\n\n🌟 热门编程语言：\n• Python：简单易学，AI/数据科学首选\n• JavaScript：Web开发必备\n• Java：企业级应用的主力\n• Go：云原生和微服务的新宠\n• Rust：系统编程的安全选择\n\n🚀 学习建议：\n• 选择一门语言深入学习\n• 多做项目实践\n• 学习算法和数据结构\n• 关注开源社区\n• 培养解决问题的思维\n\n记住：编程不仅是技术，更是一种思维方式！"
	}

	// 技术学习相关
	if strings.Contains(q, "学习") || strings.Contains(q, "入门") || strings.Contains(q, "教程") {
		return "学习技术是一个持续的旅程！\n\n📚 高效学习方法：\n• 理论与实践相结合\n• 从简单项目开始\n• 多看优秀的开源代码\n• 加入技术社区交流\n• 写技术博客总结经验\n\n🎯 学习路径建议：\n• 基础知识：计算机原理、数据结构\n• 编程语言：选择1-2门深入掌握\n• 框架工具：根据方向选择主流框架\n• 项目实战：完成端到端的项目\n• 持续进阶：关注新技术趋势\n\n💡 学习资源：\n• 官方文档（最权威）\n• GitHub开源项目\n• 技术博客和教程\n• 在线课程平台\n• 技术会议和讲座\n\n坚持学习，技术改变世界，也改变你的未来！"
	}

	// 非技术问题处理
	return g.handleNonTechnical(q)
}

// handleNonTechnical 处理非技术问题 - 保持原有逻辑不变
func (g *Generator) handleNonTechnical(q string) string {
	// 生活类问题 - 精确匹配
	cookingKeywords := []string{"做饭", "菜谱", "烹饪", "红烧肉", "炒菜", "煮饭", "食谱"}
	for _, keyword := range cookingKeywords {
		if strings.Contains(q, keyword) {
			return "🍳 我是技术FAQ助手，专注于技术领域，不太懂烹饪呢！\n\n👨‍🍳 烹饪建议：\n• 下载美食类APP（如下厨房）\n• 搜索菜谱网站\n• 观看烹饪视频教程\n• 请教身边的美食达人\n\n不过如果您想了解美食网站的技术架构，我很乐意分享！"
		}
	}

	// 天气类问题 - 精确匹配
	if strings.Contains(q, "天气") || strings.Contains(q, "weather") || strings.Contains(q, "下雨") || strings.Contains(q, "晴天") {
		return "🌤️ 我是技术FAQ助手，无法提供天气信息。\n\n💡 获取天气信息：\n• 查看手机天气应用\n• 访问天气预报网站\n• 询问语音助手\n\n有什么技术问题需要我帮助吗？"
	}

	// 时间类问题 - 精确匹配
	if strings.Contains(q, "几点") || strings.Contains(q, "现在时间") || q == "time" {
		return "⏰ 我是技术FAQ助手，无法提供时间信息。\n\n🕐 查看时间：\n• 看手机或电脑时钟\n• 询问语音助手\n\n请细说您想了解的技术问题！"
	}

	// 兜底处理 - 根据问题特征给出不同回应
	// 包含疑问词的问题
	if strings.Contains(q, "什么") || strings.Contains(q, "如何") || strings.Contains(q, "怎么") ||
		strings.Contains(q, "为什么") || strings.Contains(q, "哪个") || strings.Contains(q, "哪些") {
		return "🤔 请细说，希望能帮到您！我专注于技术领域：\n\n💻 我可以解答：\n• LocalAI和AI技术\n• MySQL数据库\n• 向量搜索\n• Go语言开发\n• 系统架构\n\n请详细描述您的技术问题～"
	}

	// 其他情况 - 通用兜底
	return "🤔 请细说，希望能帮到您！我是技术FAQ助手，请告诉我您想了解的具体技术问题。"
}

// GenerateFallbackAnswer 生成兜底回答 - 保持原有逻辑不变
func (g *Generator) GenerateFallbackAnswer(question string) string {
	q := strings.ToLower(strings.TrimSpace(question))

	// 根据问题类型给出不同的兜底回答
	if strings.Contains(q, "什么") || strings.Contains(q, "what") {
		return "🤔 这是个很好的问题！虽然我的专业领域是技术相关，但我建议：\n\n💡 如果是技术问题：\n• 尝试更具体地描述问题\n• 提供相关的技术背景\n• 我会尽力为您解答\n\n🌐 如果是其他问题：\n• 可以尝试搜索引擎\n• 咨询相关领域的专家\n• 或查看专业网站\n\n我专注于LocalAI、数据库、编程等技术话题！"
	}

	if strings.Contains(q, "如何") || strings.Contains(q, "怎么") || strings.Contains(q, "how") {
		return "🛠️ 看起来您需要操作指导！\n\n💻 如果是技术操作：\n• 请提供更多技术细节\n• 说明您的使用环境\n• 我会尽力提供技术指导\n\n📚 如果是其他操作：\n• 查看相关官方文档\n• 搜索专业教程\n• 咨询相关领域专家\n\n我擅长技术相关的操作指导！"
	}

	// 默认兜底回答
	return "😊 感谢您的提问！我是专注于技术领域的FAQ助手。\n\n🤖 我的专长领域：\n• LocalAI和AI技术\n• 数据库和存储\n• 编程语言和开发\n• 系统架构和部署\n\n🌟 如果您有技术问题：\n• 请尽量详细描述问题\n• 我会基于技术知识为您解答\n\n如果是其他领域的问题，建议咨询相关专业人士哦！"
}
