package database

import (
	"database/sql"
	"faq-system/internal/config"
	"faq-system/internal/logger"
	"fmt"
	"strings"

	_ "github.com/go-sql-driver/mysql"
)

// Initialize 初始化数据库
func Initialize(cfg *config.Config) error {
	logger.Info("开始初始化数据库...")

	// 1. 连接MySQL服务器（不指定数据库）- 使用原来的方法
	serverDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_general_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
	)

	logger.Infof("尝试连接MySQL服务器: %s:%s", cfg.MySQL.Host, cfg.MySQL.Port)
	db, err := sql.Open("mysql", serverDSN)
	if err != nil {
		return err
	}
	defer db.Close()

	if err := db.Ping(); err != nil {
		return err
	}
	logger.Info("成功连接到MySQL服务器")

	// 打印MySQL字符集配置信息
	printMySQLCharsetInfo(db)

	// 2. 创建数据库，使用utf8mb4_general_ci
	_, err = db.Exec("CREATE DATABASE IF NOT EXISTS " + cfg.MySQL.Database + " CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		return err
	}
	logger.Infof("数据库 '%s' 创建成功或已存在", cfg.MySQL.Database)

	// 设置会话字符集，确保一致性
	_, err = db.Exec("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		logger.Warnf("设置会话字符集失败: %v", err)
	}

	// 3. 连接到指定数据库 - 使用原来的方法
	dbDSN := fmt.Sprintf("%s:%s@tcp(%s:%s)/%s?charset=utf8mb4&parseTime=True&loc=Local&collation=utf8mb4_general_ci",
		cfg.MySQL.Username,
		cfg.MySQL.Password,
		cfg.MySQL.Host,
		cfg.MySQL.Port,
		cfg.MySQL.Database,
	)

	faqDB, err := sql.Open("mysql", dbDSN)
	if err != nil {
		return err
	}
	defer faqDB.Close()

	// 确保连接使用正确的字符集
	_, err = faqDB.Exec("SET NAMES utf8mb4 COLLATE utf8mb4_general_ci")
	if err != nil {
		logger.Warnf("设置数据库连接字符集失败: %v", err)
	}

	// 打印数据库连接的字符集配置信息
	logger.Infof("连接到数据库 '%s' 后的字符集配置:", cfg.MySQL.Database)
	printMySQLCharsetInfo(faqDB)

	// 4. FAQ表已移除 - 使用learned_knowledge表替代
	logger.Info("跳过FAQ表创建，使用learned_knowledge表")

	// 5. 跳过FAQ示例数据插入 - FAQ表已移除
	logger.Info("跳过FAQ示例数据插入，FAQ表已移除")

	// 5. 初始化学习相关表
	logger.Info("初始化学习相关表...")
	if err := initializeLearningTables(faqDB); err != nil {
		logger.Errorf("初始化学习表失败: %v", err)
		return fmt.Errorf("初始化学习表失败: %v", err)
	}
	logger.Info("学习相关表初始化完成")

	// 6. 初始化爬虫相关表
	logger.Info("初始化爬虫相关表...")
	if err := initializeCrawlerTables(faqDB); err != nil {
		logger.Errorf("初始化爬虫表失败: %v", err)
		return fmt.Errorf("初始化爬虫表失败: %v", err)
	}
	logger.Info("爬虫相关表初始化完成")

	logger.Info("数据库初始化完成")
	return nil
}

// insertSampleData 函数已移除 - FAQ表已移除

// initializeLearningTables 初始化学习相关表
func initializeLearningTables(db *sql.DB) error {
	// 学习表的SQL定义
	learningTables := []string{
		// 1. 用户查询记录表
		`CREATE TABLE IF NOT EXISTS user_queries (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			session_id VARCHAR(64) NOT NULL COMMENT '会话ID',
			user_id VARCHAR(64) NOT NULL COMMENT '用户ID',
			query_text TEXT NOT NULL COMMENT '用户查询内容',
			query_intent VARCHAR(50) COMMENT '识别的意图',
			query_type VARCHAR(50) COMMENT '问题类型',
			query_embedding JSON COMMENT '查询向量（JSON格式）',
			context_data JSON COMMENT '上下文信息',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_user_id (user_id),
			INDEX idx_session_id (session_id),
			INDEX idx_created_at (created_at),
			INDEX idx_intent (query_intent)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户查询记录表'`,

		// 2. 系统响应记录表
		`CREATE TABLE IF NOT EXISTS system_responses (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			matched_faq_id INT COMMENT '匹配的FAQ ID',
			response_text TEXT NOT NULL COMMENT '系统回答内容',
			response_source VARCHAR(100) COMMENT '回答来源',
			confidence_score FLOAT COMMENT '置信度得分',
			match_type VARCHAR(50) COMMENT '匹配类型',
			processing_time_ms INT COMMENT '处理时间（毫秒）',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_faq_id (matched_faq_id),
			INDEX idx_confidence (confidence_score)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统响应记录表'`,

		// 3. 用户反馈表
		`CREATE TABLE IF NOT EXISTS user_feedback (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			response_id BIGINT NOT NULL COMMENT '关联的响应ID',
			feedback_type ENUM('helpful', 'not_helpful', 'partially_helpful') NOT NULL COMMENT '反馈类型',
			rating INT CHECK (rating >= 1 AND rating <= 5) COMMENT '评分(1-5)',
			feedback_text TEXT COMMENT '文字反馈',
			improvement_suggestion TEXT COMMENT '改进建议',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			FOREIGN KEY (response_id) REFERENCES system_responses(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_response_id (response_id),
			INDEX idx_feedback_type (feedback_type),
			INDEX idx_rating (rating)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户反馈表'`,

		// 4. 用户行为追踪表
		`CREATE TABLE IF NOT EXISTS user_behaviors (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			query_id BIGINT NOT NULL COMMENT '关联的查询ID',
			behavior_type ENUM('click', 'scroll', 'copy', 'follow_up', 'exit') NOT NULL COMMENT '行为类型',
			behavior_data JSON COMMENT '行为详细数据',
			timestamp_ms BIGINT COMMENT '行为时间戳（毫秒）',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (query_id) REFERENCES user_queries(id) ON DELETE CASCADE,
			INDEX idx_query_id (query_id),
			INDEX idx_behavior_type (behavior_type),
			INDEX idx_timestamp (timestamp_ms)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户行为追踪表'`,

		// 5. 学习模式表
		`CREATE TABLE IF NOT EXISTS learning_patterns (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			pattern_type ENUM('query_similarity', 'intent_mapping', 'response_optimization', 'user_preference') NOT NULL COMMENT '模式类型',
			pattern_name VARCHAR(200) NOT NULL COMMENT '模式名称',
			pattern_data JSON NOT NULL COMMENT '模式数据',
			confidence FLOAT DEFAULT 0.0 COMMENT '模式置信度',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			last_updated TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_pattern_type (pattern_type),
			INDEX idx_confidence (confidence),
			INDEX idx_success_rate (success_rate),
			UNIQUE KEY uk_pattern_name (pattern_name)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习模式表'`,

		// 6. FAQ性能统计表已移除 - FAQ表已移除

		// 7. 系统学习配置表
		`CREATE TABLE IF NOT EXISTS learning_config (
			id INT AUTO_INCREMENT PRIMARY KEY,
			config_key VARCHAR(100) NOT NULL COMMENT '配置键',
			config_value TEXT NOT NULL COMMENT '配置值',
			config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
			description TEXT COMMENT '配置描述',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_config_key (config_key)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='系统学习配置表'`,

		// 8. 学习知识表 - 从create_knowledge_learning_tables.go集成
		`CREATE TABLE IF NOT EXISTS learned_knowledge (
			id INT AUTO_INCREMENT PRIMARY KEY,
			question TEXT NOT NULL COMMENT '学习到的问题',
			answer TEXT NOT NULL COMMENT '学习到的答案',
			source ENUM('user_input', 'conversation', 'correction', 'implicit', 'crawler', 'manual_input', 'api_input') NOT NULL COMMENT '知识来源',
			confidence FLOAT DEFAULT 0.0 COMMENT '知识置信度',
			category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
			keywords JSON COMMENT '关键词',
			context TEXT COMMENT '学习上下文',
			learned_from VARCHAR(100) COMMENT '学习来源用户',
			status ENUM('pending', 'approved', 'rejected', 'integrated') DEFAULT 'approved' COMMENT '状态',
			metadata JSON COMMENT '元数据',
			usage_count INT DEFAULT 0 COMMENT '使用次数',
			success_rate FLOAT DEFAULT 0.0 COMMENT '成功率',
			last_used TIMESTAMP NULL COMMENT '最后使用时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_source (source),
			INDEX idx_category (category),
			INDEX idx_status (status),
			INDEX idx_confidence (confidence),
			INDEX idx_learned_from (learned_from),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='学习知识表'`,

		// 9. 知识向量表
		`CREATE TABLE IF NOT EXISTS knowledge_vectors (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			vector_data JSON NOT NULL COMMENT '向量数据',
			vector_type ENUM('question', 'answer', 'combined') DEFAULT 'combined' COMMENT '向量类型',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_vector_type (vector_type),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识向量表'`,

		// 10. 知识反馈表
		`CREATE TABLE IF NOT EXISTS knowledge_feedback (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
			feedback_type ENUM('helpful', 'not_helpful', 'incorrect', 'incomplete') NOT NULL COMMENT '反馈类型',
			rating INT DEFAULT 0 COMMENT '评分 1-5',
			comment TEXT COMMENT '反馈评论',
			context TEXT COMMENT '反馈上下文',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_user_id (user_id),
			INDEX idx_feedback_type (feedback_type),
			INDEX idx_rating (rating),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识反馈表'`,

		// 11. 知识使用记录表
		`CREATE TABLE IF NOT EXISTS knowledge_usage (
			id INT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '知识ID',
			query_id BIGINT COMMENT '查询ID',
			user_id VARCHAR(100) NOT NULL COMMENT '用户ID',
			match_score FLOAT DEFAULT 0.0 COMMENT '匹配分数',
			was_helpful BOOLEAN DEFAULT NULL COMMENT '是否有帮助',
			response_time INT DEFAULT 0 COMMENT '响应时间(ms)',
			context TEXT COMMENT '使用上下文',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_query_id (query_id),
			INDEX idx_user_id (user_id),
			INDEX idx_match_score (match_score),
			INDEX idx_was_helpful (was_helpful),
			INDEX idx_created_at (created_at),
			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识使用记录表'`,
	}

	// 创建表
	for _, tableSQL := range learningTables {
		if _, err := db.Exec(tableSQL); err != nil {
			return fmt.Errorf("创建学习表失败: %v", err)
		}
	}

	// 插入默认学习配置
	defaultConfigs := []struct {
		key         string
		value       string
		configType  string
		description string
	}{
		{"learning_enabled", "true", "boolean", "是否启用学习功能"},
		{"min_feedback_threshold", "5", "int", "最小反馈数量阈值"},
		{"confidence_learning_rate", "0.1", "float", "置信度学习率"},
		{"pattern_update_interval", "3600", "int", "模式更新间隔（秒）"},
		{"max_learning_patterns", "1000", "int", "最大学习模式数量"},
		{"feedback_weight_positive", "1.0", "float", "正面反馈权重"},
		{"feedback_weight_negative", "2.0", "float", "负面反馈权重"},
		{"similarity_threshold", "0.8", "float", "相似度阈值"},
		{"auto_optimize_enabled", "true", "boolean", "是否启用自动优化"},
		{"learning_batch_size", "100", "int", "学习批次大小"},
	}

	for _, config := range defaultConfigs {
		_, err := db.Exec(`
			INSERT INTO learning_config (config_key, config_value, config_type, description)
			VALUES (?, ?, ?, ?)
			ON DUPLICATE KEY UPDATE
			config_value = VALUES(config_value),
			updated_at = CURRENT_TIMESTAMP
		`, config.key, config.value, config.configType, config.description)

		if err != nil {
			return fmt.Errorf("插入默认学习配置失败: %v", err)
		}
	}

	// 插入示例学习知识
	logger.Info("插入示例学习知识...")
	if err := insertSampleLearningKnowledge(db); err != nil {
		logger.Warnf("插入示例学习知识失败: %v", err)
	} else {
		logger.Info("示例学习知识插入成功")
	}

	logger.Info("学习相关表初始化完成")

	// 迁移knowledge_vectors表结构
	if err := migrateKnowledgeVectorsTable(db); err != nil {
		logger.Warnf("迁移knowledge_vectors表失败: %v", err)
	}

	// 迁移learned_knowledge表结构，添加知识点标志字段
	if err := migrateLearningKnowledgeTable(db); err != nil {
		logger.Warnf("迁移learned_knowledge表失败: %v", err)
	}

	// 初始化知识点管理表
	if err := initializeKnowledgeTopicsTable(db); err != nil {
		logger.Warnf("初始化知识点管理表失败: %v", err)
	}

	// 初始化向量化相关表
	if err := initializeCrawlerVectorizationTables(db); err != nil {
		logger.Warnf("初始化向量化表失败: %v", err)
	}

	return nil
}

// initializeCrawlerVectorizationTables 初始化向量化相关表
func initializeCrawlerVectorizationTables(db *sql.DB) error {
	logger.Info("初始化向量化相关表...")

	// 检查knowledge_vectors表是否存在，如果不存在则创建
	createKnowledgeVectorsTable := `
		CREATE TABLE IF NOT EXISTS knowledge_vectors (
			id BIGINT AUTO_INCREMENT PRIMARY KEY,
			knowledge_id INT NOT NULL COMMENT '关联的learned_knowledge ID',
			vector_data JSON COMMENT '向量数据',
			vector_type VARCHAR(50) DEFAULT 'combined' COMMENT '向量类型',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,

			UNIQUE KEY unique_knowledge_vector (knowledge_id, vector_type),
			INDEX idx_knowledge_id (knowledge_id),
			INDEX idx_vector_type (vector_type),

			FOREIGN KEY (knowledge_id) REFERENCES learned_knowledge(id) ON DELETE CASCADE
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识向量表'
	`

	if _, err := db.Exec(createKnowledgeVectorsTable); err != nil {
		return fmt.Errorf("创建knowledge_vectors表失败: %v", err)
	}

	logger.Info("✅ 向量化表初始化完成")
	return nil
}

// insertSampleLearningKnowledge 插入示例学习知识
func insertSampleLearningKnowledge(db *sql.DB) error {
	// 检查是否已有学习知识
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM learned_knowledge").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		logger.Infof("学习知识表中已有 %d 条数据，跳过示例数据插入", count)
		return nil
	}

	sampleKnowledge := []string{
		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是C#？', 'C#是微软开发的一种面向对象的编程语言，运行在.NET框架上。', 'user_input', 0.9, 'technology', '["c#", "编程语言", "微软", ".net"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是Python？', 'Python是一种高级编程语言，以其简洁的语法和强大的功能而闻名。', 'user_input', 0.9, 'technology', '["python", "编程语言", "高级语言"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是JavaScript？', 'JavaScript是一种主要用于网页开发的脚本语言，可以实现动态交互效果。', 'user_input', 0.9, 'technology', '["javascript", "脚本语言", "网页开发"]', '用户教学输入', 'system', 'approved')`,

		`INSERT IGNORE INTO learned_knowledge
		(question, answer, source, confidence, category, keywords, context, learned_from, status) VALUES
		('什么是Java？', 'Java是一种跨平台的面向对象编程语言，广泛用于企业级应用开发。', 'user_input', 0.9, 'technology', '["java", "编程语言", "跨平台", "企业级"]', '用户教学输入', 'system', 'approved')`,
	}

	for i, knowledgeSQL := range sampleKnowledge {
		if _, err := db.Exec(knowledgeSQL); err != nil {
			logger.Warnf("插入示例知识 %d 失败: %v", i+1, err)
		} else {
			logger.Infof("✅ 插入示例知识 %d/4", i+1)
		}
	}

	return nil
}

// initializeCrawlerTables 初始化爬虫相关表
func initializeCrawlerTables(db *sql.DB) error {
	// 爬虫表的SQL定义
	crawlerTables := []string{
		// 1. 爬取目标表
		`CREATE TABLE IF NOT EXISTS crawl_targets (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(200) NOT NULL COMMENT '目标名称',
			url TEXT NOT NULL COMMENT '目标URL',
			type ENUM('website', 'api', 'rss', 'search_engine') DEFAULT 'website' COMMENT '目标类型',
			category VARCHAR(100) DEFAULT 'general' COMMENT '知识分类',
			keywords JSON COMMENT '关键词',
			selectors JSON COMMENT 'CSS选择器配置',
			filters JSON COMMENT '过滤条件',
			schedule VARCHAR(50) DEFAULT '0 */6 * * *' COMMENT '调度表达式',
			enabled BOOLEAN DEFAULT TRUE COMMENT '是否启用',
			last_crawled TIMESTAMP NULL COMMENT '最后爬取时间',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_enabled (enabled),
			INDEX idx_type (type),
			INDEX idx_category (category),
			INDEX idx_last_crawled (last_crawled)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取目标表'`,

		// 2. 爬取结果表
		`CREATE TABLE IF NOT EXISTS crawl_results (
			id INT AUTO_INCREMENT PRIMARY KEY,
			target_id INT NOT NULL COMMENT '目标ID',
			url TEXT NOT NULL COMMENT '爬取URL',
			title VARCHAR(500) COMMENT '标题',
			content LONGTEXT COMMENT '内容',
			summary TEXT COMMENT '摘要',
			keywords JSON COMMENT '关键词',
			category VARCHAR(100) COMMENT '分类',
			metadata JSON COMMENT '元数据',
			crawled_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP COMMENT '爬取时间',
			processed_at TIMESTAMP NULL COMMENT '处理时间',
			status ENUM('pending', 'processed', 'failed') DEFAULT 'processed' COMMENT '状态',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
			INDEX idx_target_id (target_id),
			INDEX idx_status (status),
			INDEX idx_crawled_at (crawled_at),
			INDEX idx_processed_at (processed_at),
			INDEX idx_category (category)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取结果表'`,

		// 3. 爬取日志表
		`CREATE TABLE IF NOT EXISTS crawl_logs (
			id INT AUTO_INCREMENT PRIMARY KEY,
			target_id INT NOT NULL COMMENT '目标ID',
			url TEXT COMMENT '爬取URL',
			status ENUM('success', 'failed', 'timeout', 'error') NOT NULL COMMENT '状态',
			message TEXT COMMENT '日志消息',
			error_details TEXT COMMENT '错误详情',
			duration_ms INT COMMENT '耗时(毫秒)',
			response_code INT COMMENT 'HTTP响应码',
			response_size INT COMMENT '响应大小(字节)',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
			INDEX idx_target_id (target_id),
			INDEX idx_status (status),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取日志表'`,

		// 4. 爬虫配置表
		`CREATE TABLE IF NOT EXISTS crawler_config (
			id INT AUTO_INCREMENT PRIMARY KEY,
			config_key VARCHAR(100) NOT NULL COMMENT '配置键',
			config_value TEXT NOT NULL COMMENT '配置值',
			config_type ENUM('string', 'int', 'float', 'boolean', 'json') DEFAULT 'string' COMMENT '配置类型',
			description TEXT COMMENT '配置描述',
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			UNIQUE KEY uk_config_key (config_key)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬虫配置表'`,

		// 5. 爬取统计表
		`CREATE TABLE IF NOT EXISTS crawl_statistics (
			id INT AUTO_INCREMENT PRIMARY KEY,
			target_id INT NOT NULL COMMENT '目标ID',
			date DATE NOT NULL COMMENT '统计日期',
			total_crawls INT DEFAULT 0 COMMENT '总爬取次数',
			successful_crawls INT DEFAULT 0 COMMENT '成功爬取次数',
			failed_crawls INT DEFAULT 0 COMMENT '失败爬取次数',
			total_pages INT DEFAULT 0 COMMENT '总页面数',
			total_knowledge INT DEFAULT 0 COMMENT '提取知识数',
			avg_response_time INT DEFAULT 0 COMMENT '平均响应时间(ms)',
			total_data_size BIGINT DEFAULT 0 COMMENT '总数据大小(字节)',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			FOREIGN KEY (target_id) REFERENCES crawl_targets(id) ON DELETE CASCADE,
			UNIQUE KEY uk_target_date (target_id, date),
			INDEX idx_date (date)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='爬取统计表'`,
	}

	// 创建表
	for i, tableSQL := range crawlerTables {
		if _, err := db.Exec(tableSQL); err != nil {
			logger.Errorf("创建爬虫表 %d 失败: %v", i+1, err)
			return err
		}
		logger.Infof("✅ 创建爬虫表 %d/%d", i+1, len(crawlerTables))
	}

	// 插入默认配置
	logger.Info("插入爬虫默认配置...")
	if err := insertCrawlerDefaultConfig(db); err != nil {
		logger.Warnf("插入爬虫默认配置失败: %v", err)
	} else {
		logger.Info("爬虫默认配置插入成功")
	}

	// 插入示例爬取目标
	logger.Info("插入示例爬取目标...")
	if err := insertSampleCrawlTargets(db); err != nil {
		logger.Warnf("插入示例爬取目标失败: %v", err)
	} else {
		logger.Info("示例爬取目标插入成功")
	}

	// 创建数据模板相关表
	logger.Info("创建数据模板相关表...")
	if err := createDataTemplateTables(db); err != nil {
		logger.Errorf("创建数据模板表失败: %v", err)
		return err
	}

	return nil
}

// insertCrawlerDefaultConfig 插入爬虫默认配置
func insertCrawlerDefaultConfig(db *sql.DB) error {
	configs := []string{
		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('max_concurrency', '5', 'int', '最大并发爬取数')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('request_delay', '2000', 'int', '请求间隔(毫秒)')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('timeout', '30000', 'int', '请求超时(毫秒)')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('user_agent', 'FAQ-System-Crawler/1.0', 'string', 'User-Agent')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('max_retries', '3', 'int', '最大重试次数')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('enable_javascript', 'false', 'boolean', '是否启用JavaScript渲染')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('max_content_length', '1048576', 'int', '最大内容长度(字节)')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('enable_robots_txt', 'true', 'boolean', '是否遵守robots.txt')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('crawl_depth', '3', 'int', '最大爬取深度')`,

		`INSERT IGNORE INTO crawler_config (config_key, config_value, config_type, description) VALUES
		('enable_duplicate_filter', 'true', 'boolean', '是否启用重复过滤')`,
	}

	for i, configSQL := range configs {
		if _, err := db.Exec(configSQL); err != nil {
			logger.Warnf("插入爬虫配置 %d 失败: %v", i+1, err)
		} else {
			logger.Infof("✅ 插入爬虫配置 %d/%d", i+1, len(configs))
		}
	}

	return nil
}

// insertSampleCrawlTargets 插入示例爬取目标
func insertSampleCrawlTargets(db *sql.DB) error {
	// 检查是否已有爬取目标
	var count int
	err := db.QueryRow("SELECT COUNT(*) FROM crawl_targets").Scan(&count)
	if err != nil {
		return err
	}

	if count > 0 {
		logger.Infof("爬取目标表中已有 %d 条数据，跳过示例数据插入", count)
		return nil
	}

	targets := []string{
		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('测试API - 每分钟', 'https://httpbin.org/json', 'api', 'test',
		 '["test", "api", "json", "httpbin"]',
		 '{"title": "title", "content": "body"}',
		 '* * * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('Python官方文档', 'https://docs.python.org/3/', 'website', 'technology',
		 '["python", "documentation", "programming"]',
		 '{"title": "title", "content": ".body"}',
		 '0 0 * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('GitHub Trending', 'https://github.com/trending', 'website', 'technology',
		 '["github", "trending", "opensource"]',
		 '{"title": "h1", "content": ".Box-row"}',
		 '0 */6 * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('Stack Overflow Python', 'https://stackoverflow.com/questions/tagged/python', 'website', 'technology',
		 '["python", "stackoverflow", "qa"]',
		 '{"title": ".question-hyperlink", "content": ".post-text"}',
		 '0 */4 * * *', false)`,

		`INSERT IGNORE INTO crawl_targets (name, url, type, category, keywords, selectors, schedule, enabled) VALUES
		('MDN Web Docs', 'https://developer.mozilla.org/en-US/docs/Web/JavaScript', 'website', 'technology',
		 '["javascript", "mdn", "web", "documentation"]',
		 '{"title": "h1", "content": ".main-page-content"}',
		 '0 0 * * *', false)`,
	}

	for i, targetSQL := range targets {
		if _, err := db.Exec(targetSQL); err != nil {
			logger.Warnf("插入示例爬取目标 %d 失败: %v", i+1, err)
		} else {
			logger.Infof("✅ 插入示例爬取目标 %d/%d", i+1, len(targets))
		}
	}

	return nil
}

// printMySQLCharsetInfo 打印MySQL字符集配置信息
func printMySQLCharsetInfo(db *sql.DB) {
	logger.Info("=== MySQL字符集配置信息 ===")

	// 查询字符集变量
	charsetVars := []string{
		"character_set_client",
		"character_set_connection",
		"character_set_results",
	}

	for _, varName := range charsetVars {
		var variable, value string
		query := fmt.Sprintf("SHOW VARIABLES LIKE '%s'", varName)
		err := db.QueryRow(query).Scan(&variable, &value)
		if err != nil {
			logger.Warnf("查询 %s 失败: %v", varName, err)
			continue
		}
		logger.Infof("%-25s: %s", variable, value)
	}

	logger.Info("=== 字符集配置信息结束 ===")
}

// migrateKnowledgeVectorsTable 迁移knowledge_vectors表结构，添加updated_at字段
func migrateKnowledgeVectorsTable(db *sql.DB) error {
	// 检查updated_at字段是否存在
	checkColumnQuery := `
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.COLUMNS
		WHERE TABLE_SCHEMA = DATABASE()
		AND TABLE_NAME = 'knowledge_vectors'
		AND COLUMN_NAME = 'updated_at'
	`

	var columnExists int
	err := db.QueryRow(checkColumnQuery).Scan(&columnExists)
	if err != nil {
		return fmt.Errorf("检查updated_at字段失败: %v", err)
	}

	// 如果字段不存在，则添加
	if columnExists == 0 {
		logger.Info("为knowledge_vectors表添加updated_at字段...")
		addColumnQuery := `
			ALTER TABLE knowledge_vectors
			ADD COLUMN updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
		`

		_, err = db.Exec(addColumnQuery)
		if err != nil {
			return fmt.Errorf("添加updated_at字段失败: %v", err)
		}

		logger.Info("✅ knowledge_vectors表updated_at字段添加成功")
	} else {
		logger.Info("knowledge_vectors表updated_at字段已存在，跳过迁移")
	}

	return nil
}

// migrateLearningKnowledgeTable 迁移learned_knowledge表结构，添加知识点标志字段
func migrateLearningKnowledgeTable(db *sql.DB) error {
	logger.Info("检查learned_knowledge表结构...")

	// 检查knowledge_topic字段是否存在
	checkColumnQuery := `
		SELECT COUNT(*)
		FROM INFORMATION_SCHEMA.COLUMNS
		WHERE TABLE_SCHEMA = DATABASE()
		AND TABLE_NAME = 'learned_knowledge'
		AND COLUMN_NAME = 'knowledge_topic'
	`

	var columnExists int
	err := db.QueryRow(checkColumnQuery).Scan(&columnExists)
	if err != nil {
		return fmt.Errorf("检查knowledge_topic字段失败: %v", err)
	}

	// 如果字段不存在，则添加
	if columnExists == 0 {
		logger.Info("为learned_knowledge表添加knowledge_topic字段...")
		addColumnQuery := `
			ALTER TABLE learned_knowledge
			ADD COLUMN knowledge_topic VARCHAR(100) DEFAULT NULL COMMENT '知识点标志，用于关联相关问题'
		`

		_, err = db.Exec(addColumnQuery)
		if err != nil {
			return fmt.Errorf("添加knowledge_topic字段失败: %v", err)
		}

		logger.Info("✅ learned_knowledge表knowledge_topic字段添加成功")

		// 添加索引
		logger.Info("为knowledge_topic字段添加索引...")
		addIndexQuery := `CREATE INDEX idx_knowledge_topic ON learned_knowledge(knowledge_topic)`
		_, err = db.Exec(addIndexQuery)
		if err != nil {
			logger.Warnf("添加knowledge_topic索引失败: %v", err)
		} else {
			logger.Info("✅ knowledge_topic索引添加成功")
		}

		// 自动检测并更新现有知识的知识点标志
		logger.Info("自动检测现有知识的知识点标志...")
		if err := autoDetectKnowledgeTopics(db); err != nil {
			logger.Warnf("自动检测知识点标志失败: %v", err)
		}
	} else {
		logger.Info("learned_knowledge表knowledge_topic字段已存在，跳过迁移")
	}

	return nil
}

// autoDetectKnowledgeTopics 自动检测现有知识的知识点标志
func autoDetectKnowledgeTopics(db *sql.DB) error {
	// 定义知识点关键词映射
	topicMappings := []struct {
		topic    string
		keywords []string
	}{
		{"React框架", []string{"react", "jsx", "组件", "state", "props", "hook", "虚拟dom"}},
		{"Vue.js框架", []string{"vue", "vuejs", "vue.js", "组件", "指令", "响应式", "mvvm"}},
		{"Node.js", []string{"node", "nodejs", "node.js", "npm", "服务器", "后端", "express"}},
		{"JavaScript基础", []string{"javascript", "js", "ecmascript", "变量", "函数", "对象", "数组"}},
		{"Python基础", []string{"python", "py", "列表", "字典", "函数", "类", "模块"}},
		{"Java基础", []string{"java", "class", "object", "继承", "多态", "封装", "jvm"}},
		{"Go语言", []string{"go", "golang", "goroutine", "channel", "interface", "struct"}},
		{"数据库设计", []string{"数据库", "mysql", "sql", "表", "索引", "查询", "关系型"}},
		{"机器学习", []string{"机器学习", "算法", "模型", "训练", "预测", "分类", "回归"}},
		{"深度学习", []string{"深度学习", "神经网络", "cnn", "rnn", "lstm", "transformer"}},
	}

	updateCount := 0
	for _, mapping := range topicMappings {
		// 构建WHERE条件
		conditions := make([]string, len(mapping.keywords))
		for i, keyword := range mapping.keywords {
			conditions[i] = fmt.Sprintf("(LOWER(question) LIKE '%%%s%%' OR LOWER(answer) LIKE '%%%s%%')", keyword, keyword)
		}
		whereClause := strings.Join(conditions, " OR ")

		// 更新SQL
		updateQuery := fmt.Sprintf(`
			UPDATE learned_knowledge
			SET knowledge_topic = ?
			WHERE (%s) AND knowledge_topic IS NULL
		`, whereClause)

		result, err := db.Exec(updateQuery, mapping.topic)
		if err != nil {
			logger.Warnf("更新知识点标志 '%s' 失败: %v", mapping.topic, err)
			continue
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected > 0 {
			logger.Infof("✅ 为 %d 条知识设置知识点标志: %s", rowsAffected, mapping.topic)
			updateCount += int(rowsAffected)
		}
	}

	logger.Infof("📊 总共为 %d 条知识自动设置了知识点标志", updateCount)
	return nil
}

// initializeKnowledgeTopicsTable 初始化知识点管理表
func initializeKnowledgeTopicsTable(db *sql.DB) error {
	logger.Info("初始化知识点管理表...")

	// 创建知识点管理表
	createTableQuery := `
		CREATE TABLE IF NOT EXISTS knowledge_topics (
			id INT AUTO_INCREMENT PRIMARY KEY,
			topic_name VARCHAR(100) NOT NULL UNIQUE COMMENT '知识点名称',
			topic_description TEXT COMMENT '知识点描述',
			category VARCHAR(50) COMMENT '知识点分类',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_topic_name (topic_name),
			INDEX idx_category (category)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='知识点管理表'
	`

	_, err := db.Exec(createTableQuery)
	if err != nil {
		return fmt.Errorf("创建knowledge_topics表失败: %v", err)
	}

	logger.Info("✅ knowledge_topics表创建成功")

	// 插入示例知识点
	logger.Info("插入示例知识点...")
	sampleTopics := []struct {
		name        string
		description string
		category    string
	}{
		{"JavaScript基础", "JavaScript编程语言的基础概念和语法", "technology"},
		{"React框架", "React前端框架相关知识", "technology"},
		{"Vue.js框架", "Vue.js前端框架相关知识", "technology"},
		{"Node.js", "Node.js服务端JavaScript运行环境", "technology"},
		{"Python基础", "Python编程语言基础知识", "technology"},
		{"Java基础", "Java编程语言基础知识", "technology"},
		{"Go语言", "Go编程语言相关知识", "technology"},
		{"数据库设计", "数据库设计原理和最佳实践", "technology"},
		{"机器学习", "机器学习算法和应用", "science"},
		{"深度学习", "深度学习和神经网络", "science"},
	}

	insertCount := 0
	for _, topic := range sampleTopics {
		insertQuery := `
			INSERT IGNORE INTO knowledge_topics (topic_name, topic_description, category)
			VALUES (?, ?, ?)
		`
		result, err := db.Exec(insertQuery, topic.name, topic.description, topic.category)
		if err != nil {
			logger.Warnf("插入知识点 '%s' 失败: %v", topic.name, err)
			continue
		}

		rowsAffected, _ := result.RowsAffected()
		if rowsAffected > 0 {
			insertCount++
			logger.Infof("✅ 插入知识点: %s", topic.name)
		}
	}

	logger.Infof("📊 成功插入 %d 个示例知识点", insertCount)
	return nil
}

// createDataTemplateTables 创建数据模板相关表
func createDataTemplateTables(db *sql.DB) error {
	// 数据模板相关表
	templateTables := []string{
		// 1. 数据模板定义表
		`CREATE TABLE IF NOT EXISTS data_templates (
			id VARCHAR(50) PRIMARY KEY COMMENT '模板ID',
			name VARCHAR(100) NOT NULL COMMENT '模板名称',
			description TEXT COMMENT '模板描述',
			category VARCHAR(50) NOT NULL COMMENT '模板分类',
			keywords JSON COMMENT '关键词列表',
			fields JSON NOT NULL COMMENT '字段定义',
			sql_template TEXT COMMENT 'SQL模板',
			db_config JSON COMMENT '数据库配置',
			validation JSON COMMENT '验证规则',
			is_active BOOLEAN DEFAULT TRUE COMMENT '是否启用',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_category (category),
			INDEX idx_is_active (is_active)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='数据模板定义表'`,

		// 2. 模板会话表
		`CREATE TABLE IF NOT EXISTS template_sessions (
			id VARCHAR(100) PRIMARY KEY COMMENT '会话ID',
			template_id VARCHAR(50) NOT NULL COMMENT '模板ID',
			user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
			current_field INT DEFAULT 0 COMMENT '当前字段索引',
			collected_data JSON COMMENT '已收集的数据',
			status ENUM('collecting', 'completed', 'cancelled') DEFAULT 'collecting' COMMENT '会话状态',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			FOREIGN KEY (template_id) REFERENCES data_templates(id) ON DELETE CASCADE,
			INDEX idx_template_id (template_id),
			INDEX idx_user_id (user_id),
			INDEX idx_status (status),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板会话表'`,

		// 3. 模板执行历史表
		`CREATE TABLE IF NOT EXISTS template_executions (
			id INT AUTO_INCREMENT PRIMARY KEY,
			session_id VARCHAR(100) NOT NULL COMMENT '会话ID',
			template_id VARCHAR(50) NOT NULL COMMENT '模板ID',
			user_id VARCHAR(50) NOT NULL COMMENT '用户ID',
			collected_data JSON NOT NULL COMMENT '收集的数据',
			generated_sql JSON COMMENT '生成的SQL语句',
			execution_result JSON COMMENT '执行结果',
			status ENUM('success', 'failed', 'partial') NOT NULL COMMENT '执行状态',
			error_message TEXT COMMENT '错误信息',
			execution_time_ms INT COMMENT '执行耗时(毫秒)',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (session_id) REFERENCES template_sessions(id) ON DELETE CASCADE,
			FOREIGN KEY (template_id) REFERENCES data_templates(id) ON DELETE CASCADE,
			INDEX idx_session_id (session_id),
			INDEX idx_template_id (template_id),
			INDEX idx_user_id (user_id),
			INDEX idx_status (status),
			INDEX idx_created_at (created_at)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='模板执行历史表'`,

		// 4. 车场配置表（示例业务表）
		`CREATE TABLE IF NOT EXISTS parking_config (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(100) NOT NULL COMMENT '车场名称',
			area_type ENUM('仅外场', '内外场都有') NOT NULL COMMENT '区域类型',
			total_areas INT NOT NULL COMMENT '总区域数',
			area_details TEXT COMMENT '区域详情',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_name (name),
			INDEX idx_area_type (area_type)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='车场配置表'`,

		// 5. 车场区域表
		`CREATE TABLE IF NOT EXISTS parking_areas (
			id INT AUTO_INCREMENT PRIMARY KEY,
			parking_id INT NOT NULL COMMENT '车场ID',
			area_name VARCHAR(100) NOT NULL COMMENT '区域名称',
			area_type ENUM('内场', '外场') NOT NULL COMMENT '区域类型',
			lane_count INT NOT NULL COMMENT '车道数量',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (parking_id) REFERENCES parking_config(id) ON DELETE CASCADE,
			INDEX idx_parking_id (parking_id),
			INDEX idx_area_type (area_type)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='车场区域表'`,

		// 6. 车道表
		`CREATE TABLE IF NOT EXISTS parking_lanes (
			id INT AUTO_INCREMENT PRIMARY KEY,
			area_id INT NOT NULL COMMENT '区域ID',
			lane_number INT NOT NULL COMMENT '车道编号',
			camera_count INT DEFAULT 1 COMMENT '相机数量',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			FOREIGN KEY (area_id) REFERENCES parking_areas(id) ON DELETE CASCADE,
			INDEX idx_area_id (area_id),
			INDEX idx_lane_number (lane_number)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='车道表'`,

		// 7. 用户表（示例业务表）
		`CREATE TABLE IF NOT EXISTS users (
			id INT AUTO_INCREMENT PRIMARY KEY,
			username VARCHAR(50) NOT NULL UNIQUE COMMENT '用户名',
			real_name VARCHAR(100) NOT NULL COMMENT '真实姓名',
			email VARCHAR(100) NOT NULL COMMENT '邮箱',
			role ENUM('管理员', '操作员', '查看者', '审核员') NOT NULL COMMENT '角色',
			department VARCHAR(100) COMMENT '部门',
			status ENUM('active', 'inactive', 'suspended') DEFAULT 'active' COMMENT '状态',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_username (username),
			INDEX idx_role (role),
			INDEX idx_status (status)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='用户表'`,

		// 8. 设备表（示例业务表）
		`CREATE TABLE IF NOT EXISTS devices (
			id INT AUTO_INCREMENT PRIMARY KEY,
			name VARCHAR(100) NOT NULL COMMENT '设备名称',
			type ENUM('摄像头', '传感器', '服务器', '网络设备', '存储设备', '其他') NOT NULL COMMENT '设备类型',
			location VARCHAR(200) NOT NULL COMMENT '安装位置',
			ip_address VARCHAR(45) COMMENT 'IP地址',
			status ENUM('active', 'inactive', 'maintenance', 'error') DEFAULT 'active' COMMENT '设备状态',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_name (name),
			INDEX idx_type (type),
			INDEX idx_status (status),
			INDEX idx_ip_address (ip_address)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci COMMENT='设备表'`,
	}

	// 创建表
	for i, tableSQL := range templateTables {
		if _, err := db.Exec(tableSQL); err != nil {
			logger.Errorf("创建数据模板表 %d 失败: %v", i+1, err)
			return err
		}
		logger.Infof("✅ 创建数据模板表 %d/%d", i+1, len(templateTables))
	}

	logger.Info("✅ 数据模板相关表创建完成")
	return nil
}
