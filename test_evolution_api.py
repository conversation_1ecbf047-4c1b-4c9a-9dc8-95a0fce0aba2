#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FAQ系统的进化改进API和知识学习持久化
"""

import requests
import json
import time
import mysql.connector
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/api/v1/ask"
FEEDBACK_ENDPOINT = f"{SERVER_URL}/feedback"
EVOLUTION_ENDPOINT = f"{SERVER_URL}/api/learning/evolution/apply"

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root123',
    'database': 'faq_system',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return mysql.connector.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def create_test_interactions():
    """创建测试用的用户交互数据"""
    print("📝 创建测试用户交互数据...")
    
    # 模拟一些高质量的用户交互
    test_interactions = [
        {
            "question": "什么是微服务架构？",
            "good_answer": "微服务架构是一种将单一应用程序开发为一套小服务的方法，每个服务运行在自己的进程中，并使用轻量级机制（通常是HTTP API）进行通信。",
            "feedback_type": "positive",
            "rating": 5
        },
        {
            "question": "如何实现Redis缓存？",
            "good_answer": "Redis缓存实现包括：1)安装Redis服务 2)选择合适的数据结构 3)设置过期时间 4)实现缓存策略（如LRU）5)处理缓存穿透和雪崩问题。",
            "feedback_type": "positive", 
            "rating": 4
        },
        {
            "question": "什么是JWT令牌？",
            "bad_answer": "JWT是一种加密技术。",
            "correction": "JWT（JSON Web Token）是一种开放标准，用于在各方之间安全地传输信息。它由三部分组成：头部、载荷和签名，常用于身份验证和信息交换。",
            "feedback_type": "correction",
            "rating": 2
        }
    ]
    
    conn = get_db_connection()
    if not conn:
        return False
    
    try:
        cursor = conn.cursor()
        
        for interaction in test_interactions:
            # 插入用户查询
            query_sql = """
            INSERT INTO user_queries (query_text, user_id, query_intent, created_at)
            VALUES (%s, %s, %s, NOW())
            """
            cursor.execute(query_sql, (
                interaction["question"],
                "test_user_001",
                "technical"
            ))
            query_id = cursor.lastrowid
            
            # 插入系统响应
            if interaction["feedback_type"] == "correction":
                response_content = interaction["bad_answer"]
                confidence = 0.3
            else:
                response_content = interaction["good_answer"]
                confidence = 0.9
                
            response_sql = """
            INSERT INTO system_responses (query_id, response_content, confidence_score, created_at)
            VALUES (%s, %s, %s, NOW())
            """
            cursor.execute(response_sql, (query_id, response_content, confidence))
            response_id = cursor.lastrowid
            
            # 插入用户反馈
            feedback_content = interaction.get("correction", "")
            feedback_sql = """
            INSERT INTO user_feedback (response_id, feedback_type, feedback_content, rating, created_at)
            VALUES (%s, %s, %s, %s, NOW())
            """
            cursor.execute(feedback_sql, (
                response_id,
                interaction["feedback_type"],
                feedback_content,
                interaction["rating"]
            ))
        
        conn.commit()
        print(f"✅ 成功创建了 {len(test_interactions)} 条测试交互数据")
        return True
        
    except Exception as e:
        print(f"❌ 创建测试数据失败: {e}")
        conn.rollback()
        return False
    finally:
        conn.close()

def check_database_before():
    """检查进化改进前的数据库状态"""
    print("\n📊 检查进化改进前的数据库状态...")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 检查learned_knowledge表
        cursor.execute("SELECT COUNT(*) FROM learned_knowledge")
        learned_count = cursor.fetchone()[0]
        
        # 检查knowledge_vectors表
        cursor.execute("SELECT COUNT(*) FROM knowledge_vectors")
        vector_count = cursor.fetchone()[0]
        
        # 检查用户交互数据
        cursor.execute("SELECT COUNT(*) FROM user_queries WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)")
        recent_queries = cursor.fetchone()[0]
        
        cursor.execute("SELECT COUNT(*) FROM user_feedback WHERE created_at > DATE_SUB(NOW(), INTERVAL 1 HOUR)")
        recent_feedback = cursor.fetchone()[0]
        
        print(f"📚 learned_knowledge表: {learned_count} 条记录")
        print(f"🔢 knowledge_vectors表: {vector_count} 条记录")
        print(f"❓ 最近1小时用户查询: {recent_queries} 条")
        print(f"💬 最近1小时用户反馈: {recent_feedback} 条")
        
    except Exception as e:
        print(f"❌ 检查数据库状态失败: {e}")
    finally:
        conn.close()

def trigger_evolution():
    """触发进化改进"""
    print("\n🚀 触发进化改进...")
    
    try:
        response = requests.post(EVOLUTION_ENDPOINT,
                               headers={"Content-Type": "application/json"},
                               timeout=30)
        
        if response.status_code == 200:
            data = response.json()
            print(f"✅ 进化改进已触发: {data.get('message', '')}")
            print(f"⏰ 时间戳: {data.get('timestamp', '')}")
            return True
        else:
            print(f"❌ 触发进化改进失败: {response.status_code}")
            print(f"错误信息: {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def check_database_after():
    """检查进化改进后的数据库状态"""
    print("\n📊 检查进化改进后的数据库状态...")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 检查learned_knowledge表
        cursor.execute("SELECT COUNT(*) FROM learned_knowledge")
        learned_count = cursor.fetchone()[0]
        
        # 检查最新的学习知识
        cursor.execute("""
            SELECT id, question, answer, source, confidence, category, status, created_at
            FROM learned_knowledge 
            ORDER BY created_at DESC 
            LIMIT 5
        """)
        recent_knowledge = cursor.fetchall()
        
        # 检查knowledge_vectors表
        cursor.execute("SELECT COUNT(*) FROM knowledge_vectors")
        vector_count = cursor.fetchone()[0]
        
        # 检查最新的向量
        cursor.execute("""
            SELECT kv.knowledge_id, lk.question, LENGTH(kv.vector_data) as vector_size, kv.created_at
            FROM knowledge_vectors kv
            JOIN learned_knowledge lk ON kv.knowledge_id = lk.id
            ORDER BY kv.created_at DESC 
            LIMIT 5
        """)
        recent_vectors = cursor.fetchall()
        
        print(f"📚 learned_knowledge表: {learned_count} 条记录")
        print(f"🔢 knowledge_vectors表: {vector_count} 条记录")
        
        if recent_knowledge:
            print(f"\n📖 最新学习的知识 ({len(recent_knowledge)} 条):")
            for i, knowledge in enumerate(recent_knowledge, 1):
                print(f"  {i}. ID={knowledge[0]} | {knowledge[1][:50]}...")
                print(f"     来源: {knowledge[3]} | 置信度: {knowledge[4]} | 状态: {knowledge[6]}")
        
        if recent_vectors:
            print(f"\n🔢 最新生成的向量 ({len(recent_vectors)} 条):")
            for i, vector in enumerate(recent_vectors, 1):
                print(f"  {i}. 知识ID={vector[0]} | 向量大小: {vector[2]} 字节")
                print(f"     问题: {vector[1][:50]}...")
        
    except Exception as e:
        print(f"❌ 检查数据库状态失败: {e}")
    finally:
        conn.close()

def test_learned_knowledge():
    """测试学习到的知识是否能被正确使用"""
    print("\n🧪 测试学习到的知识...")
    
    test_questions = [
        "微服务架构的特点是什么？",
        "Redis缓存怎么用？",
        "JWT的组成部分有哪些？"
    ]
    
    for question in test_questions:
        print(f"\n🔍 测试问题: {question}")
        try:
            response = requests.post(ASK_ENDPOINT,
                                   json={"question": question},
                                   headers={"Content-Type": "application/json"},
                                   timeout=10)
            
            if response.status_code == 200:
                data = response.json()
                answer = data.get("answer", "")
                confidence = data.get("confidence", 0)
                print(f"✅ 回答: {answer[:100]}...")
                print(f"📊 置信度: {confidence:.2f}")
            else:
                print(f"❌ 请求失败: {response.status_code}")
                
        except Exception as e:
            print(f"❌ 请求异常: {e}")
        
        time.sleep(1)

def main():
    """主测试函数"""
    print("🧠 FAQ系统进化改进和知识学习持久化测试")
    print("=" * 60)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 1. 创建测试数据
    if not create_test_interactions():
        print("❌ 创建测试数据失败，退出测试")
        return
    
    # 2. 检查初始状态
    check_database_before()
    
    # 3. 触发进化改进
    if not trigger_evolution():
        print("❌ 触发进化改进失败，退出测试")
        return
    
    # 4. 等待进化改进完成
    print("\n⏳ 等待进化改进完成...")
    time.sleep(10)  # 给系统时间处理
    
    # 5. 检查改进后状态
    check_database_after()
    
    # 6. 测试学习效果
    test_learned_knowledge()
    
    print(f"\n⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 进化改进和知识学习持久化测试完成！")

if __name__ == "__main__":
    main()
