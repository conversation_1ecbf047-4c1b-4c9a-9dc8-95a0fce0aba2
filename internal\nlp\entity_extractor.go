package nlp

import (
	"log"
	"regexp"
	"strings"
)

// EntityExtractor 实体抽取器
type EntityExtractor struct {
	patterns     map[string][]*regexp.Regexp
	dictionaries map[string][]string
}

// ExtractedEntity 抽取的实体
type ExtractedEntity struct {
	Text       string                 `json:"text"`
	Type       string                 `json:"type"`
	Confidence float64                `json:"confidence"`
	Position   EntityPosition         `json:"position"`
	Attributes map[string]interface{} `json:"attributes"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// EntityPosition 实体位置
type EntityPosition struct {
	Start int `json:"start"`
	End   int `json:"end"`
}

// NewEntityExtractor 创建实体抽取器
func NewEntityExtractor() *EntityExtractor {
	log.Printf("🏷️ 初始化实体抽取器...")

	ee := &EntityExtractor{
		patterns:     make(map[string][]*regexp.Regexp),
		dictionaries: make(map[string][]string),
	}

	ee.initializePatterns()
	ee.initializeDictionaries()

	log.Printf("✅ 实体抽取器初始化完成")
	return ee
}

// initializePatterns 初始化模式
func (ee *EntityExtractor) initializePatterns() {
	// 时间实体
	ee.addPattern("TIME", `\d{4}年\d{1,2}月\d{1,2}日`)
	ee.addPattern("TIME", `\d{1,2}:\d{2}(:\d{2})?`)
	ee.addPattern("TIME", `\d{4}-\d{2}-\d{2}`)
	ee.addPattern("TIME", `(今天|明天|昨天|后天|前天)`)
	ee.addPattern("TIME", `(上午|下午|晚上|凌晨)\d{1,2}点`)

	// 数字实体
	ee.addPattern("NUMBER", `\d+(\.\d+)?%`)
	ee.addPattern("NUMBER", `\d+(\.\d+)?(万|千|百|十)?`)
	ee.addPattern("NUMBER", `第\d+`)

	// 货币实体
	ee.addPattern("MONEY", `\d+(\.\d+)?(元|块|毛|分)`)
	ee.addPattern("MONEY", `\$\d+(\.\d+)?`)
	ee.addPattern("MONEY", `¥\d+(\.\d+)?`)

	// 邮箱实体
	ee.addPattern("EMAIL", `[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}`)

	// 电话实体
	ee.addPattern("PHONE", `1[3-9]\d{9}`)
	ee.addPattern("PHONE", `\d{3,4}-\d{7,8}`)
	ee.addPattern("PHONE", `\(\d{3,4}\)\d{7,8}`)

	// URL实体
	ee.addPattern("URL", `https?://[^\s]+`)
	ee.addPattern("URL", `www\.[^\s]+`)

	// IP地址
	ee.addPattern("IP", `\d{1,3}\.\d{1,3}\.\d{1,3}\.\d{1,3}`)

	// 版本号
	ee.addPattern("VERSION", `v?\d+\.\d+(\.\d+)?`)

	// 文件路径
	ee.addPattern("PATH", `[a-zA-Z]:\\[^\s]+`)
	ee.addPattern("PATH", `/[^\s]+`)

	// 技术术语
	ee.addPattern("TECH", `[A-Z]{2,}`) // 大写缩写
	ee.addPattern("TECH", `\w+\.(js|py|go|java|cpp|html|css|json|xml|sql)`)

	// 人名（简单模式）
	ee.addPattern("PERSON", `[A-Z][a-z]+ [A-Z][a-z]+`) // 英文名
	ee.addPattern("PERSON", `[一-龯]{2,4}(先生|女士|老师|教授|博士|经理|总监|CEO|CTO)`)

	// 地名
	ee.addPattern("LOCATION", `[一-龯]{2,}(市|省|县|区|镇|村|路|街|道|号)`)
	ee.addPattern("LOCATION", `[A-Z][a-z]+(, [A-Z][a-z]+)*`) // 英文地名

	// 组织机构
	ee.addPattern("ORGANIZATION", `[一-龯]{2,}(公司|企业|集团|有限公司|股份有限公司|科技|技术)`)
	ee.addPattern("ORGANIZATION", `[A-Z][a-zA-Z]+ (Inc|Corp|Ltd|LLC|Co)\.?`)
}

// initializeDictionaries 初始化词典
func (ee *EntityExtractor) initializeDictionaries() {
	// 编程语言
	ee.dictionaries["PROGRAMMING_LANGUAGE"] = []string{
		"Python", "Java", "JavaScript", "Go", "C++", "C#", "PHP", "Ruby", "Swift",
		"Kotlin", "Rust", "TypeScript", "Scala", "R", "MATLAB", "Perl", "Lua",
	}

	// 技术框架
	ee.dictionaries["FRAMEWORK"] = []string{
		"React", "Vue", "Angular", "Django", "Flask", "Spring", "Express",
		"Laravel", "Rails", "ASP.NET", "jQuery", "Bootstrap", "TensorFlow",
		"PyTorch", "Keras", "Scikit-learn", "Pandas", "NumPy",
	}

	// 数据库
	ee.dictionaries["DATABASE"] = []string{
		"MySQL", "PostgreSQL", "MongoDB", "Redis", "SQLite", "Oracle",
		"SQL Server", "Cassandra", "Elasticsearch", "InfluxDB",
	}

	// 操作系统
	ee.dictionaries["OS"] = []string{
		"Windows", "Linux", "macOS", "Ubuntu", "CentOS", "Debian",
		"Android", "iOS", "Unix", "FreeBSD",
	}

	// 云服务
	ee.dictionaries["CLOUD"] = []string{
		"AWS", "Azure", "Google Cloud", "阿里云", "腾讯云", "华为云",
		"Docker", "Kubernetes", "Jenkins", "GitLab", "GitHub",
	}

	// 职位
	ee.dictionaries["JOB_TITLE"] = []string{
		"工程师", "开发者", "程序员", "架构师", "技术总监", "CTO", "CEO",
		"产品经理", "项目经理", "测试工程师", "运维工程师", "数据分析师",
		"算法工程师", "前端工程师", "后端工程师", "全栈工程师",
	}

	// 学历
	ee.dictionaries["EDUCATION"] = []string{
		"博士", "硕士", "学士", "本科", "专科", "高中", "初中",
		"PhD", "Master", "Bachelor", "MBA",
	}

	// 时间单位
	ee.dictionaries["TIME_UNIT"] = []string{
		"秒", "分钟", "小时", "天", "周", "月", "年", "季度",
		"second", "minute", "hour", "day", "week", "month", "year",
	}
}

// addPattern 添加模式
func (ee *EntityExtractor) addPattern(entityType string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	ee.patterns[entityType] = append(ee.patterns[entityType], regex)
}

// ExtractEntities 抽取实体
func (ee *EntityExtractor) ExtractEntities(text string) []ExtractedEntity {
	var entities []ExtractedEntity

	if strings.TrimSpace(text) == "" {
		return entities
	}

	// 1. 基于正则模式的抽取
	entities = append(entities, ee.extractByPatterns(text)...)

	// 2. 基于词典的抽取
	entities = append(entities, ee.extractByDictionaries(text)...)

	// 3. 去重和排序
	entities = ee.deduplicateEntities(entities)

	return entities
}

// extractByPatterns 基于模式抽取
func (ee *EntityExtractor) extractByPatterns(text string) []ExtractedEntity {
	var entities []ExtractedEntity

	for entityType, patterns := range ee.patterns {
		for _, pattern := range patterns {
			matches := pattern.FindAllStringSubmatch(text, -1)
			indices := pattern.FindAllStringIndex(text, -1)

			for i, match := range matches {
				if len(match) > 0 && len(indices) > i {
					entity := ExtractedEntity{
						Text:       match[0],
						Type:       entityType,
						Confidence: ee.calculatePatternConfidence(entityType, match[0]),
						Position: EntityPosition{
							Start: indices[i][0],
							End:   indices[i][1],
						},
						Attributes: make(map[string]interface{}),
						Metadata: map[string]interface{}{
							"extraction_method": "pattern",
							"pattern_type":      entityType,
						},
					}

					// 添加特定类型的属性
					ee.addTypeSpecificAttributes(&entity)
					entities = append(entities, entity)
				}
			}
		}
	}

	return entities
}

// extractByDictionaries 基于词典抽取
func (ee *EntityExtractor) extractByDictionaries(text string) []ExtractedEntity {
	var entities []ExtractedEntity
	textLower := strings.ToLower(text)

	for entityType, dictionary := range ee.dictionaries {
		for _, term := range dictionary {
			termLower := strings.ToLower(term)

			// 查找所有匹配位置
			start := 0
			for {
				index := strings.Index(textLower[start:], termLower)
				if index == -1 {
					break
				}

				actualStart := start + index
				actualEnd := actualStart + len(term)

				// 检查边界（避免部分匹配）
				if ee.isValidBoundary(text, actualStart, actualEnd) {
					entity := ExtractedEntity{
						Text:       text[actualStart:actualEnd],
						Type:       entityType,
						Confidence: ee.calculateDictionaryConfidence(entityType, term),
						Position: EntityPosition{
							Start: actualStart,
							End:   actualEnd,
						},
						Attributes: make(map[string]interface{}),
						Metadata: map[string]interface{}{
							"extraction_method": "dictionary",
							"dictionary_type":   entityType,
							"matched_term":      term,
						},
					}

					entities = append(entities, entity)
				}

				start = actualStart + 1
			}
		}
	}

	return entities
}

// isValidBoundary 检查边界是否有效
func (ee *EntityExtractor) isValidBoundary(text string, start, end int) bool {
	// 安全地转换为rune数组
	runes := []rune(text)
	runeLen := len(runes)

	// 检查边界是否在有效范围内
	if start < 0 || end < 0 || start >= runeLen || end > runeLen || start >= end {
		return false
	}

	// 检查前边界
	if start > 0 && start-1 < runeLen {
		prevRune := runes[start-1]
		if (prevRune >= 'a' && prevRune <= 'z') ||
			(prevRune >= 'A' && prevRune <= 'Z') ||
			(prevRune >= '0' && prevRune <= '9') ||
			(prevRune >= 0x4e00 && prevRune <= 0x9fff) {
			return false
		}
	}

	// 检查后边界
	if end < runeLen {
		nextRune := runes[end]
		if (nextRune >= 'a' && nextRune <= 'z') ||
			(nextRune >= 'A' && nextRune <= 'Z') ||
			(nextRune >= '0' && nextRune <= '9') ||
			(nextRune >= 0x4e00 && nextRune <= 0x9fff) {
			return false
		}
	}

	return true
}

// calculatePatternConfidence 计算模式置信度
func (ee *EntityExtractor) calculatePatternConfidence(entityType, text string) float64 {
	baseConfidence := map[string]float64{
		"EMAIL":        0.95,
		"PHONE":        0.90,
		"URL":          0.95,
		"IP":           0.90,
		"TIME":         0.85,
		"MONEY":        0.85,
		"NUMBER":       0.80,
		"VERSION":      0.80,
		"PATH":         0.75,
		"TECH":         0.70,
		"PERSON":       0.60,
		"LOCATION":     0.65,
		"ORGANIZATION": 0.65,
	}

	if confidence, exists := baseConfidence[entityType]; exists {
		// 根据文本长度调整置信度
		lengthFactor := 1.0
		if len(text) < 3 {
			lengthFactor = 0.8
		} else if len(text) > 20 {
			lengthFactor = 0.9
		}

		return confidence * lengthFactor
	}

	return 0.5
}

// calculateDictionaryConfidence 计算词典置信度
func (ee *EntityExtractor) calculateDictionaryConfidence(entityType, term string) float64 {
	baseConfidence := map[string]float64{
		"PROGRAMMING_LANGUAGE": 0.90,
		"FRAMEWORK":            0.85,
		"DATABASE":             0.90,
		"OS":                   0.85,
		"CLOUD":                0.85,
		"JOB_TITLE":            0.80,
		"EDUCATION":            0.80,
		"TIME_UNIT":            0.75,
	}

	if confidence, exists := baseConfidence[entityType]; exists {
		// 根据术语长度调整置信度
		lengthFactor := 1.0
		if len(term) > 10 {
			lengthFactor = 1.1
		} else if len(term) < 4 {
			lengthFactor = 0.9
		}

		return confidence * lengthFactor
	}

	return 0.6
}

// addTypeSpecificAttributes 添加特定类型的属性
func (ee *EntityExtractor) addTypeSpecificAttributes(entity *ExtractedEntity) {
	switch entity.Type {
	case "TIME":
		entity.Attributes["normalized"] = ee.normalizeTime(entity.Text)
	case "MONEY":
		entity.Attributes["currency"] = ee.extractCurrency(entity.Text)
		entity.Attributes["amount"] = ee.extractAmount(entity.Text)
	case "EMAIL":
		parts := strings.Split(entity.Text, "@")
		if len(parts) == 2 {
			entity.Attributes["username"] = parts[0]
			entity.Attributes["domain"] = parts[1]
		}
	case "URL":
		entity.Attributes["protocol"] = ee.extractProtocol(entity.Text)
		entity.Attributes["domain"] = ee.extractDomain(entity.Text)
	}
}

// normalizeTime 标准化时间
func (ee *EntityExtractor) normalizeTime(timeStr string) string {
	// 简单的时间标准化
	timeStr = strings.ReplaceAll(timeStr, "年", "-")
	timeStr = strings.ReplaceAll(timeStr, "月", "-")
	timeStr = strings.ReplaceAll(timeStr, "日", "")
	return timeStr
}

// extractCurrency 提取货币类型
func (ee *EntityExtractor) extractCurrency(moneyStr string) string {
	if strings.Contains(moneyStr, "¥") || strings.Contains(moneyStr, "元") {
		return "CNY"
	} else if strings.Contains(moneyStr, "$") {
		return "USD"
	}
	return "unknown"
}

// extractAmount 提取金额
func (ee *EntityExtractor) extractAmount(moneyStr string) string {
	re := regexp.MustCompile(`\d+(\.\d+)?`)
	return re.FindString(moneyStr)
}

// extractProtocol 提取协议
func (ee *EntityExtractor) extractProtocol(url string) string {
	if strings.HasPrefix(url, "https://") {
		return "https"
	} else if strings.HasPrefix(url, "http://") {
		return "http"
	}
	return "unknown"
}

// extractDomain 提取域名
func (ee *EntityExtractor) extractDomain(url string) string {
	url = strings.TrimPrefix(url, "https://")
	url = strings.TrimPrefix(url, "http://")
	url = strings.TrimPrefix(url, "www.")

	parts := strings.Split(url, "/")
	if len(parts) > 0 {
		return parts[0]
	}
	return url
}

// deduplicateEntities 去重实体
func (ee *EntityExtractor) deduplicateEntities(entities []ExtractedEntity) []ExtractedEntity {
	seen := make(map[string]bool)
	var result []ExtractedEntity

	for _, entity := range entities {
		key := entity.Text + "|" + entity.Type
		if !seen[key] {
			seen[key] = true
			result = append(result, entity)
		}
	}

	return result
}
