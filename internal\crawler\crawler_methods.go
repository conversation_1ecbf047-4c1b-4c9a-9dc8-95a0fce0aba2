package crawler

import (
	"encoding/json"
	"fmt"
	"log"
	"net/http"
	"net/url"
	"regexp"
	"strings"
	"time"
	"unicode"
	"unicode/utf8"

	"faq-system/internal/learning"

	"github.com/PuerkitoBio/goquery"
)

// crawlTarget 爬取目标
func (kc *KnowledgeCrawler) crawlTarget(target *CrawlTarget) (*CrawlResult, error) {
	switch target.Type {
	case "website":
		return kc.crawlWebsite(target)
	case "api":
		return kc.crawlAPI(target)
	case "rss":
		return kc.crawlRSS(target)
	case "search_engine":
		return kc.crawlSearchEngine(target)
	default:
		return kc.crawlWebsite(target)
	}
}

// crawlWebsite 爬取网站
func (kc *KnowledgeCrawler) crawlWebsite(target *CrawlTarget) (*CrawlResult, error) {
	// 发送HTTP请求
	req, err := http.NewRequest("GET", target.URL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", kc.config.UserAgent)

	resp, err := kc.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 解析HTML
	doc, err := goquery.NewDocumentFromReader(resp.Body)
	if err != nil {
		return nil, err
	}

	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	// 提取标题
	if titleSelector, ok := target.Selectors["title"]; ok {
		result.Title = kc.sanitizeString(strings.TrimSpace(doc.Find(titleSelector).First().Text()))
	} else {
		result.Title = kc.sanitizeString(strings.TrimSpace(doc.Find("title").First().Text()))
	}

	// 提取内容
	if contentSelector, ok := target.Selectors["content"]; ok {
		result.Content = kc.sanitizeString(strings.TrimSpace(doc.Find(contentSelector).Text()))
	} else {
		// 默认提取主要内容区域
		content := ""
		doc.Find("article, .content, .post, .entry, main, #content").Each(func(i int, s *goquery.Selection) {
			content += s.Text() + "\n"
		})
		result.Content = kc.sanitizeString(strings.TrimSpace(content))
	}

	// 如果没有找到内容，尝试提取body中的文本
	if result.Content == "" {
		result.Content = kc.sanitizeString(strings.TrimSpace(doc.Find("body").Text()))
	}

	// 生成摘要
	result.Summary = kc.generateSummary(result.Content)

	// 提取关键词
	if len(result.Keywords) == 0 {
		result.Keywords = kc.extractKeywords(result.Title + " " + result.Content)
	}

	return result, nil
}

// crawlAPI 爬取API
func (kc *KnowledgeCrawler) crawlAPI(target *CrawlTarget) (*CrawlResult, error) {
	// 简单的API爬取实现
	req, err := http.NewRequest("GET", target.URL, nil)
	if err != nil {
		return nil, err
	}

	req.Header.Set("User-Agent", kc.config.UserAgent)
	req.Header.Set("Accept", "application/json")

	resp, err := kc.httpClient.Do(req)
	if err != nil {
		return nil, err
	}
	defer resp.Body.Close()

	if resp.StatusCode != http.StatusOK {
		return nil, fmt.Errorf("HTTP状态码: %d", resp.StatusCode)
	}

	// 这里可以根据API的具体格式来解析数据
	// 暂时返回一个基本的结果
	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Title:     "API数据",
		Content:   "从API获取的数据",
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// crawlRSS 爬取RSS
func (kc *KnowledgeCrawler) crawlRSS(target *CrawlTarget) (*CrawlResult, error) {
	// RSS爬取的简单实现
	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Title:     "RSS数据",
		Content:   "从RSS获取的数据",
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// crawlSearchEngine 爬取搜索引擎
func (kc *KnowledgeCrawler) crawlSearchEngine(target *CrawlTarget) (*CrawlResult, error) {
	// 搜索引擎爬取的简单实现
	result := &CrawlResult{
		TargetID:  target.ID,
		URL:       target.URL,
		Title:     "搜索结果",
		Content:   "从搜索引擎获取的数据",
		Category:  target.Category,
		Keywords:  target.Keywords,
		CrawledAt: time.Now(),
		Status:    "pending",
		Metadata:  make(map[string]interface{}),
	}

	return result, nil
}

// generateSummary 生成摘要
func (kc *KnowledgeCrawler) generateSummary(content string) string {
	// 首先清理内容
	content = kc.sanitizeString(content)

	// 简单的摘要生成：取前200个字符
	content = strings.TrimSpace(content)
	if len(content) <= 200 {
		return content
	}

	// 尝试在句号处截断
	summary := content[:200]
	if lastDot := strings.LastIndex(summary, "。"); lastDot > 100 {
		summary = summary[:lastDot+3] // 包含句号
	}

	return summary + "..."
}

// extractKeywords 提取关键词
func (kc *KnowledgeCrawler) extractKeywords(text string) []string {
	// 简单的关键词提取
	text = strings.ToLower(text)

	// 技术相关关键词
	techKeywords := []string{
		"python", "java", "javascript", "c#", "go", "rust", "php", "ruby",
		"react", "vue", "angular", "node", "express", "django", "spring",
		"mysql", "postgresql", "mongodb", "redis", "elasticsearch",
		"docker", "kubernetes", "aws", "azure", "gcp", "linux", "windows",
		"api", "rest", "graphql", "microservice", "devops", "ci/cd",
		"machine learning", "ai", "deep learning", "tensorflow", "pytorch",
	}

	var keywords []string
	for _, keyword := range techKeywords {
		if strings.Contains(text, keyword) {
			keywords = append(keywords, keyword)
		}
	}

	return keywords
}

// saveCrawlResult 保存爬取结果
func (kc *KnowledgeCrawler) saveCrawlResult(result *CrawlResult) error {
	// 清理字符串字段，防止MySQL字符编码错误，并根据字段类型限制长度
	result.Title = kc.sanitizeStringWithLength(result.Title, 500)            // VARCHAR(500)
	result.Content = kc.sanitizeStringWithLength(result.Content, 4294967295) // LONGTEXT
	result.Summary = kc.sanitizeStringWithLength(result.Summary, 65535)      // TEXT
	result.Category = kc.sanitizeStringWithLength(result.Category, 100)      // VARCHAR(100)

	metadataJSON, _ := json.Marshal(result.Metadata)
	keywordsJSON, _ := json.Marshal(result.Keywords)

	query := `
		INSERT INTO crawl_results
		(target_id, url, title, content, summary, keywords, category, metadata, crawled_at, status)
		VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
	`

	_, err := kc.db.Exec(query, result.TargetID, result.URL, result.Title, result.Content,
		result.Summary, keywordsJSON, result.Category, metadataJSON, result.CrawledAt, result.Status)

	return err
}

// updateLastCrawled 更新最后爬取时间
func (kc *KnowledgeCrawler) updateLastCrawled(targetID int) error {
	query := "UPDATE crawl_targets SET last_crawled = NOW() WHERE id = ?"
	_, err := kc.db.Exec(query, targetID)

	// 同时更新内存中的数据
	kc.targetsMutex.Lock()
	if target, exists := kc.targets[targetID]; exists {
		target.LastCrawled = time.Now()
	}
	kc.targetsMutex.Unlock()

	return err
}

// processResults 处理爬取结果
func (kc *KnowledgeCrawler) processResults() {
	// 查询待处理的爬取结果
	query := `
		SELECT id, target_id, url, title, content, summary, keywords, category, metadata, crawled_at
		FROM crawl_results 
		WHERE status = 'pending' 
		ORDER BY crawled_at ASC 
		LIMIT 10
	`

	rows, err := kc.db.Query(query)
	if err != nil {
		log.Printf("查询待处理结果失败: %v", err)
		return
	}
	defer rows.Close()

	for rows.Next() {
		var result CrawlResult
		var id int
		var keywordsJSON, metadataJSON string

		err := rows.Scan(&id, &result.TargetID, &result.URL, &result.Title, &result.Content,
			&result.Summary, &keywordsJSON, &result.Category, &metadataJSON, &result.CrawledAt)
		if err != nil {
			continue
		}

		json.Unmarshal([]byte(keywordsJSON), &result.Keywords)
		json.Unmarshal([]byte(metadataJSON), &result.Metadata)

		// 处理结果并转换为学习知识
		if err := kc.processResult(id, &result); err != nil {
			log.Printf("处理结果失败 %d: %v", id, err)
		}
	}
}

// processResult 处理单个爬取结果
func (kc *KnowledgeCrawler) processResult(resultID int, result *CrawlResult) error {
	// 从内容中提取知识
	knowledge := kc.extractKnowledgeFromContent(result)
	if knowledge == nil {
		// 标记为已处理但没有提取到知识
		kc.updateResultStatus(resultID, "processed")
		return nil
	}

	// 保存学习知识
	if kc.knowledgeLearner != nil {
		log.Printf("📚 提取到知识: %s -> %s", knowledge.Question, knowledge.Answer)

		// 设置爬虫特有的属性
		knowledge.LearnedFrom = "crawler"
		knowledge.Source = "crawler" // 使用专门的爬虫来源标识

		// 直接保存知识，不通过LearnFromUserInput
		err := kc.knowledgeLearner.SaveKnowledge(knowledge)
		if err != nil {
			log.Printf("保存学习知识失败: %v", err)
			kc.updateResultStatus(resultID, "failed")
			return err
		}

		log.Printf("✅ 知识保存成功: %s", knowledge.Question)
	}

	// 标记为已处理
	kc.updateResultStatus(resultID, "processed")
	log.Printf("✅ 处理爬取结果: %s", result.Title)

	return nil
}

// extractKnowledgeFromContent 从内容中提取知识
func (kc *KnowledgeCrawler) extractKnowledgeFromContent(result *CrawlResult) *learning.LearnedKnowledge {
	// 简单的知识提取逻辑
	if len(result.Content) < 50 {
		return nil // 内容太短，不提取
	}

	// 特殊处理百度热搜API数据
	if strings.Contains(result.URL, "top.baidu.com/api/board") {
		return kc.extractBaiduHotSearchKnowledge(result)
	}

	// 生成问题和答案
	question := result.Title
	if question == "" {
		question = result.Summary
	}

	// 如果问题是JSON格式，跳过处理
	if strings.HasPrefix(question, "{") && strings.HasSuffix(question, "}") {
		log.Printf("⚠️  跳过JSON格式的问题: %s", result.URL)
		return nil
	}

	// 确保问题以问号结尾
	if !strings.HasSuffix(question, "？") && !strings.HasSuffix(question, "?") {
		question += "？"
	}

	knowledge := &learning.LearnedKnowledge{
		Question:   question,
		Answer:     result.Summary,
		Category:   result.Category,
		Keywords:   result.Keywords,
		Context:    fmt.Sprintf("从网站爬取: %s", result.URL),
		Confidence: 0.7,        // 爬取的知识置信度中等
		Status:     "approved", // 直接设置为已批准状态
		CreatedAt:  time.Now(),
		Metadata: map[string]interface{}{
			"source_url":     result.URL,
			"crawled_at":     result.CrawledAt,
			"target_id":      result.TargetID,
			"content_length": len(result.Content),
		},
	}

	return knowledge
}

// extractBaiduHotSearchKnowledge 从百度热搜JSON数据中提取知识
func (kc *KnowledgeCrawler) extractBaiduHotSearchKnowledge(result *CrawlResult) *learning.LearnedKnowledge {
	// 解析JSON数据
	var hotSearchData map[string]interface{}
	if err := json.Unmarshal([]byte(result.Content), &hotSearchData); err != nil {
		log.Printf("❌ 解析百度热搜JSON失败: %v", err)
		return nil
	}

	// 检查数据结构
	data, ok := hotSearchData["data"].(map[string]interface{})
	if !ok {
		log.Printf("❌ 百度热搜数据格式错误: 缺少data字段")
		return nil
	}

	cards, ok := data["cards"].([]interface{})
	if !ok || len(cards) == 0 {
		log.Printf("❌ 百度热搜数据格式错误: 缺少cards字段")
		return nil
	}

	// 获取第一个卡片的热搜内容
	firstCard, ok := cards[0].(map[string]interface{})
	if !ok {
		log.Printf("❌ 百度热搜数据格式错误: cards格式错误")
		return nil
	}

	content, ok := firstCard["content"].([]interface{})
	if !ok || len(content) == 0 {
		log.Printf("❌ 百度热搜数据格式错误: 缺少content字段")
		return nil
	}

	// 提取热搜条目
	var hotTopics []string
	for i, item := range content {
		if i >= 5 { // 只取前5个热搜
			break
		}

		if itemMap, ok := item.(map[string]interface{}); ok {
			if query, exists := itemMap["query"].(string); exists && query != "" {
				// URL解码
				if decodedQuery, err := url.QueryUnescape(query); err == nil {
					hotTopics = append(hotTopics, decodedQuery)
				} else {
					hotTopics = append(hotTopics, query)
				}
			}
		}
	}

	if len(hotTopics) == 0 {
		log.Printf("❌ 没有提取到有效的热搜话题")
		return nil
	}

	// 生成知识
	question := "当前百度热搜榜上有哪些热门话题？"
	answer := fmt.Sprintf("根据最新的百度热搜榜，当前热门话题包括：\n%s", strings.Join(hotTopics, "\n"))

	knowledge := &learning.LearnedKnowledge{
		Question:   question,
		Answer:     answer,
		Category:   "热搜",
		Keywords:   hotTopics,
		Context:    fmt.Sprintf("从百度热搜API获取: %s", result.URL),
		Confidence: 0.8,        // 热搜数据置信度较高
		Status:     "approved", // 直接设置为已批准状态
		CreatedAt:  time.Now(),
		Metadata: map[string]interface{}{
			"source_url":     result.URL,
			"crawled_at":     result.CrawledAt,
			"target_id":      result.TargetID,
			"content_length": len(result.Content),
			"hot_topics":     hotTopics,
		},
	}

	log.Printf("✅ 成功提取百度热搜知识: %d个话题", len(hotTopics))
	return knowledge
}

// updateResultStatus 更新结果状态
func (kc *KnowledgeCrawler) updateResultStatus(resultID int, status string) error {
	query := "UPDATE crawl_results SET status = ?, processed_at = NOW() WHERE id = ?"
	_, err := kc.db.Exec(query, status, resultID)
	return err
}

// sanitizeString 清理字符串，移除可能导致MySQL编码错误的字符
func (kc *KnowledgeCrawler) sanitizeString(s string) string {
	return kc.sanitizeStringWithLength(s, 65535) // 默认TEXT字段长度
}

// sanitizeStringWithLength 清理字符串并限制长度
func (kc *KnowledgeCrawler) sanitizeStringWithLength(s string, maxLength int) string {
	if s == "" {
		return s
	}

	// 1. 移除无效的UTF-8字符
	if !utf8.ValidString(s) {
		s = strings.ToValidUTF8(s, "")
	}

	// 2. 移除控制字符（除了换行符、制表符、回车符）
	s = strings.Map(func(r rune) rune {
		if unicode.IsControl(r) && r != '\n' && r != '\t' && r != '\r' {
			return -1 // 移除字符
		}
		return r
	}, s)

	// 3. 移除emoji和特殊Unicode字符（可能导致MySQL问题）
	emojiRegex := regexp.MustCompile(`[\x{1F600}-\x{1F64F}]|[\x{1F300}-\x{1F5FF}]|[\x{1F680}-\x{1F6FF}]|[\x{1F1E0}-\x{1F1FF}]|[\x{2600}-\x{26FF}]|[\x{2700}-\x{27BF}]`)
	s = emojiRegex.ReplaceAllString(s, "")

	// 4. 移除其他可能有问题的Unicode字符
	problemCharsRegex := regexp.MustCompile(`[\x{FFF0}-\x{FFFF}]|[\x{0000}-\x{001F}]`)
	s = problemCharsRegex.ReplaceAllString(s, "")

	// 5. 限制字符串长度，防止过长
	if len(s) > maxLength {
		// 安全截断，确保不会在UTF-8字符中间截断
		truncated := s
		for len(truncated) > maxLength {
			// 逐个字符减少，确保UTF-8完整性
			runes := []rune(truncated)
			if len(runes) > 0 {
				truncated = string(runes[:len(runes)-1])
			} else {
				break
			}
		}

		// 尝试在合适的位置截断（尽量在句号或空格处）
		if lastDot := strings.LastIndex(truncated, "。"); lastDot > len(truncated)-100 && lastDot > 0 {
			s = truncated[:lastDot+3]
		} else if lastSpace := strings.LastIndex(truncated, " "); lastSpace > len(truncated)-100 && lastSpace > 0 {
			s = truncated[:lastSpace]
		} else {
			s = truncated
		}
	}

	// 6. 清理多余的空白字符
	s = strings.TrimSpace(s)
	s = regexp.MustCompile(`\s+`).ReplaceAllString(s, " ")

	return s
}
