<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试弹窗样式</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background: #f5f5f5;
        }
        
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 15px 30px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px;
        }
        
        .test-button:hover {
            background: #0056b3;
        }
        
        /* 知识详情弹窗样式 */
        .knowledge-modal {
            position: fixed !important;
            top: 0 !important;
            left: 0 !important;
            width: 100% !important;
            height: 100% !important;
            background: rgba(0, 0, 0, 0.5) !important;
            display: flex !important;
            justify-content: center !important;
            align-items: center !important;
            z-index: 9999 !important;
        }
        
        .knowledge-modal-content {
            background: white !important;
            border-radius: 15px !important;
            max-width: 600px !important;
            width: 90% !important;
            max-height: 80vh !important;
            overflow-y: auto !important;
            box-shadow: 0 20px 40px rgba(0,0,0,0.2) !important;
            position: relative !important;
        }
        
        .knowledge-modal-header {
            padding: 20px !important;
            border-bottom: 1px solid #eee !important;
            display: flex !important;
            justify-content: space-between !important;
            align-items: center !important;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
            border-radius: 15px 15px 0 0 !important;
        }
        
        .knowledge-modal-header h3 {
            margin: 0;
            color: white;
        }
        
        .knowledge-modal-close {
            font-size: 24px;
            cursor: pointer;
            color: white;
            opacity: 0.8;
        }
        
        .knowledge-modal-close:hover {
            opacity: 1;
        }
        
        .knowledge-modal-body {
            padding: 20px;
        }
        
        .knowledge-item {
            margin-bottom: 20px;
        }
        
        .knowledge-question, .knowledge-answer {
            margin-bottom: 15px;
        }
        
        .knowledge-question p, .knowledge-answer p {
            margin: 5px 0;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 8px;
            border-left: 4px solid #667eea;
        }
        
        .knowledge-meta {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .knowledge-meta span {
            background: #e9ecef;
            padding: 5px 10px;
            border-radius: 15px;
            font-size: 12px;
            color: #495057;
        }
        
        .knowledge-topic {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%) !important;
            color: white !important;
        }
        
        .knowledge-actions {
            display: flex;
            gap: 10px;
            justify-content: center;
            margin-top: 20px;
        }
        
        .action-btn {
            padding: 10px 20px;
            border: none;
            border-radius: 25px;
            cursor: pointer;
            font-size: 14px;
            transition: transform 0.2s;
        }
        
        .action-btn:first-child {
            background: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
            color: white;
        }
        
        .action-btn:last-child {
            background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
            color: white;
        }
        
        .action-btn:hover {
            transform: translateY(-2px);
        }
        
        /* 推荐链接样式 */
        .knowledge-link, a[href^="#knowledge-"] {
            color: #667eea !important;
            text-decoration: none !important;
            cursor: pointer !important;
            border-bottom: 1px dashed #667eea !important;
            padding: 2px 4px !important;
            border-radius: 3px !important;
            background: rgba(102, 126, 234, 0.1) !important;
            transition: all 0.2s ease !important;
        }
        
        .knowledge-link:hover, a[href^="#knowledge-"]:hover {
            color: #764ba2 !important;
            border-bottom-style: solid !important;
            background: rgba(118, 75, 162, 0.15) !important;
            transform: translateY(-1px) !important;
        }
    </style>
</head>
<body>
    <h1>🎨 弹窗样式测试</h1>
    
    <div>
        <button class="test-button" onclick="showTestModal()">测试知识详情弹窗</button>
        <button class="test-button" onclick="showTestLink()">测试推荐链接样式</button>
    </div>
    
    <div id="test-content" style="margin-top: 20px; padding: 20px; background: white; border-radius: 8px;">
        <h3>测试内容区域</h3>
        <p>这里会显示测试内容</p>
    </div>

    <script>
        function showTestModal() {
            // 创建测试弹窗
            const modal = document.createElement('div');
            modal.className = 'knowledge-modal';
            modal.style.cssText = 'position: fixed !important; top: 0 !important; left: 0 !important; width: 100% !important; height: 100% !important; background: rgba(0, 0, 0, 0.5) !important; display: flex !important; justify-content: center !important; align-items: center !important; z-index: 9999 !important;';
            
            modal.innerHTML =
                '<div class="knowledge-modal-content" style="background: white !important; border-radius: 15px !important; max-width: 600px !important; width: 90% !important; max-height: 80vh !important; overflow-y: auto !important; box-shadow: 0 20px 40px rgba(0,0,0,0.2) !important; position: relative !important;">' +
                    '<div class="knowledge-modal-header">' +
                        '<h3>📖 知识详情</h3>' +
                        '<span class="knowledge-modal-close" onclick="closeTestModal()">&times;</span>' +
                    '</div>' +
                    '<div class="knowledge-modal-body">' +
                        '<div class="knowledge-item">' +
                            '<div class="knowledge-question">' +
                                '<strong>❓ 问题：</strong>' +
                                '<p>这是一个测试问题</p>' +
                            '</div>' +
                            '<div class="knowledge-answer">' +
                                '<strong>💡 答案：</strong>' +
                                '<p>这是一个测试答案，用来验证弹窗样式是否正确显示。</p>' +
                            '</div>' +
                            '<div class="knowledge-meta">' +
                                '<span class="knowledge-topic">🏷️ 测试知识点</span>' +
                                '<span class="knowledge-confidence">📊 置信度: 95.0%</span>' +
                                '<span class="knowledge-time">⏰ ' + new Date().toLocaleString() + '</span>' +
                            '</div>' +
                        '</div>' +
                        '<div class="knowledge-actions">' +
                            '<button class="action-btn" onclick="alert(\'重新提问功能\')">🔄 重新提问</button>' +
                            '<button class="action-btn" onclick="closeTestModal()">✅ 知道了</button>' +
                        '</div>' +
                    '</div>' +
                '</div>';
            
            document.body.appendChild(modal);
            
            // 添加点击外部关闭功能
            modal.addEventListener('click', function(e) {
                if (e.target === modal) {
                    closeTestModal();
                }
            });
        }
        
        function closeTestModal() {
            const modal = document.querySelector('.knowledge-modal');
            if (modal) {
                modal.remove();
            }
        }
        
        function showTestLink() {
            const testContent = document.getElementById('test-content');
            testContent.innerHTML = 
                '<h3>推荐链接样式测试</h3>' +
                '<p>🔗 <strong>您可能还想了解 (测试知识点)：</strong></p>' +
                '<p><a href="#knowledge-123" class="knowledge-link">📖 这是一个可点击的推荐链接</a></p>' +
                '<p><a href="#knowledge-456" class="knowledge-link">📖 另一个推荐链接示例</a></p>' +
                '<p><a href="#knowledge-789" class="knowledge-link">📖 第三个推荐链接</a></p>' +
                '<p style="margin-top: 20px; color: #666;">悬停在链接上查看效果，点击链接会弹出知识详情。</p>';
            
            // 添加点击事件
            document.addEventListener('click', function(e) {
                if (e.target.tagName === 'A' && e.target.getAttribute('href') && e.target.getAttribute('href').startsWith('#knowledge-')) {
                    e.preventDefault();
                    showTestModal();
                }
            });
        }
    </script>
</body>
</html>
