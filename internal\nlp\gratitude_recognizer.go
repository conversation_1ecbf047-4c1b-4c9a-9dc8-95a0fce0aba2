package nlp

import (
	"log"
	"regexp"
	"strings"
)

// GratitudeRecognizer 感谢识别器 - 专门用于识别各种感谢表达
type GratitudeRecognizer struct {
	patterns    map[string][]*regexp.Regexp
	keywords    map[string][]string
	initialized bool
}

// GratitudeResult 感谢识别结果
type GratitudeResult struct {
	IsGratitude     bool                   `json:"is_gratitude"`
	GratitudeType   string                 `json:"gratitude_type"`   // basic, emphatic, formal, casual, specific
	Intensity       string                 `json:"intensity"`        // mild, moderate, strong, very_strong
	Formality       string                 `json:"formality"`        // formal, informal, casual
	Confidence      float64                `json:"confidence"`
	MatchedPatterns []string               `json:"matched_patterns"`
	MatchedKeywords []string               `json:"matched_keywords"`
	Attributes      map[string]interface{} `json:"attributes"`
	Metadata        map[string]interface{} `json:"metadata"`
}

// NewGratitudeRecognizer 创建感谢识别器
func NewGratitudeRecognizer() *GratitudeRecognizer {
	gr := &GratitudeRecognizer{
		patterns: make(map[string][]*regexp.Regexp),
		keywords: make(map[string][]string),
	}
	
	gr.initializePatterns()
	gr.initializeKeywords()
	gr.initialized = true
	
	log.Printf("✅ 感谢识别器初始化完成")
	return gr
}

// initializePatterns 初始化模式
func (gr *GratitudeRecognizer) initializePatterns() {
	// 基础感谢
	gr.addPattern("basic", `^(谢谢|多谢|感谢|thank you|thanks|thx)$`)
	gr.addPattern("basic", `^(谢谢|多谢|感谢|thank you|thanks|thx)[！!。.]*$`)
	
	// 强调感谢
	gr.addPattern("emphatic", `^(真的谢谢|真的感谢|非常感谢|十分感谢|万分感谢|特别感谢)$`)
	gr.addPattern("emphatic", `^(真的谢谢|真的感谢|非常感谢|十分感谢|万分感谢|特别感谢)[！!。.]*$`)
	gr.addPattern("emphatic", `^(太感谢了|感谢不尽|无比感谢|深深感谢|由衷感谢)$`)
	gr.addPattern("emphatic", `^(thank you so much|thank you very much|thanks a lot|many thanks)$`)
	
	// 正式感谢
	gr.addPattern("formal", `^(感谢您|谢谢您|多谢您|致谢|表示感谢|深表感谢)$`)
	gr.addPattern("formal", `^(感谢您|谢谢您|多谢您|致谢|表示感谢|深表感谢)[！!。.]*$`)
	gr.addPattern("formal", `^(衷心感谢|诚挚感谢|真诚感谢|由衷感谢您)$`)
	
	// 随意感谢
	gr.addPattern("casual", `^(谢了|谢啦|3q|3Q|thx|tks|ty)$`)
	gr.addPattern("casual", `^(谢了|谢啦|3q|3Q|thx|tks|ty)[！!。.]*$`)
	
	// 具体感谢
	gr.addPattern("specific", `^(谢谢你的帮助|感谢你的帮助|谢谢你的回答|感谢你的回答)$`)
	gr.addPattern("specific", `^(谢谢你的帮助|感谢你的帮助|谢谢你的回答|感谢你的回答)[！!。.]*$`)
	gr.addPattern("specific", `^(谢谢你的解答|感谢你的解答|谢谢你的指导|感谢你的指导)$`)
	gr.addPattern("specific", `^(谢谢你的支持|感谢你的支持|谢谢你的服务|感谢你的服务)$`)
	gr.addPattern("specific", `(谢谢.*帮助|感谢.*帮助|谢谢.*回答|感谢.*回答)`)
	gr.addPattern("specific", `(谢谢.*解答|感谢.*解答|谢谢.*指导|感谢.*指导)`)
	
	// 感谢 + 赞美
	gr.addPattern("appreciative", `^(谢谢，你真棒|感谢，你很厉害|谢谢，太好了|感谢，很有用)$`)
	gr.addPattern("appreciative", `^(谢谢，你真棒|感谢，你很厉害|谢谢，太好了|感谢，很有用)[！!。.]*$`)
	gr.addPattern("appreciative", `(谢谢.*棒|感谢.*棒|谢谢.*好|感谢.*好|谢谢.*厉害|感谢.*厉害)`)
}

// initializeKeywords 初始化关键词
func (gr *GratitudeRecognizer) initializeKeywords() {
	gr.keywords["basic"] = []string{
		"谢谢", "多谢", "感谢", "thank you", "thanks", "thx", "ty",
	}
	
	gr.keywords["emphatic"] = []string{
		"真的谢谢", "真的感谢", "非常感谢", "十分感谢", "万分感谢", "特别感谢",
		"太感谢了", "感谢不尽", "无比感谢", "深深感谢", "由衷感谢",
		"thank you so much", "thank you very much", "thanks a lot", "many thanks",
	}
	
	gr.keywords["formal"] = []string{
		"感谢您", "谢谢您", "多谢您", "致谢", "表示感谢", "深表感谢",
		"衷心感谢", "诚挚感谢", "真诚感谢", "由衷感谢您",
	}
	
	gr.keywords["casual"] = []string{
		"谢了", "谢啦", "3q", "3Q", "thx", "tks", "ty",
	}
	
	gr.keywords["specific"] = []string{
		"谢谢你的帮助", "感谢你的帮助", "谢谢你的回答", "感谢你的回答",
		"谢谢你的解答", "感谢你的解答", "谢谢你的指导", "感谢你的指导",
		"谢谢你的支持", "感谢你的支持", "谢谢你的服务", "感谢你的服务",
		"帮助", "回答", "解答", "指导", "支持", "服务",
	}
	
	gr.keywords["appreciative"] = []string{
		"谢谢，你真棒", "感谢，你很厉害", "谢谢，太好了", "感谢，很有用",
		"棒", "厉害", "好", "有用", "赞", "优秀", "完美", "满意",
	}
}

// addPattern 添加模式
func (gr *GratitudeRecognizer) addPattern(category string, pattern string) {
	regex, err := regexp.Compile(pattern)
	if err != nil {
		log.Printf("⚠️ 感谢识别器编译正则表达式失败: %s - %v", pattern, err)
		return
	}
	gr.patterns[category] = append(gr.patterns[category], regex)
}

// RecognizeGratitude 识别感谢
func (gr *GratitudeRecognizer) RecognizeGratitude(text string) GratitudeResult {
	if !gr.initialized {
		return GratitudeResult{
			IsGratitude: false,
			Confidence:  0.0,
			Attributes:  make(map[string]interface{}),
			Metadata:    make(map[string]interface{}),
		}
	}
	
	textLower := strings.ToLower(strings.TrimSpace(text))
	textOriginal := strings.TrimSpace(text)
	
	result := GratitudeResult{
		MatchedPatterns: []string{},
		MatchedKeywords: []string{},
		Attributes:      make(map[string]interface{}),
		Metadata: map[string]interface{}{
			"original_text": text,
			"text_length":   len(text),
			"word_count":    len(strings.Fields(text)),
		},
	}
	
	// 1. 模式匹配
	patternScore := gr.matchPatterns(textLower, &result)
	
	// 2. 关键词匹配
	keywordScore := gr.matchKeywords(textLower, &result)
	
	// 3. 特殊规则
	specialScore := gr.applySpecialRules(textOriginal, textLower, &result)
	
	// 4. 计算综合置信度
	totalScore := patternScore + keywordScore + specialScore
	result.Confidence = gr.normalizeScore(totalScore)
	
	// 5. 判断是否为感谢
	result.IsGratitude = result.Confidence >= 0.5
	
	// 6. 确定感谢类型、强度和正式程度
	if result.IsGratitude {
		result.GratitudeType = gr.determineGratitudeType(&result)
		result.Intensity = gr.determineIntensity(textOriginal, &result)
		result.Formality = gr.determineFormality(textOriginal, &result)
	}
	
	log.Printf("🙏 感谢识别: %s -> %t (%.2f) [%s/%s/%s]", 
		text, result.IsGratitude, result.Confidence, result.GratitudeType, result.Intensity, result.Formality)
	
	return result
}

// matchPatterns 模式匹配
func (gr *GratitudeRecognizer) matchPatterns(text string, result *GratitudeResult) float64 {
	score := 0.0
	
	for category, patterns := range gr.patterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				score += gr.getPatternWeight(category)
				result.MatchedPatterns = append(result.MatchedPatterns, category)
				result.Attributes[category+"_matched"] = true
			}
		}
	}
	
	return score
}

// matchKeywords 关键词匹配
func (gr *GratitudeRecognizer) matchKeywords(text string, result *GratitudeResult) float64 {
	score := 0.0
	
	for category, keywords := range gr.keywords {
		for _, keyword := range keywords {
			if strings.Contains(text, keyword) {
				score += gr.getKeywordWeight(category)
				result.MatchedKeywords = append(result.MatchedKeywords, keyword)
			}
		}
	}
	
	return score
}

// applySpecialRules 应用特殊规则
func (gr *GratitudeRecognizer) applySpecialRules(textOriginal, textLower string, result *GratitudeResult) float64 {
	score := 0.0
	
	// 1. 短文本更可能是感谢
	if len(textOriginal) <= 20 {
		score += 0.3
		result.Attributes["short_text"] = true
	}
	
	// 2. 包含感叹号表示热情
	if strings.Contains(textOriginal, "!") || strings.Contains(textOriginal, "！") {
		score += 0.3
		result.Attributes["enthusiastic"] = true
	}
	
	// 3. 强调词汇增强识别
	emphasisWords := []string{"真的", "非常", "十分", "万分", "特别", "太", "深深", "由衷"}
	for _, word := range emphasisWords {
		if strings.Contains(textLower, word) {
			score += 0.4
			result.Attributes["emphasized"] = true
			break
		}
	}
	
	// 4. 表情符号增强识别
	gratefulEmojis := []string{"😊", "😄", "😃", "🙂", "🙏", "❤️", "💖", "👍", "🎉", "😘"}
	for _, emoji := range gratefulEmojis {
		if strings.Contains(textOriginal, emoji) {
			score += 0.4
			result.Attributes["has_emoji"] = true
			break
		}
	}
	
	// 5. 重复感谢词表示强烈感谢
	thankWords := []string{"谢谢", "感谢", "thanks", "thank"}
	for _, word := range thankWords {
		count := strings.Count(textLower, word)
		if count > 1 {
			score += 0.3 * float64(count-1)
			result.Attributes["repeated_thanks"] = true
			break
		}
	}
	
	// 6. 包含具体感谢对象
	specificObjects := []string{"帮助", "回答", "解答", "指导", "支持", "服务", "建议", "意见"}
	for _, obj := range specificObjects {
		if strings.Contains(textLower, obj) {
			score += 0.2
			result.Attributes["specific_object"] = true
			break
		}
	}
	
	// 7. 排除技术问题的误判
	techKeywords := []string{"api", "数据库", "mysql", "代码", "编程", "开发", "部署", "配置", "算法", "函数"}
	for _, keyword := range techKeywords {
		if strings.Contains(textLower, keyword) {
			score *= 0.3 // 降低分数
			result.Attributes["has_tech_keyword"] = true
			break
		}
	}
	
	return score
}

// getPatternWeight 获取模式权重
func (gr *GratitudeRecognizer) getPatternWeight(category string) float64 {
	weights := map[string]float64{
		"basic":        1.0,
		"emphatic":     1.3,
		"formal":       1.1,
		"casual":       0.9,
		"specific":     1.2,
		"appreciative": 1.4,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.5
}

// getKeywordWeight 获取关键词权重
func (gr *GratitudeRecognizer) getKeywordWeight(category string) float64 {
	weights := map[string]float64{
		"basic":        0.4,
		"emphatic":     0.6,
		"formal":       0.5,
		"casual":       0.3,
		"specific":     0.5,
		"appreciative": 0.6,
	}
	
	if weight, exists := weights[category]; exists {
		return weight
	}
	return 0.2
}

// normalizeScore 标准化分数
func (gr *GratitudeRecognizer) normalizeScore(score float64) float64 {
	if score <= 0 {
		return 0.0
	}
	normalized := 1.0 / (1.0 + 1.0/score)
	if normalized > 1.0 {
		normalized = 1.0
	}
	return normalized
}

// determineGratitudeType 确定感谢类型
func (gr *GratitudeRecognizer) determineGratitudeType(result *GratitudeResult) string {
	// 根据匹配的模式确定类型
	for _, pattern := range result.MatchedPatterns {
		if pattern != "basic" {
			return pattern
		}
	}
	
	// 根据关键词确定类型
	if gr.containsKeywords(result.MatchedKeywords, gr.keywords["emphatic"]) {
		return "emphatic"
	}
	if gr.containsKeywords(result.MatchedKeywords, gr.keywords["formal"]) {
		return "formal"
	}
	if gr.containsKeywords(result.MatchedKeywords, gr.keywords["specific"]) {
		return "specific"
	}
	if gr.containsKeywords(result.MatchedKeywords, gr.keywords["appreciative"]) {
		return "appreciative"
	}
	if gr.containsKeywords(result.MatchedKeywords, gr.keywords["casual"]) {
		return "casual"
	}
	
	return "basic"
}

// determineIntensity 确定强度
func (gr *GratitudeRecognizer) determineIntensity(text string, result *GratitudeResult) string {
	// 非常强烈的感谢
	veryStrongWords := []string{"万分", "无比", "深深", "由衷", "感谢不尽"}
	for _, word := range veryStrongWords {
		if strings.Contains(text, word) {
			return "very_strong"
		}
	}
	
	// 强烈的感谢
	strongWords := []string{"非常", "十分", "特别", "真的", "太"}
	for _, word := range strongWords {
		if strings.Contains(text, word) {
			return "strong"
		}
	}
	
	// 包含感叹号或表情符号或重复感谢
	if result.Attributes["enthusiastic"] == true || result.Attributes["has_emoji"] == true || result.Attributes["repeated_thanks"] == true {
		return "moderate"
	}
	
	return "mild"
}

// determineFormality 确定正式程度
func (gr *GratitudeRecognizer) determineFormality(text string, result *GratitudeResult) string {
	// 正式用语
	formalWords := []string{"感谢您", "谢谢您", "致谢", "表示感谢", "深表感谢", "衷心感谢", "诚挚感谢"}
	for _, word := range formalWords {
		if strings.Contains(text, word) {
			return "formal"
		}
	}
	
	// 非正式用语
	informalWords := []string{"谢了", "谢啦", "3q", "3Q", "thx", "tks", "ty"}
	for _, word := range informalWords {
		if strings.Contains(text, word) {
			return "informal"
		}
	}
	
	// 包含感叹号或表情符号通常比较随意
	if result.Attributes["enthusiastic"] == true || result.Attributes["has_emoji"] == true {
		return "casual"
	}
	
	return "neutral"
}

// containsKeywords 检查是否包含特定类别的关键词
func (gr *GratitudeRecognizer) containsKeywords(matched []string, categoryKeywords []string) bool {
	for _, matched := range matched {
		for _, keyword := range categoryKeywords {
			if matched == keyword {
				return true
			}
		}
	}
	return false
}

// IsInitialized 检查是否已初始化
func (gr *GratitudeRecognizer) IsInitialized() bool {
	return gr.initialized
}
