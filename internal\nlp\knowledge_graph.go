package nlp

import (
	"fmt"
	"log"
	"sort"
	"strings"
	"sync"
	"time"
)

// KnowledgeGraph 知识图谱
type KnowledgeGraph struct {
	integratedProcessor *IntegratedProcessor
	entities            map[string]*KGEntity
	relations           map[string]*KGRelation
	concepts            map[string]*KGConcept
	rules               []*KGRule
	initialized         bool
	mutex               sync.RWMutex

	// 图谱统计
	stats *KGStats
}

// KGEntity 知识图谱实体
type KGEntity struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"`
	Properties map[string]interface{} `json:"properties"`
	Relations  []string               `json:"relations"` // 关系ID列表
	Concepts   []string               `json:"concepts"`  // 概念ID列表
	Confidence float64                `json:"confidence"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// KGRelation 知识图谱关系
type KGRelation struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	Subject    string                 `json:"subject"`   // 主体实体ID
	Predicate  string                 `json:"predicate"` // 谓词
	Object     string                 `json:"object"`    // 客体实体ID
	Weight     float64                `json:"weight"`    // 关系权重
	Confidence float64                `json:"confidence"`
	Properties map[string]interface{} `json:"properties"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// KGConcept 知识图谱概念
type KGConcept struct {
	ID          string                 `json:"id"`
	Name        string                 `json:"name"`
	Description string                 `json:"description"`
	Category    string                 `json:"category"`
	Level       int                    `json:"level"`    // 概念层级
	Parents     []string               `json:"parents"`  // 父概念ID列表
	Children    []string               `json:"children"` // 子概念ID列表
	Entities    []string               `json:"entities"` // 相关实体ID列表
	Properties  map[string]interface{} `json:"properties"`
	Confidence  float64                `json:"confidence"`
	CreatedAt   time.Time              `json:"created_at"`
	UpdatedAt   time.Time              `json:"updated_at"`
	Metadata    map[string]interface{} `json:"metadata"`
}

// KGRule 知识图谱规则
type KGRule struct {
	ID         string                 `json:"id"`
	Name       string                 `json:"name"`
	Type       string                 `json:"type"`      // inference, validation, transformation
	Condition  string                 `json:"condition"` // 规则条件
	Action     string                 `json:"action"`    // 规则动作
	Priority   int                    `json:"priority"`
	Enabled    bool                   `json:"enabled"`
	Confidence float64                `json:"confidence"`
	CreatedAt  time.Time              `json:"created_at"`
	UpdatedAt  time.Time              `json:"updated_at"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// KGStats 知识图谱统计
type KGStats struct {
	EntityCount   int `json:"entity_count"`
	RelationCount int `json:"relation_count"`
	ConceptCount  int `json:"concept_count"`
	RuleCount     int `json:"rule_count"`

	// 类型统计
	EntityTypes   map[string]int `json:"entity_types"`
	RelationTypes map[string]int `json:"relation_types"`
	ConceptTypes  map[string]int `json:"concept_types"`

	// 质量指标
	AvgConfidence float64 `json:"avg_confidence"`
	Completeness  float64 `json:"completeness"`
	Consistency   float64 `json:"consistency"`

	// 更新时间
	LastUpdated time.Time `json:"last_updated"`
}

// KGQuery 知识图谱查询
type KGQuery struct {
	Type      string                 `json:"type"` // entity, relation, concept, path
	Subject   string                 `json:"subject"`
	Predicate string                 `json:"predicate"`
	Object    string                 `json:"object"`
	Depth     int                    `json:"depth"` // 查询深度
	Limit     int                    `json:"limit"` // 结果限制
	Filters   map[string]interface{} `json:"filters"`
	Options   map[string]interface{} `json:"options"`
}

// KGQueryResult 知识图谱查询结果
type KGQueryResult struct {
	Query          *KGQuery               `json:"query"`
	Entities       []*KGEntity            `json:"entities"`
	Relations      []*KGRelation          `json:"relations"`
	Concepts       []*KGConcept           `json:"concepts"`
	Paths          []KGPath               `json:"paths"`
	TotalCount     int                    `json:"total_count"`
	ProcessingTime time.Duration          `json:"processing_time"`
	Confidence     float64                `json:"confidence"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// KGPath 知识图谱路径
type KGPath struct {
	Nodes      []KGNode               `json:"nodes"`
	Edges      []KGEdge               `json:"edges"`
	Length     int                    `json:"length"`
	Weight     float64                `json:"weight"`
	Confidence float64                `json:"confidence"`
	Metadata   map[string]interface{} `json:"metadata"`
}

// KGNode 知识图谱节点
type KGNode struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"` // entity, concept
	Label      string                 `json:"label"`
	Properties map[string]interface{} `json:"properties"`
}

// KGEdge 知识图谱边
type KGEdge struct {
	ID         string                 `json:"id"`
	Type       string                 `json:"type"`
	Label      string                 `json:"label"`
	Source     string                 `json:"source"`
	Target     string                 `json:"target"`
	Weight     float64                `json:"weight"`
	Properties map[string]interface{} `json:"properties"`
}

// NewKnowledgeGraph 创建知识图谱
func NewKnowledgeGraph(processor *IntegratedProcessor) *KnowledgeGraph {
	log.Printf("🧠 初始化知识图谱系统...")

	kg := &KnowledgeGraph{
		integratedProcessor: processor,
		entities:            make(map[string]*KGEntity),
		relations:           make(map[string]*KGRelation),
		concepts:            make(map[string]*KGConcept),
		rules:               []*KGRule{},
		initialized:         false,
		stats:               &KGStats{},
	}

	// 异步初始化
	go kg.initializeAsync()

	return kg
}

// initializeAsync 异步初始化
func (kg *KnowledgeGraph) initializeAsync() {
	defer func() {
		if r := recover(); r != nil {
			log.Printf("⚠️ 知识图谱初始化失败: %v", r)
			kg.initialized = false
		}
	}()

	log.Printf("🔧 开始初始化知识图谱组件...")

	// 等待NLP处理器初始化完成
	for i := 0; i < 30; i++ {
		if kg.integratedProcessor != nil && kg.integratedProcessor.IsInitialized() {
			break
		}
		time.Sleep(1 * time.Second)
		log.Printf("⏳ 等待NLP处理器初始化... (%d/30)", i+1)
	}

	// 初始化基础概念
	kg.initializeBaseConcepts()

	// 初始化推理规则
	kg.initializeInferenceRules()

	// 更新统计信息
	kg.updateStats()

	kg.mutex.Lock()
	kg.initialized = true
	kg.mutex.Unlock()

	log.Printf("✅ 知识图谱系统初始化完成")
}

// IsInitialized 检查是否已初始化
func (kg *KnowledgeGraph) IsInitialized() bool {
	kg.mutex.RLock()
	defer kg.mutex.RUnlock()
	return kg.initialized
}

// AddEntity 添加实体
func (kg *KnowledgeGraph) AddEntity(name, entityType string, properties map[string]interface{}) (*KGEntity, error) {
	kg.mutex.Lock()
	defer kg.mutex.Unlock()

	entityID := kg.generateEntityID(name, entityType)

	entity := &KGEntity{
		ID:         entityID,
		Name:       name,
		Type:       entityType,
		Properties: properties,
		Relations:  []string{},
		Concepts:   []string{},
		Confidence: 0.8,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	kg.entities[entityID] = entity

	log.Printf("📝 添加实体: %s (%s)", name, entityType)

	return entity, nil
}

// AddRelation 添加关系
func (kg *KnowledgeGraph) AddRelation(subjectID, predicate, objectID string, weight float64) (*KGRelation, error) {
	kg.mutex.Lock()
	defer kg.mutex.Unlock()

	relationID := kg.generateRelationID(subjectID, predicate, objectID)

	relation := &KGRelation{
		ID:         relationID,
		Type:       kg.classifyRelationType(predicate),
		Subject:    subjectID,
		Predicate:  predicate,
		Object:     objectID,
		Weight:     weight,
		Confidence: 0.7,
		Properties: make(map[string]interface{}),
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	kg.relations[relationID] = relation

	// 更新实体的关系列表
	if subject, exists := kg.entities[subjectID]; exists {
		subject.Relations = append(subject.Relations, relationID)
	}
	if object, exists := kg.entities[objectID]; exists {
		object.Relations = append(object.Relations, relationID)
	}

	log.Printf("🔗 添加关系: %s -[%s]-> %s", subjectID, predicate, objectID)

	return relation, nil
}

// AddConcept 添加概念
func (kg *KnowledgeGraph) AddConcept(name, description, category string, level int) (*KGConcept, error) {
	kg.mutex.Lock()
	defer kg.mutex.Unlock()

	conceptID := kg.generateConceptID(name, category)

	concept := &KGConcept{
		ID:          conceptID,
		Name:        name,
		Description: description,
		Category:    category,
		Level:       level,
		Parents:     []string{},
		Children:    []string{},
		Entities:    []string{},
		Properties:  make(map[string]interface{}),
		Confidence:  0.9,
		CreatedAt:   time.Now(),
		UpdatedAt:   time.Now(),
		Metadata:    make(map[string]interface{}),
	}

	kg.concepts[conceptID] = concept

	log.Printf("💡 添加概念: %s (%s)", name, category)

	return concept, nil
}

// Query 查询知识图谱
func (kg *KnowledgeGraph) Query(query *KGQuery) (*KGQueryResult, error) {
	startTime := time.Now()

	log.Printf("🔍 执行知识图谱查询: 类型=%s", query.Type)

	result := &KGQueryResult{
		Query:      query,
		Entities:   []*KGEntity{},
		Relations:  []*KGRelation{},
		Concepts:   []*KGConcept{},
		Paths:      []KGPath{},
		TotalCount: 0,
		Confidence: 0.0,
		Metadata:   make(map[string]interface{}),
	}

	kg.mutex.RLock()
	defer kg.mutex.RUnlock()

	switch query.Type {
	case "entity":
		result.Entities = kg.queryEntities(query)
	case "relation":
		result.Relations = kg.queryRelations(query)
	case "concept":
		result.Concepts = kg.queryConcepts(query)
	case "path":
		result.Paths = kg.queryPaths(query)
	default:
		return nil, fmt.Errorf("不支持的查询类型: %s", query.Type)
	}

	result.TotalCount = len(result.Entities) + len(result.Relations) + len(result.Concepts) + len(result.Paths)
	result.ProcessingTime = time.Since(startTime)
	result.Confidence = kg.calculateQueryConfidence(result)

	log.Printf("✅ 知识图谱查询完成: %d 个结果, 耗时=%v", result.TotalCount, result.ProcessingTime)

	return result, nil
}

// ExtractKnowledgeFromText 从文本中提取知识
func (kg *KnowledgeGraph) ExtractKnowledgeFromText(text string) error {
	if !kg.integratedProcessor.IsInitialized() {
		return fmt.Errorf("NLP处理器未初始化")
	}

	log.Printf("📖 从文本中提取知识: %s", text[:min(len(text), 50)])

	// 使用NLP处理器分析文本
	nlpResult := kg.integratedProcessor.ProcessText(text)

	// 提取实体
	for _, entity := range nlpResult.ExtractedEntities {
		if entity.Confidence > 0.6 {
			properties := map[string]interface{}{
				"source_text": text,
				"confidence":  entity.Confidence,
				"attributes":  entity.Attributes,
			}
			kg.AddEntity(entity.Text, entity.Type, properties)
		}
	}

	// 提取关系
	for _, relation := range nlpResult.Relations {
		if relation.Confidence > 0.5 {
			// 查找或创建主体和客体实体
			subjectID := kg.findOrCreateEntity(relation.Subject, "UNKNOWN")
			objectID := kg.findOrCreateEntity(relation.Object, "UNKNOWN")

			if subjectID != "" && objectID != "" {
				kg.AddRelation(subjectID, relation.Predicate, objectID, relation.Confidence)
			}
		}
	}

	log.Printf("✅ 知识提取完成")

	return nil
}

// initializeBaseConcepts 初始化基础概念
func (kg *KnowledgeGraph) initializeBaseConcepts() {
	log.Printf("💡 初始化基础概念...")

	// 技术领域概念
	kg.AddConcept("编程语言", "用于编写计算机程序的形式语言", "technology", 1)
	kg.AddConcept("数据库", "存储和管理数据的系统", "technology", 1)
	kg.AddConcept("算法", "解决问题的步骤和方法", "technology", 1)
	kg.AddConcept("框架", "软件开发的基础结构", "technology", 1)

	// 业务领域概念
	kg.AddConcept("项目管理", "规划、执行和控制项目的过程", "business", 1)
	kg.AddConcept("用户体验", "用户与产品交互的整体体验", "business", 1)

	// 学习领域概念
	kg.AddConcept("教程", "教学指导材料", "education", 1)
	kg.AddConcept("文档", "说明和参考资料", "education", 1)

	log.Printf("✅ 基础概念初始化完成")
}

// initializeInferenceRules 初始化推理规则
func (kg *KnowledgeGraph) initializeInferenceRules() {
	log.Printf("🧮 初始化推理规则...")

	// 传递性规则
	rule1 := &KGRule{
		ID:         "rule_transitivity",
		Name:       "传递性规则",
		Type:       "inference",
		Condition:  "A -[is_a]-> B AND B -[is_a]-> C",
		Action:     "INFER A -[is_a]-> C",
		Priority:   1,
		Enabled:    true,
		Confidence: 0.8,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	// 对称性规则
	rule2 := &KGRule{
		ID:         "rule_symmetry",
		Name:       "对称性规则",
		Type:       "inference",
		Condition:  "A -[similar_to]-> B",
		Action:     "INFER B -[similar_to]-> A",
		Priority:   2,
		Enabled:    true,
		Confidence: 0.9,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	// 概念归属规则
	rule3 := &KGRule{
		ID:         "rule_concept_membership",
		Name:       "概念归属规则",
		Type:       "inference",
		Condition:  "A -[instance_of]-> B AND B -[subclass_of]-> C",
		Action:     "INFER A -[instance_of]-> C",
		Priority:   3,
		Enabled:    true,
		Confidence: 0.7,
		CreatedAt:  time.Now(),
		UpdatedAt:  time.Now(),
		Metadata:   make(map[string]interface{}),
	}

	kg.rules = append(kg.rules, rule1, rule2, rule3)

	log.Printf("✅ 推理规则初始化完成: %d 个规则", len(kg.rules))
}

// generateEntityID 生成实体ID
func (kg *KnowledgeGraph) generateEntityID(name, entityType string) string {
	return fmt.Sprintf("entity_%s_%s_%d",
		strings.ToLower(strings.ReplaceAll(entityType, " ", "_")),
		strings.ToLower(strings.ReplaceAll(name, " ", "_")),
		time.Now().Unix())
}

// generateRelationID 生成关系ID
func (kg *KnowledgeGraph) generateRelationID(subject, predicate, object string) string {
	return fmt.Sprintf("relation_%s_%s_%s_%d",
		subject,
		strings.ToLower(strings.ReplaceAll(predicate, " ", "_")),
		object,
		time.Now().Unix())
}

// generateConceptID 生成概念ID
func (kg *KnowledgeGraph) generateConceptID(name, category string) string {
	return fmt.Sprintf("concept_%s_%s_%d",
		strings.ToLower(strings.ReplaceAll(category, " ", "_")),
		strings.ToLower(strings.ReplaceAll(name, " ", "_")),
		time.Now().Unix())
}

// classifyRelationType 分类关系类型
func (kg *KnowledgeGraph) classifyRelationType(predicate string) string {
	predicateLower := strings.ToLower(predicate)

	// 层次关系
	if strings.Contains(predicateLower, "是") || strings.Contains(predicateLower, "属于") ||
		strings.Contains(predicateLower, "is") || strings.Contains(predicateLower, "belongs") {
		return "hierarchical"
	}

	// 因果关系
	if strings.Contains(predicateLower, "导致") || strings.Contains(predicateLower, "引起") ||
		strings.Contains(predicateLower, "causes") || strings.Contains(predicateLower, "leads") {
		return "causal"
	}

	// 关联关系
	if strings.Contains(predicateLower, "相关") || strings.Contains(predicateLower, "关联") ||
		strings.Contains(predicateLower, "related") || strings.Contains(predicateLower, "associated") {
		return "associative"
	}

	// 功能关系
	if strings.Contains(predicateLower, "用于") || strings.Contains(predicateLower, "实现") ||
		strings.Contains(predicateLower, "used") || strings.Contains(predicateLower, "implements") {
		return "functional"
	}

	return "general"
}

// queryEntities 查询实体
func (kg *KnowledgeGraph) queryEntities(query *KGQuery) []*KGEntity {
	var results []*KGEntity

	for _, entity := range kg.entities {
		if kg.matchesEntityQuery(entity, query) {
			results = append(results, entity)
		}
	}

	// 按置信度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Confidence > results[j].Confidence
	})

	// 限制结果数量
	if query.Limit > 0 && len(results) > query.Limit {
		results = results[:query.Limit]
	}

	return results
}

// queryRelations 查询关系
func (kg *KnowledgeGraph) queryRelations(query *KGQuery) []*KGRelation {
	var results []*KGRelation

	for _, relation := range kg.relations {
		if kg.matchesRelationQuery(relation, query) {
			results = append(results, relation)
		}
	}

	// 按权重排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Weight > results[j].Weight
	})

	// 限制结果数量
	if query.Limit > 0 && len(results) > query.Limit {
		results = results[:query.Limit]
	}

	return results
}

// queryConcepts 查询概念
func (kg *KnowledgeGraph) queryConcepts(query *KGQuery) []*KGConcept {
	var results []*KGConcept

	for _, concept := range kg.concepts {
		if kg.matchesConceptQuery(concept, query) {
			results = append(results, concept)
		}
	}

	// 按置信度排序
	sort.Slice(results, func(i, j int) bool {
		return results[i].Confidence > results[j].Confidence
	})

	// 限制结果数量
	if query.Limit > 0 && len(results) > query.Limit {
		results = results[:query.Limit]
	}

	return results
}

// queryPaths 查询路径
func (kg *KnowledgeGraph) queryPaths(query *KGQuery) []KGPath {
	var paths []KGPath

	if query.Subject == "" || query.Object == "" {
		return paths
	}

	// 使用广度优先搜索查找路径
	paths = kg.findPaths(query.Subject, query.Object, query.Depth)

	// 按权重排序
	sort.Slice(paths, func(i, j int) bool {
		return paths[i].Weight > paths[j].Weight
	})

	// 限制结果数量
	if query.Limit > 0 && len(paths) > query.Limit {
		paths = paths[:query.Limit]
	}

	return paths
}

// matchesEntityQuery 检查实体是否匹配查询
func (kg *KnowledgeGraph) matchesEntityQuery(entity *KGEntity, query *KGQuery) bool {
	// 检查名称匹配
	if query.Subject != "" && !strings.Contains(strings.ToLower(entity.Name), strings.ToLower(query.Subject)) {
		return false
	}

	// 检查类型匹配
	if typeFilter, exists := query.Filters["type"]; exists {
		if typeStr, ok := typeFilter.(string); ok && entity.Type != typeStr {
			return false
		}
	}

	// 检查置信度阈值
	if confFilter, exists := query.Filters["min_confidence"]; exists {
		if confFloat, ok := confFilter.(float64); ok && entity.Confidence < confFloat {
			return false
		}
	}

	return true
}

// matchesRelationQuery 检查关系是否匹配查询
func (kg *KnowledgeGraph) matchesRelationQuery(relation *KGRelation, query *KGQuery) bool {
	// 检查主体匹配
	if query.Subject != "" && relation.Subject != query.Subject {
		return false
	}

	// 检查谓词匹配
	if query.Predicate != "" && !strings.Contains(strings.ToLower(relation.Predicate), strings.ToLower(query.Predicate)) {
		return false
	}

	// 检查客体匹配
	if query.Object != "" && relation.Object != query.Object {
		return false
	}

	// 检查类型匹配
	if typeFilter, exists := query.Filters["type"]; exists {
		if typeStr, ok := typeFilter.(string); ok && relation.Type != typeStr {
			return false
		}
	}

	return true
}

// matchesConceptQuery 检查概念是否匹配查询
func (kg *KnowledgeGraph) matchesConceptQuery(concept *KGConcept, query *KGQuery) bool {
	// 检查名称匹配
	if query.Subject != "" && !strings.Contains(strings.ToLower(concept.Name), strings.ToLower(query.Subject)) {
		return false
	}

	// 检查类别匹配
	if categoryFilter, exists := query.Filters["category"]; exists {
		if categoryStr, ok := categoryFilter.(string); ok && concept.Category != categoryStr {
			return false
		}
	}

	// 检查层级匹配
	if levelFilter, exists := query.Filters["level"]; exists {
		if levelInt, ok := levelFilter.(int); ok && concept.Level != levelInt {
			return false
		}
	}

	return true
}

// findPaths 查找路径
func (kg *KnowledgeGraph) findPaths(startID, endID string, maxDepth int) []KGPath {
	var paths []KGPath

	if maxDepth <= 0 {
		maxDepth = 3 // 默认最大深度
	}

	// 使用深度优先搜索查找路径
	visited := make(map[string]bool)
	currentPath := KGPath{
		Nodes:      []KGNode{},
		Edges:      []KGEdge{},
		Length:     0,
		Weight:     1.0,
		Confidence: 1.0,
		Metadata:   make(map[string]interface{}),
	}

	kg.dfsPath(startID, endID, maxDepth, 0, visited, currentPath, &paths)

	return paths
}

// dfsPath 深度优先搜索路径
func (kg *KnowledgeGraph) dfsPath(currentID, targetID string, maxDepth, currentDepth int, visited map[string]bool, currentPath KGPath, paths *[]KGPath) {
	if currentDepth > maxDepth {
		return
	}

	if visited[currentID] {
		return
	}

	visited[currentID] = true
	defer func() { visited[currentID] = false }()

	// 添加当前节点到路径
	if entity, exists := kg.entities[currentID]; exists {
		node := KGNode{
			ID:         currentID,
			Type:       "entity",
			Label:      entity.Name,
			Properties: entity.Properties,
		}
		currentPath.Nodes = append(currentPath.Nodes, node)
		currentPath.Length++
	}

	// 如果到达目标，保存路径
	if currentID == targetID {
		pathCopy := currentPath
		pathCopy.Nodes = make([]KGNode, len(currentPath.Nodes))
		copy(pathCopy.Nodes, currentPath.Nodes)
		pathCopy.Edges = make([]KGEdge, len(currentPath.Edges))
		copy(pathCopy.Edges, currentPath.Edges)
		*paths = append(*paths, pathCopy)
		return
	}

	// 继续搜索相邻节点
	for _, relation := range kg.relations {
		var nextID string
		if relation.Subject == currentID {
			nextID = relation.Object
		} else if relation.Object == currentID {
			nextID = relation.Subject
		} else {
			continue
		}

		// 添加边到路径
		edge := KGEdge{
			ID:         relation.ID,
			Type:       relation.Type,
			Label:      relation.Predicate,
			Source:     currentID,
			Target:     nextID,
			Weight:     relation.Weight,
			Properties: relation.Properties,
		}
		currentPath.Edges = append(currentPath.Edges, edge)
		currentPath.Weight *= relation.Weight
		currentPath.Confidence *= relation.Confidence

		// 递归搜索
		kg.dfsPath(nextID, targetID, maxDepth, currentDepth+1, visited, currentPath, paths)

		// 回溯
		currentPath.Edges = currentPath.Edges[:len(currentPath.Edges)-1]
		currentPath.Weight /= relation.Weight
		currentPath.Confidence /= relation.Confidence
	}
}

// calculateQueryConfidence 计算查询置信度
func (kg *KnowledgeGraph) calculateQueryConfidence(result *KGQueryResult) float64 {
	if result.TotalCount == 0 {
		return 0.0
	}

	totalConfidence := 0.0
	count := 0

	for _, entity := range result.Entities {
		totalConfidence += entity.Confidence
		count++
	}

	for _, relation := range result.Relations {
		totalConfidence += relation.Confidence
		count++
	}

	for _, concept := range result.Concepts {
		totalConfidence += concept.Confidence
		count++
	}

	for _, path := range result.Paths {
		totalConfidence += path.Confidence
		count++
	}

	if count == 0 {
		return 0.0
	}

	return totalConfidence / float64(count)
}

// findOrCreateEntity 查找或创建实体
func (kg *KnowledgeGraph) findOrCreateEntity(name, entityType string) string {
	// 查找现有实体
	for id, entity := range kg.entities {
		if strings.EqualFold(entity.Name, name) {
			return id
		}
	}

	// 创建新实体
	entity, err := kg.AddEntity(name, entityType, make(map[string]interface{}))
	if err != nil {
		log.Printf("⚠️ 创建实体失败: %v", err)
		return ""
	}

	return entity.ID
}

// updateStats 更新统计信息
func (kg *KnowledgeGraph) updateStats() {
	kg.stats.EntityCount = len(kg.entities)
	kg.stats.RelationCount = len(kg.relations)
	kg.stats.ConceptCount = len(kg.concepts)
	kg.stats.RuleCount = len(kg.rules)

	// 统计类型分布
	kg.stats.EntityTypes = make(map[string]int)
	kg.stats.RelationTypes = make(map[string]int)
	kg.stats.ConceptTypes = make(map[string]int)

	totalConfidence := 0.0
	count := 0

	for _, entity := range kg.entities {
		kg.stats.EntityTypes[entity.Type]++
		totalConfidence += entity.Confidence
		count++
	}

	for _, relation := range kg.relations {
		kg.stats.RelationTypes[relation.Type]++
		totalConfidence += relation.Confidence
		count++
	}

	for _, concept := range kg.concepts {
		kg.stats.ConceptTypes[concept.Category]++
		totalConfidence += concept.Confidence
		count++
	}

	if count > 0 {
		kg.stats.AvgConfidence = totalConfidence / float64(count)
	}

	// 计算完整性和一致性（简化版本）
	kg.stats.Completeness = kg.calculateCompleteness()
	kg.stats.Consistency = kg.calculateConsistency()

	kg.stats.LastUpdated = time.Now()
}

// calculateCompleteness 计算完整性
func (kg *KnowledgeGraph) calculateCompleteness() float64 {
	if len(kg.entities) == 0 {
		return 0.0
	}

	// 简单的完整性计算：有关系的实体比例
	entitiesWithRelations := 0
	for _, entity := range kg.entities {
		if len(entity.Relations) > 0 {
			entitiesWithRelations++
		}
	}

	return float64(entitiesWithRelations) / float64(len(kg.entities))
}

// calculateConsistency 计算一致性
func (kg *KnowledgeGraph) calculateConsistency() float64 {
	// 简化的一致性计算：高置信度项目的比例
	highConfidenceCount := 0
	totalCount := 0

	for _, entity := range kg.entities {
		if entity.Confidence > 0.7 {
			highConfidenceCount++
		}
		totalCount++
	}

	for _, relation := range kg.relations {
		if relation.Confidence > 0.7 {
			highConfidenceCount++
		}
		totalCount++
	}

	if totalCount == 0 {
		return 1.0
	}

	return float64(highConfidenceCount) / float64(totalCount)
}

// GetStats 获取统计信息
func (kg *KnowledgeGraph) GetStats() *KGStats {
	kg.mutex.RLock()
	defer kg.mutex.RUnlock()

	kg.updateStats()
	return kg.stats
}
