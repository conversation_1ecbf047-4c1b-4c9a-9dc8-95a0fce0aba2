package main

import (
	"fmt"
	"faq-system/internal/algorithm"
	"faq-system/internal/rag"
)

func main() {
	fmt.Println("🧮 测试数学表达式识别...")
	
	// 创建算法识别器
	recognizer := algorithm.NewRecognizer()
	
	// 创建意图分类器
	intentClassifier := rag.NewIntentClassifier()
	
	// 测试用例
	testCases := []string{
		"1+1",
		"1+1等于多少",
		"1+1=?",
		"计算1+1",
		"求1+1的结果",
		"2*3",
		"10/2",
		"5-3",
	}
	
	fmt.Println("\n=== 算法识别器测试 ===")
	for _, testCase := range testCases {
		result := recognizer.RecognizeAlgorithm(testCase)
		fmt.Printf("输入: %s\n", testCase)
		fmt.Printf("  类型: %v\n", result.Type)
		fmt.Printf("  表达式: %s\n", result.Expression)
		fmt.Printf("  置信度: %.2f\n", result.Confidence)
		fmt.Println()
	}
	
	fmt.Println("\n=== 意图分类器测试 ===")
	for _, testCase := range testCases {
		intent := intentClassifier.ClassifyIntent(testCase)
		fmt.Printf("输入: %s -> 意图: %s\n", testCase, intent)
	}
}
