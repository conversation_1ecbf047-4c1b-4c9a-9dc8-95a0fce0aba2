<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试知识管理CRUD功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .test-section {
            background: white;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .test-button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            margin: 5px;
            border-radius: 4px;
            cursor: pointer;
        }
        .test-button:hover {
            background: #0056b3;
        }
        .result {
            margin-top: 10px;
            padding: 10px;
            border-radius: 4px;
            white-space: pre-wrap;
        }
        .success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
    </style>
</head>
<body>
    <h1>🧪 知识管理CRUD功能测试</h1>
    
    <div class="test-section">
        <h2>📝 创建知识测试</h2>
        <button class="test-button" onclick="testCreateKnowledge()">创建测试知识</button>
        <div id="create-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📖 查看知识测试</h2>
        <button class="test-button" onclick="testGetKnowledgeList()">获取知识列表</button>
        <button class="test-button" onclick="testGetKnowledgeDetail()">查看知识详情</button>
        <div id="read-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>✏️ 更新知识测试</h2>
        <button class="test-button" onclick="testUpdateKnowledge()">更新知识</button>
        <div id="update-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>🗑️ 删除知识测试</h2>
        <button class="test-button" onclick="testDeleteKnowledge()">删除知识</button>
        <div id="delete-result" class="result"></div>
    </div>

    <div class="test-section">
        <h2>📊 统计信息测试</h2>
        <button class="test-button" onclick="testGetStatistics()">获取统计信息</button>
        <div id="stats-result" class="result"></div>
    </div>

    <script>
        const baseURL = 'http://localhost:8082';
        let testKnowledgeId = null;

        // 显示结果
        function showResult(elementId, message, type = 'info') {
            const element = document.getElementById(elementId);
            element.textContent = message;
            element.className = `result ${type}`;
        }

        // 测试创建知识
        async function testCreateKnowledge() {
            const resultDiv = 'create-result';
            showResult(resultDiv, '正在创建测试知识...', 'info');
            
            const testData = {
                question: "什么是Vue.js？",
                answer: "Vue.js是一个用于构建用户界面的渐进式JavaScript框架。它被设计为可以自底向上逐层应用。",
                category: "technology",
                keywords: ["Vue.js", "JavaScript", "框架", "前端"],
                confidence: 0.9,
                source: "user_input",
                learned_from: "test_user"
            };

            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(testData)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    testKnowledgeId = result.data.id;
                    showResult(resultDiv, `✅ 创建成功！\nID: ${testKnowledgeId}\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 创建失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 测试获取知识列表
        async function testGetKnowledgeList() {
            const resultDiv = 'read-result';
            showResult(resultDiv, '正在获取知识列表...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge?limit=5`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(resultDiv, `✅ 获取成功！\n总数: ${result.total}\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 测试获取知识详情
        async function testGetKnowledgeDetail() {
            const resultDiv = 'read-result';
            
            if (!testKnowledgeId) {
                showResult(resultDiv, '❌ 请先创建测试知识', 'error');
                return;
            }
            
            showResult(resultDiv, `正在获取知识详情 (ID: ${testKnowledgeId})...`, 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge/${testKnowledgeId}`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(resultDiv, `✅ 获取详情成功！\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取详情失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 测试更新知识
        async function testUpdateKnowledge() {
            const resultDiv = 'update-result';
            
            if (!testKnowledgeId) {
                showResult(resultDiv, '❌ 请先创建测试知识', 'error');
                return;
            }
            
            showResult(resultDiv, `正在更新知识 (ID: ${testKnowledgeId})...`, 'info');
            
            const updateData = {
                question: "什么是Vue.js？（已更新）",
                answer: "Vue.js是一个用于构建用户界面的渐进式JavaScript框架。它被设计为可以自底向上逐层应用。Vue.js的核心库只关注视图层，易于上手。",
                category: "technology",
                keywords: ["Vue.js", "JavaScript", "框架", "前端", "MVVM"],
                confidence: 0.95,
                source: "user_input",
                learned_from: "test_user_updated"
            };

            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge/${testKnowledgeId}`, {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                    },
                    body: JSON.stringify(updateData)
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(resultDiv, `✅ 更新成功！\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 更新失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 测试删除知识
        async function testDeleteKnowledge() {
            const resultDiv = 'delete-result';
            
            if (!testKnowledgeId) {
                showResult(resultDiv, '❌ 请先创建测试知识', 'error');
                return;
            }
            
            if (!confirm(`确定要删除测试知识 (ID: ${testKnowledgeId}) 吗？`)) {
                return;
            }
            
            showResult(resultDiv, `正在删除知识 (ID: ${testKnowledgeId})...`, 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/knowledge/${testKnowledgeId}`, {
                    method: 'DELETE'
                });

                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(resultDiv, `✅ 删除成功！\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                    testKnowledgeId = null; // 重置ID
                } else {
                    showResult(resultDiv, `❌ 删除失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }

        // 测试获取统计信息
        async function testGetStatistics() {
            const resultDiv = 'stats-result';
            showResult(resultDiv, '正在获取统计信息...', 'info');
            
            try {
                const response = await fetch(`${baseURL}/api/learning/statistics`);
                const result = await response.json();
                
                if (response.ok && result.success) {
                    showResult(resultDiv, `✅ 获取统计信息成功！\n响应: ${JSON.stringify(result, null, 2)}`, 'success');
                } else {
                    showResult(resultDiv, `❌ 获取统计信息失败：${result.message || '未知错误'}`, 'error');
                }
            } catch (error) {
                showResult(resultDiv, `❌ 网络错误：${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>
