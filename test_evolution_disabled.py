#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试进化改进功能是否已被禁用
"""

import requests
import json
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8082"
EVOLUTION_ENDPOINT = f"{SERVER_URL}/api/learning/evolution/apply"

def test_evolution_disabled():
    """测试进化改进功能是否已被禁用"""
    print("🔧 测试进化改进功能禁用状态")
    print("=" * 50)
    print(f"⏰ 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    try:
        print(f"🔍 尝试访问进化改进API: {EVOLUTION_ENDPOINT}")
        response = requests.post(EVOLUTION_ENDPOINT,
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        print(f"📊 响应状态码: {response.status_code}")
        
        if response.status_code == 404:
            print("✅ 进化改进API已被禁用 (404 Not Found)")
            print("🎯 这表明路由已被注释掉，功能成功禁用")
            return True
        elif response.status_code == 503:
            # 服务不可用，可能是进化引擎被禁用
            try:
                data = response.json()
                print(f"📝 响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
                
                if "进化引擎不可用" in data.get("error", ""):
                    print("✅ 进化改进功能已被禁用 (进化引擎不可用)")
                    print("🎯 这表明进化引擎已被设置为nil，功能成功禁用")
                    return True
                else:
                    print("⚠️ 服务不可用，但原因不明")
                    return False
            except:
                print("⚠️ 无法解析响应内容")
                return False
        elif response.status_code == 200:
            print("❌ 进化改进功能仍然可用！")
            try:
                data = response.json()
                print(f"📝 响应内容: {json.dumps(data, indent=2, ensure_ascii=False)}")
            except:
                print("📝 响应内容无法解析")
            return False
        else:
            print(f"⚠️ 意外的响应状态码: {response.status_code}")
            try:
                print(f"📝 响应内容: {response.text}")
            except:
                pass
            return False
            
    except requests.exceptions.ConnectionError:
        print("❌ 无法连接到服务器")
        print("💡 请确保服务器正在运行")
        return False
    except requests.exceptions.Timeout:
        print("❌ 请求超时")
        return False
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 进化改进功能禁用状态测试")
    print("=" * 60)
    
    success = test_evolution_disabled()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 测试结果: 进化改进功能已成功禁用")
        print("\n📋 禁用的功能包括:")
        print("• 🚫 进化改进API路由 (/api/learning/evolution/apply)")
        print("• 🚫 进化引擎创建和初始化")
        print("• 🚫 定时进化改进任务")
        print("• 🚫 用户思维模式分析")
        print("• 🚫 自动知识学习和持久化")
        
        print("\n💡 如需重新启用，请:")
        print("1. 取消注释 internal/server/server.go 中的路由")
        print("2. 取消注释 internal/learning/manager.go 中的进化引擎创建")
        print("3. 取消注释 internal/learning/manager.go 中的定时任务")
    else:
        print("⚠️ 测试结果: 进化改进功能可能仍然可用")
        print("💡 请检查禁用配置是否正确")
    
    print(f"\n⏰ 测试完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

if __name__ == "__main__":
    main()
