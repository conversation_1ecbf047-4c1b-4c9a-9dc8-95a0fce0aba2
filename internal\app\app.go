package app

import (
	"database/sql"
	"fmt"
	"time"

	"faq-system/internal/config"
	"faq-system/internal/crawler"
	"faq-system/internal/database"

	// "	// "faq-system/internal/embedding" // 已移除 - 使用NLP处理器" // 已移除 - 使用NLP处理器
	"faq-system/internal/health"
	"faq-system/internal/learning"
	"faq-system/internal/logger"
	"faq-system/internal/mysql"
	"faq-system/internal/nlp"
	"faq-system/internal/rag"
	"faq-system/internal/server"
	"faq-system/internal/vectorstore"
)

// Application 应用程序结构
type Application struct {
	Config      *config.Config
	Server      *server.Server
	RAGSystem   *rag.ChatSystem
	VectorStore *vectorstore.VectorStore
	// EmbedClient已移除 - 使用NLP处理器替代
	// FAQs字段已移除 - 使用learned_knowledge表
	DB      *sql.DB
	Crawler *crawler.KnowledgeCrawler
}

// New 创建新的应用程序实例
func New() (*Application, error) {
	app := &Application{}

	// 1. 初始化日志系统
	logger.Init()
	logger.Info("Starting FAQ System...")

	// 2. 加载配置
	cfg, err := config.LoadConfig("config.yaml")
	if err != nil {
		// 如果配置文件不存在，使用默认配置
		logger.Warn("Config file not found, using default config")
		cfg = config.Load()
	}
	app.Config = cfg

	logger.Infof("Config loaded: MySQL=%s:%s (LocalAI已移除，使用NLP处理器)",
		cfg.MySQL.Host, cfg.MySQL.Port)

	// 3. 初始化数据库
	if err := app.initializeDatabase(); err != nil {
		return nil, fmt.Errorf("failed to initialize database: %w", err)
	}

	// 4. FAQ数据加载已移除 - 使用learned_knowledge表
	logger.Info("跳过FAQ数据加载，使用learned_knowledge表")

	// 5. 初始化向量存储
	if err := app.initializeVectorStore(); err != nil {
		return nil, fmt.Errorf("failed to initialize vector store: %w", err)
	}

	// 6. 嵌入客户端已移除 - 使用NLP处理器替代
	// app.initializeEmbeddingClient() // 已移除

	// 7. 生成向量嵌入
	if err := app.generateEmbeddings(); err != nil {
		logger.Warnf("Failed to generate embeddings: %v", err)
	}

	// 8. 初始化RAG系统
	app.initializeRAGSystem()

	// 9. 初始化爬虫系统 - 暂时禁用
	// if err := app.initializeCrawler(); err != nil {
	//	logger.Warnf("Failed to initialize crawler: %v", err)
	// }

	// 10. 初始化Web服务器
	if err := app.initializeServer(); err != nil {
		return nil, fmt.Errorf("failed to initialize server: %w", err)
	}

	return app, nil
}

// initializeDatabase 初始化数据库
func (app *Application) initializeDatabase() error {
	logger.Info("Initializing database...")

	// 初始化数据库结构
	if err := database.Initialize(app.Config); err != nil {
		return err
	}

	// 连接数据库并设置app.DB
	db, err := mysql.Connect(app.Config)
	if err != nil {
		return err
	}
	app.DB = db

	logger.Info("Database connection established")
	return nil
}

// loadFAQData 方法已移除 - FAQ表已移除

// initializeVectorStore 初始化向量存储
func (app *Application) initializeVectorStore() error {
	logger.Info("Initializing vector store...")
	store, err := vectorstore.NewVectorStore(app.Config.VectorStore.Path)
	if err != nil {
		return err
	}
	app.VectorStore = store
	return nil
}

// initializeEmbeddingClient 已移除 - 使用NLP处理器替代

// generateEmbeddings 生成向量嵌入 - 已移除FAQ依赖
func (app *Application) generateEmbeddings() error {
	logger.Info("跳过FAQ向量嵌入生成，使用learned_knowledge表")
	return nil
}

// initializeRAGSystem 初始化RAG系统
func (app *Application) initializeRAGSystem() {
	logger.Info("Initializing RAG chat system...")
	// 传递空的FAQ切片和nil EmbedClient，系统将使用learned_knowledge表和NLP处理器
	app.RAGSystem = rag.NewChatSystem(app.VectorStore, nil, []mysql.FAQ{})
	logger.Info("RAG chat system initialized")
}

// initializeServer 初始化Web服务器
func (app *Application) initializeServer() error {
	logger.Info("Initializing web server...")

	// 创建健康检查器
	healthChecker := health.NewChecker(app.Config, app.VectorStore)

	// 创建学习管理器
	var learningManager *learning.Manager
	if app.DB != nil {
		learningManager = learning.NewManager(app.DB)

		// 设置向量存储到知识学习器（嵌入客户端已移除）
		knowledgeLearner := learningManager.GetKnowledgeLearner()
		knowledgeLearner.SetVectorStore(app.VectorStore)
		// knowledgeLearner.SetEmbedClient(app.EmbedClient) // 已移除 - 使用NLP处理器

		// 从爬虫系统获取NLP处理器并设置到知识学习器
		if app.Crawler != nil {
			// 这里需要从爬虫系统获取NLP处理器
			// 暂时跳过，因为需要重构爬虫系统的接口
			logger.Info("🧠 NLP处理器将在爬虫初始化后设置")
		}

		logger.Info("🔗 向量存储和嵌入客户端已设置到知识学习器")

		// 启动学习系统
		if err := learningManager.Start(); err != nil {
			logger.Warnf("Failed to start learning system: %v", err)
		} else {
			logger.Info("Learning system started successfully")
			// 将学习引擎设置到RAG系统
			app.RAGSystem.SetLearningEngine(learningManager.GetEngine())

			// 创建并设置智能进化匹配器
			evolutionMatcher := learning.NewSmartMatcher(app.DB)
			app.RAGSystem.SetEvolutionMatcher(evolutionMatcher)
			logger.Info("🧠 智能进化匹配器已启用")

			// 设置知识学习器到RAG系统
			app.RAGSystem.SetKnowledgeLearner(knowledgeLearner)
			logger.Info("📚 知识学习器已启用")
		}
	}

	// 创建服务器
	srv, err := server.New(app.Config, app.RAGSystem, healthChecker, learningManager, app.Crawler)
	if err != nil {
		return err
	}
	app.Server = srv
	return nil
}

// Run 运行应用程序
func (app *Application) Run() error {
	logger.Infof("Starting server on %s", app.Config.GetServerAddress())
	return app.Server.Start()
}

// Shutdown 关闭应用程序
func (app *Application) Shutdown() error {
	logger.Info("Shutting down application...")

	if app.VectorStore != nil {
		if err := app.VectorStore.Close(); err != nil {
			logger.Errorf("Failed to close vector store: %v", err)
		}
	}

	if app.Server != nil {
		return app.Server.Shutdown()
	}

	return nil
}

// initializeCrawler 初始化爬虫系统（集成NLP向量化）
func (app *Application) initializeCrawler() error {
	logger.Info("Initializing knowledge crawler with NLP vectorization...")

	// 创建学习管理器来获取知识学习器
	var knowledgeLearner *learning.KnowledgeLearner
	if app.DB != nil {
		learningManager := learning.NewManager(app.DB)
		knowledgeLearner = learningManager.GetKnowledgeLearner()
	}

	// 初始化NLP集成处理器
	logger.Info("🧠 Initializing NLP integrated processor...")
	integratedProcessor := nlp.NewIntegratedProcessor()

	// 等待NLP处理器初始化（非阻塞）
	go func() {
		for i := 0; i < 30; i++ { // 最多等待30秒
			if integratedProcessor.IsInitialized() {
				logger.Info("✅ NLP integrated processor initialized")
				break
			}
			logger.Infof("⏳ Waiting for NLP processor initialization... (%d/30)", i+1)
			time.Sleep(time.Second)
		}
		if !integratedProcessor.IsInitialized() {
			logger.Warn("⚠️ NLP processor initialization timeout, will use fallback methods")
		}
	}()

	// 创建爬虫实例（传递NLP组件，不使用EmbedClient）
	app.Crawler = crawler.NewKnowledgeCrawlerWithNLP(app.DB, knowledgeLearner, nil, integratedProcessor)

	// 启动爬虫 - 暂时禁用自动启动
	// if err := app.Crawler.Start(); err != nil {
	//	return fmt.Errorf("failed to start crawler: %w", err)
	// }

	logger.Info("✅ Knowledge crawler with NLP vectorization initialized (auto-start disabled)")
	return nil
}
