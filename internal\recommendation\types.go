package recommendation

import (
	"faq-system/internal/learning"
	"time"
)

// RecommendationContext 推荐上下文
type RecommendationContext struct {
	Question         string                     `json:"question"`          // 用户问题
	Answer           string                     `json:"answer"`            // 系统回答
	UserID           string                     `json:"user_id"`           // 用户ID
	Intent           string                     `json:"intent"`            // 意图类型
	Source           string                     `json:"source"`            // 回答来源
	Confidence       float64                    `json:"confidence"`        // 置信度
	KnowledgeID      int                        `json:"knowledge_id"`      // 相关知识ID
	KnowledgeTopic   string                     `json:"knowledge_topic"`   // 知识点标志
	CurrentKnowledge *learning.LearnedKnowledge `json:"current_knowledge"` // 当前知识对象
}

// RecommendationResult 推荐结果
type RecommendationResult struct {
	Success           bool                         `json:"success"`            // 是否成功
	RecommendationType string                      `json:"recommendation_type"` // 推荐类型
	KnowledgeTopic    string                       `json:"knowledge_topic"`     // 知识点标志
	Items             []*RecommendationItem        `json:"items"`               // 推荐项目
	FormattedText     string                       `json:"formatted_text"`      // 格式化文本
	Metadata          map[string]interface{}       `json:"metadata"`            // 元数据
}

// RecommendationItem 推荐项目
type RecommendationItem struct {
	ID           int                        `json:"id"`            // 知识ID
	Question     string                     `json:"question"`      // 问题
	Answer       string                     `json:"answer"`        // 答案
	Confidence   float64                    `json:"confidence"`    // 置信度
	Relevance    float64                    `json:"relevance"`     // 相关性分数
	Topic        string                     `json:"topic"`         // 知识点标志
	Source       string                     `json:"source"`        // 来源
	CreatedAt    time.Time                  `json:"created_at"`    // 创建时间
	Knowledge    *learning.LearnedKnowledge `json:"knowledge"`     // 完整知识对象
}

// RecommendationStrategy 推荐策略接口
type RecommendationStrategy interface {
	// GetRecommendations 获取推荐
	GetRecommendations(ctx *RecommendationContext) (*RecommendationResult, error)
	
	// GetStrategyName 获取策略名称
	GetStrategyName() string
	
	// IsApplicable 判断策略是否适用
	IsApplicable(ctx *RecommendationContext) bool
}

// RecommendationConfig 推荐配置
type RecommendationConfig struct {
	MaxRecommendations int     `json:"max_recommendations"` // 最大推荐数量
	MinConfidence      float64 `json:"min_confidence"`      // 最小置信度
	EnableTopicBased   bool    `json:"enable_topic_based"`  // 启用基于知识点的推荐
	EnableSemanticBased bool   `json:"enable_semantic_based"` // 启用基于语义的推荐
	EnableUserBased    bool    `json:"enable_user_based"`   // 启用基于用户的推荐
}

// DefaultRecommendationConfig 默认推荐配置
func DefaultRecommendationConfig() *RecommendationConfig {
	return &RecommendationConfig{
		MaxRecommendations:  3,
		MinConfidence:       0.3,
		EnableTopicBased:    true,
		EnableSemanticBased: true,
		EnableUserBased:     false, // 暂时禁用，等待用户系统完善
	}
}
