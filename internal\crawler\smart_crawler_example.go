package crawler

import (
	"database/sql"
	"log"
	"time"

	"faq-system/internal/learning"
)

// CreateSmartCrawlExample 创建智能爬虫示例配置
func CreateSmartCrawlExample(db *sql.DB, knowledgeLearner *learning.KnowledgeLearner) {
	// 创建智能爬虫
	_ = NewSmartKnowledgeCrawler(db, knowledgeLearner, nil, nil)

	// 示例1：技术博客深度爬取
	techBlogTarget := &SmartCrawlTarget{
		CrawlTarget: &CrawlTarget{
			Name:     "技术博客深度爬取",
			URL:      "https://blog.csdn.net",
			Type:     "website",
			Category: "technology",
			Keywords: []string{"编程", "技术", "开发", "算法", "架构"},
			Enabled:  true,
		},
		Rules: []CrawlRule{
			{
				URLPattern:      `https://blog\.csdn\.net/.+/article/details/\d+`,
				FollowLinks:     []string{"a[href*='/article/details/']"},
				IgnoreLinks:     []string{"a[href*='/download/']", "a[href*='/ad/']"},
				ContentSelector: ".article_content, .markdown_views",
				TitleSelector:   "h1.title-article",
				MetaSelectors: map[string]string{
					"author":       ".follow-nickName",
					"publish_time": ".time",
					"tags":         ".tags-box a",
				},
				RequiredWords: []string{"技术", "开发", "编程", "算法", "架构", "设计"},
				ExcludedWords: []string{"广告", "推广", "营销"},
				MinWordCount:  200,
				MaxDepth:      3,
			},
		},
		SeedURLs: []string{
			"https://blog.csdn.net/nav/ai",
			"https://blog.csdn.net/nav/web",
			"https://blog.csdn.net/nav/mobile",
		},
		AllowedDomains:   []string{"blog.csdn.net"},
		BlockedDomains:   []string{"ads.csdn.net", "download.csdn.net"},
		CrawlStrategy:    "smart",
		PriorityKeywords: []string{"人工智能", "机器学习", "深度学习", "算法", "数据结构"},
	}

	// 示例2：知识问答网站爬取
	qaTarget := &SmartCrawlTarget{
		CrawlTarget: &CrawlTarget{
			Name:     "知识问答网站",
			URL:      "https://zhihu.com",
			Type:     "website",
			Category: "qa",
			Keywords: []string{"问答", "知识", "解答", "专业"},
			Enabled:  true,
		},
		Rules: []CrawlRule{
			{
				URLPattern:      `https://www\.zhihu\.com/question/\d+`,
				FollowLinks:     []string{"a[href*='/question/']"},
				IgnoreLinks:     []string{"a[href*='/people/']", "a[href*='/topic/']"},
				ContentSelector: ".RichContent-inner",
				TitleSelector:   "h1.QuestionHeader-title",
				MetaSelectors: map[string]string{
					"answer_count": ".List-headerText span",
					"follow_count": ".NumberBoard-itemValue",
				},
				RequiredWords: []string{"什么", "如何", "怎么", "为什么", "是什么"},
				ExcludedWords: []string{"广告", "推广"},
				MinWordCount:  100,
				MaxDepth:      2,
			},
		},
		SeedURLs: []string{
			"https://www.zhihu.com/topic/19551137/hot", // 编程话题
			"https://www.zhihu.com/topic/19554298/hot", // 计算机话题
		},
		AllowedDomains:   []string{"www.zhihu.com"},
		BlockedDomains:   []string{"zhuanlan.zhihu.com"},
		CrawlStrategy:    "breadth_first",
		PriorityKeywords: []string{"编程", "算法", "数据结构", "系统设计"},
	}

	// 示例3：官方文档爬取
	docTarget := &SmartCrawlTarget{
		CrawlTarget: &CrawlTarget{
			Name:     "官方文档爬取",
			URL:      "https://golang.org/doc/",
			Type:     "website",
			Category: "documentation",
			Keywords: []string{"文档", "教程", "指南", "API"},
			Enabled:  true,
		},
		Rules: []CrawlRule{
			{
				URLPattern:      `https://golang\.org/doc/.+`,
				FollowLinks:     []string{"a[href^='/doc/']", "a[href^='/pkg/']"},
				IgnoreLinks:     []string{"a[href*='download']"},
				ContentSelector: "#page .container",
				TitleSelector:   "h1",
				RequiredWords:   []string{"Go", "golang", "function", "package"},
				MinWordCount:    150,
				MaxDepth:        4,
			},
		},
		SeedURLs: []string{
			"https://golang.org/doc/",
			"https://golang.org/doc/tutorial/",
			"https://golang.org/pkg/",
		},
		AllowedDomains:   []string{"golang.org"},
		CrawlStrategy:    "depth_first",
		PriorityKeywords: []string{"Go", "golang", "programming", "tutorial"},
	}

	// 启动智能爬取示例
	log.Println("🚀 启动智能爬虫示例...")

	// 可以选择启动其中一个目标进行测试
	targets := []*SmartCrawlTarget{techBlogTarget, qaTarget, docTarget}

	for i, target := range targets {
		log.Printf("📋 示例 %d: %s", i+1, target.Name)
		log.Printf("   种子URL: %v", target.SeedURLs)
		log.Printf("   允许域名: %v", target.AllowedDomains)
		log.Printf("   爬取策略: %s", target.CrawlStrategy)
		log.Printf("   规则数量: %d", len(target.Rules))

		// 这里只是展示配置，实际使用时取消注释下面的代码
		/*
			if err := smartCrawler.StartSmartCrawl(target); err != nil {
				log.Printf("❌ 启动失败: %v", err)
				continue
			}

			// 运行一段时间后停止
			time.Sleep(time.Minute * 5)
			smartCrawler.StopSmartCrawl()
		*/
	}
}

// SmartCrawlConfig 智能爬虫配置模板
type SmartCrawlConfig struct {
	// 基础配置
	MaxDepth            int     `json:"max_depth"`
	MaxPagesPerDomain   int     `json:"max_pages_per_domain"`
	ContentMinLength    int     `json:"content_min_length"`
	ContentQualityScore float64 `json:"content_quality_score"`

	// 渲染配置
	EnableJSRendering bool `json:"enable_js_rendering"`
	RespectRobotsTxt  bool `json:"respect_robots_txt"`

	// 反爬虫配置
	UserAgentRotation []string      `json:"user_agent_rotation"`
	ProxyList         []string      `json:"proxy_list"`
	RequestDelay      time.Duration `json:"request_delay"`
	RandomDelay       bool          `json:"random_delay"`

	// 性能配置
	MaxConcurrency int           `json:"max_concurrency"`
	Timeout        time.Duration `json:"timeout"`
}

// GetDefaultSmartConfig 获取默认智能爬虫配置
func GetDefaultSmartConfig() *SmartCrawlConfig {
	return &SmartCrawlConfig{
		MaxDepth:            5,
		MaxPagesPerDomain:   100,
		ContentMinLength:    200,
		ContentQualityScore: 0.6,
		EnableJSRendering:   true,
		RespectRobotsTxt:    true,
		UserAgentRotation: []string{
			"Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
			"Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36",
		},
		RequestDelay:   time.Second * 2,
		RandomDelay:    true,
		MaxConcurrency: 3,
		Timeout:        time.Second * 30,
	}
}

// CreateCustomRule 创建自定义爬取规则
func CreateCustomRule(urlPattern, contentSelector string, keywords []string) CrawlRule {
	return CrawlRule{
		URLPattern:      urlPattern,
		FollowLinks:     []string{"a[href]"},
		IgnoreLinks:     []string{"a[href*='download']", "a[href*='ad']"},
		ContentSelector: contentSelector,
		TitleSelector:   "h1, .title, .post-title",
		MetaSelectors: map[string]string{
			"author":       ".author, .by-author",
			"publish_time": ".date, .time, .publish-time",
			"tags":         ".tags a, .tag",
		},
		RequiredWords: keywords,
		ExcludedWords: []string{"广告", "推广", "营销", "advertisement"},
		MinWordCount:  150,
		MaxDepth:      3,
	}
}

// SmartCrawlStats 智能爬虫统计信息
type SmartCrawlStats struct {
	TotalPages     int            `json:"total_pages"`
	SuccessPages   int            `json:"success_pages"`
	FailedPages    int            `json:"failed_pages"`
	KnowledgeCount int            `json:"knowledge_count"`
	AverageQuality float64        `json:"average_quality"`
	DomainStats    map[string]int `json:"domain_stats"`
	DepthStats     map[int]int    `json:"depth_stats"`
	LanguageStats  map[string]int `json:"language_stats"`
	ProcessingTime time.Duration  `json:"processing_time"`
	StartTime      time.Time      `json:"start_time"`
	EndTime        time.Time      `json:"end_time"`
}

// GetCrawlStats 获取爬取统计信息
func (sc *SmartKnowledgeCrawler) GetCrawlStats() *SmartCrawlStats {
	sc.visitedMutex.RLock()
	sc.domainMutex.RLock()
	defer sc.visitedMutex.RUnlock()
	defer sc.domainMutex.RUnlock()

	stats := &SmartCrawlStats{
		TotalPages:    len(sc.visitedURLs),
		DomainStats:   make(map[string]int),
		DepthStats:    make(map[int]int),
		LanguageStats: make(map[string]int),
	}

	// 复制域名统计
	for domain, count := range sc.domainPageCount {
		stats.DomainStats[domain] = count
	}

	return stats
}
