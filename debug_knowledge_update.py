#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试知识更新问题
"""

import mysql.connector
import json

# 数据库配置 - 使用系统实际配置
DB_CONFIG = {
    'host': '127.0.0.1',
    'port': 33508,
    'user': 'root',
    'password': 'park%123456',  # 注意：这里需要URL解码
    'database': 'faqdb',
    'charset': 'utf8mb4'
}

# 备用配置 - 如果上面的不工作
DB_CONFIG_ALT = {
    'host': 'localhost',
    'port': 3306,
    'user': 'root',
    'password': 'root123',
    'database': 'faq_system',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    # 首先尝试系统配置
    try:
        print(f"🔗 尝试连接系统数据库: {DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}")
        return mysql.connector.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ 系统数据库连接失败: {e}")

        # 尝试备用配置
        try:
            print(f"🔗 尝试连接备用数据库: {DB_CONFIG_ALT['host']}:{DB_CONFIG_ALT['port']}/{DB_CONFIG_ALT['database']}")
            return mysql.connector.connect(**DB_CONFIG_ALT)
        except Exception as e2:
            print(f"❌ 备用数据库连接也失败: {e2}")
            return None

def check_knowledge_record(knowledge_id):
    """检查知识记录是否存在"""
    print(f"🔍 检查知识记录 ID={knowledge_id}")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 检查learned_knowledge表是否存在
        cursor.execute("""
            SELECT COUNT(*)
            FROM INFORMATION_SCHEMA.TABLES
            WHERE TABLE_SCHEMA = DATABASE()
            AND TABLE_NAME = 'learned_knowledge'
        """)
        table_exists = cursor.fetchone()[0] > 0
        print(f"📊 learned_knowledge表存在: {'是' if table_exists else '否'}")
        
        if not table_exists:
            print("❌ learned_knowledge表不存在，这是问题的根源！")
            return
        
        # 检查具体记录
        cursor.execute("SELECT * FROM learned_knowledge WHERE id = %s", (knowledge_id,))
        record = cursor.fetchone()
        
        if record:
            print(f"✅ 找到记录 ID={knowledge_id}")
            # 获取列名
            cursor.execute("DESCRIBE learned_knowledge")
            columns = [col[0] for col in cursor.fetchall()]
            
            print("📋 记录详情:")
            for i, col in enumerate(columns):
                value = record[i] if i < len(record) else None
                if isinstance(value, bytes):
                    try:
                        value = value.decode('utf-8')
                    except:
                        value = str(value)
                print(f"  {col}: {value}")
        else:
            print(f"❌ 未找到记录 ID={knowledge_id}")
            
            # 查看所有记录的ID
            cursor.execute("SELECT id, question FROM learned_knowledge LIMIT 10")
            all_records = cursor.fetchall()
            if all_records:
                print("📋 现有记录的ID:")
                for record in all_records:
                    print(f"  ID={record[0]}: {record[1][:50]}...")
            else:
                print("📋 learned_knowledge表为空")
        
    except Exception as e:
        print(f"❌ 查询失败: {e}")
    finally:
        conn.close()

def check_database_structure():
    """检查数据库结构"""
    print("\n🏗️ 检查数据库结构")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 检查当前数据库
        cursor.execute("SELECT DATABASE()")
        current_db = cursor.fetchone()[0]
        print(f"📊 当前数据库: {current_db}")
        
        # 检查所有表
        cursor.execute("SHOW TABLES")
        tables = cursor.fetchall()
        print(f"📊 数据库中的表 ({len(tables)} 个):")
        for table in tables:
            print(f"  - {table[0]}")
        
        # 如果learned_knowledge表存在，检查其结构
        if ('learned_knowledge',) in tables:
            cursor.execute("DESCRIBE learned_knowledge")
            columns = cursor.fetchall()
            print(f"\n📊 learned_knowledge表结构:")
            for col in columns:
                print(f"  {col[0]} {col[1]} {col[2]} {col[3]} {col[4]} {col[5]}")
            
            # 检查记录数量
            cursor.execute("SELECT COUNT(*) FROM learned_knowledge")
            count = cursor.fetchone()[0]
            print(f"📊 learned_knowledge表记录数: {count}")
        
    except Exception as e:
        print(f"❌ 检查数据库结构失败: {e}")
    finally:
        conn.close()

def test_update_query(knowledge_id):
    """测试更新查询"""
    print(f"\n🧪 测试更新查询 ID={knowledge_id}")
    
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 模拟更新查询
        test_query = """
            UPDATE learned_knowledge
            SET question = ?, answer = ?, category = ?, keywords = ?,
                confidence = ?, source = ?, learned_from = ?, context = ?, knowledge_topic = ?, metadata = ?
            WHERE id = ?
        """
        
        # 测试数据
        test_data = (
            "测试问题",
            "测试答案", 
            "test_category",
            '["test"]',
            0.8,
            "test_source",
            "test_user",
            "测试上下文",
            "测试知识点",
            '{}',
            knowledge_id
        )
        
        cursor.execute(test_query, test_data)
        rows_affected = cursor.rowcount
        print(f"📊 受影响的行数: {rows_affected}")
        
        if rows_affected == 0:
            print("❌ 没有行被更新，这解释了为什么会返回'知识记录不存在'")
        else:
            print("✅ 更新成功")
            # 回滚更改
            conn.rollback()
            print("🔄 已回滚测试更改")
        
    except Exception as e:
        print(f"❌ 测试更新查询失败: {e}")
    finally:
        conn.close()

def main():
    """主函数"""
    print("🔧 调试知识更新问题")
    print("=" * 50)
    
    # 检查数据库结构
    check_database_structure()
    
    # 检查特定记录
    knowledge_id = 5  # 您提到的ID
    check_knowledge_record(knowledge_id)
    
    # 测试更新查询
    test_update_query(knowledge_id)
    
    print("\n💡 可能的解决方案:")
    print("1. 如果learned_knowledge表不存在，需要创建该表")
    print("2. 如果记录不存在，检查前端传递的ID是否正确")
    print("3. 如果数据库连接有问题，检查数据库配置")
    print("4. 检查是否连接到了正确的数据库")

if __name__ == "__main__":
    main()
