package nlp

import (
	"log"
	"math"
	"regexp"
	"strings"
)

// ContextAnalyzer 上下文分析器
type ContextAnalyzer struct {
	domainKeywords map[string][]string
	topicPatterns  map[string][]*regexp.Regexp
	formalityWords map[string]float64
	urgencyWords   map[string]float64
}

// ContextResult 上下文分析结果
type ContextResult struct {
	Domain         string                 `json:"domain"`
	Topic          string                 `json:"topic"`
	Complexity     float64                `json:"complexity"`     // 0.0-1.0
	Formality      float64                `json:"formality"`      // 0.0-1.0
	Urgency        float64                `json:"urgency"`        // 0.0-1.0
	ContextClues   []string               `json:"context_clues"`
	Dependencies   []string               `json:"dependencies"`
	Metadata       map[string]interface{} `json:"metadata"`
}

// NewContextAnalyzer 创建上下文分析器
func NewContextAnalyzer() *ContextAnalyzer {
	log.Printf("🔍 初始化上下文分析器...")
	
	ca := &ContextAnalyzer{
		domainKeywords: make(map[string][]string),
		topicPatterns:  make(map[string][]*regexp.Regexp),
		formalityWords: make(map[string]float64),
		urgencyWords:   make(map[string]float64),
	}
	
	ca.initializeDomainKeywords()
	ca.initializeTopicPatterns()
	ca.initializeFormalityWords()
	ca.initializeUrgencyWords()
	
	log.Printf("✅ 上下文分析器初始化完成")
	return ca
}

// initializeDomainKeywords 初始化领域关键词
func (ca *ContextAnalyzer) initializeDomainKeywords() {
	ca.domainKeywords = map[string][]string{
		"technology": {
			"编程", "代码", "算法", "数据库", "服务器", "API", "接口", "框架",
			"开发", "软件", "硬件", "网络", "安全", "云计算", "人工智能", "机器学习",
			"programming", "code", "algorithm", "database", "server", "api", "framework",
			"development", "software", "hardware", "network", "security", "cloud", "ai", "ml",
		},
		"business": {
			"商业", "业务", "市场", "销售", "营销", "客户", "产品", "服务",
			"管理", "运营", "财务", "投资", "利润", "成本", "预算", "战略",
			"business", "market", "sales", "marketing", "customer", "product", "service",
			"management", "operation", "finance", "investment", "profit", "cost", "budget",
		},
		"education": {
			"教育", "学习", "教学", "课程", "学生", "老师", "教授", "学校",
			"大学", "知识", "技能", "培训", "考试", "学位", "研究", "论文",
			"education", "learning", "teaching", "course", "student", "teacher", "professor",
			"school", "university", "knowledge", "skill", "training", "exam", "degree",
		},
		"health": {
			"健康", "医疗", "医生", "病人", "疾病", "治疗", "药物", "医院",
			"诊断", "症状", "预防", "康复", "营养", "运动", "心理", "身体",
			"health", "medical", "doctor", "patient", "disease", "treatment", "medicine",
			"hospital", "diagnosis", "symptom", "prevention", "recovery", "nutrition",
		},
		"finance": {
			"金融", "银行", "投资", "股票", "基金", "保险", "贷款", "信贷",
			"理财", "财务", "会计", "税务", "资产", "负债", "收入", "支出",
			"finance", "bank", "investment", "stock", "fund", "insurance", "loan", "credit",
			"accounting", "tax", "asset", "liability", "income", "expense",
		},
		"entertainment": {
			"娱乐", "电影", "音乐", "游戏", "体育", "旅游", "美食", "时尚",
			"艺术", "文化", "小说", "电视", "明星", "演员", "歌手", "导演",
			"entertainment", "movie", "music", "game", "sport", "travel", "food", "fashion",
			"art", "culture", "novel", "tv", "star", "actor", "singer", "director",
		},
		"science": {
			"科学", "研究", "实验", "理论", "假设", "数据", "分析", "结果",
			"物理", "化学", "生物", "数学", "统计", "工程", "技术", "创新",
			"science", "research", "experiment", "theory", "hypothesis", "data", "analysis",
			"physics", "chemistry", "biology", "mathematics", "statistics", "engineering",
		},
	}
}

// initializeTopicPatterns 初始化话题模式
func (ca *ContextAnalyzer) initializeTopicPatterns() {
	patterns := map[string][]string{
		"problem_solving": {
			`(如何解决|怎么解决|解决方案|问题.*解决)`,
			`(出现.*问题|遇到.*问题|问题.*处理)`,
			`(故障.*排除|错误.*修复|异常.*处理)`,
		},
		"how_to": {
			`^(如何|怎么|怎样)`,
			`(怎么做|如何做|怎样做)`,
			`(步骤|方法|流程|过程)`,
		},
		"comparison": {
			`(比较|对比|区别|差异)`,
			`(哪个好|选择.*还是|.*vs.*)`,
			`(优缺点|利弊|好坏)`,
		},
		"explanation": {
			`(是什么|什么是|解释|说明)`,
			`(原理|机制|工作方式)`,
			`(为什么|原因|因为)`,
		},
		"recommendation": {
			`(推荐|建议|意见)`,
			`(应该.*用|最好.*用|推荐.*用)`,
			`(哪个.*好|什么.*好)`,
		},
		"troubleshooting": {
			`(不工作|无法|不能|失败)`,
			`(错误|异常|故障|问题)`,
			`(修复|解决|处理|排除)`,
		},
	}
	
	for topic, patternStrs := range patterns {
		for _, patternStr := range patternStrs {
			if regex, err := regexp.Compile(patternStr); err == nil {
				ca.topicPatterns[topic] = append(ca.topicPatterns[topic], regex)
			}
		}
	}
}

// initializeFormalityWords 初始化正式程度词汇
func (ca *ContextAnalyzer) initializeFormalityWords() {
	ca.formalityWords = map[string]float64{
		// 正式词汇 (高分)
		"请":     0.8, "您":     0.9, "敬请":   1.0, "恳请":   0.9, "烦请":   0.8,
		"贵":     0.8, "尊敬":   0.9, "诚挚":   0.9, "谨":     0.8, "恭":     0.9,
		"致敬":   0.9, "感谢":   0.7, "谢谢":   0.6, "多谢":   0.7, "劳烦":   0.8,
		"please": 0.7, "kindly": 0.8, "respectfully": 0.9, "sincerely": 0.8,
		
		// 非正式词汇 (低分)
		"嗨":     0.2, "哈喽":   0.1, "咋":     0.2, "啥":     0.2, "咋样":   0.2,
		"搞":     0.3, "弄":     0.3, "整":     0.3, "玩":     0.2, "折腾":   0.3,
		"牛":     0.2, "屌":     0.1, "酷":     0.2, "赞":     0.3, "棒":     0.3,
		"hi":     0.2, "hey":    0.1, "yo":     0.1, "sup":    0.1, "cool":   0.2,
	}
}

// initializeUrgencyWords 初始化紧急程度词汇
func (ca *ContextAnalyzer) initializeUrgencyWords() {
	ca.urgencyWords = map[string]float64{
		// 高紧急度
		"紧急":   1.0, "急":     0.9, "立即":   0.9, "马上":   0.8, "赶紧":   0.8,
		"快":     0.7, "尽快":   0.8, "火急":   1.0, "十万火急": 1.0, "刻不容缓": 1.0,
		"urgent": 1.0, "asap":   0.9, "immediately": 0.9, "quickly": 0.7, "fast": 0.6,
		
		// 中等紧急度
		"需要":   0.5, "希望":   0.4, "想要":   0.4, "期望":   0.4, "盼望":   0.5,
		"soon":   0.6, "need":   0.5, "want":   0.4, "hope":   0.4, "expect": 0.4,
		
		// 低紧急度
		"有空":   0.2, "方便":   0.2, "闲时":   0.1, "随时":   0.3, "不急":   0.1,
		"whenever": 0.2, "convenient": 0.2, "leisure": 0.1, "anytime": 0.3,
	}
}

// AnalyzeContext 分析上下文
func (ca *ContextAnalyzer) AnalyzeContext(text string, entities []Entity, intent IntentResult) ContextResult {
	if strings.TrimSpace(text) == "" {
		return ContextResult{
			Domain:       "unknown",
			Topic:        "unknown",
			Complexity:   0.0,
			Formality:    0.5,
			Urgency:      0.0,
			ContextClues: []string{},
			Dependencies: []string{},
			Metadata:     make(map[string]interface{}),
		}
	}
	
	textLower := strings.ToLower(text)
	
	result := ContextResult{
		ContextClues: []string{},
		Dependencies: []string{},
		Metadata:     make(map[string]interface{}),
	}
	
	// 1. 领域识别
	result.Domain = ca.identifyDomain(textLower)
	
	// 2. 话题识别
	result.Topic = ca.identifyTopic(textLower)
	
	// 3. 复杂度分析
	result.Complexity = ca.calculateComplexity(text, entities)
	
	// 4. 正式程度分析
	result.Formality = ca.calculateFormality(textLower)
	
	// 5. 紧急程度分析
	result.Urgency = ca.calculateUrgency(textLower)
	
	// 6. 提取上下文线索
	result.ContextClues = ca.extractContextClues(text, entities)
	
	// 7. 识别依赖关系
	result.Dependencies = ca.identifyDependencies(text, entities)
	
	// 8. 添加元数据
	result.Metadata = map[string]interface{}{
		"text_length":    len(text),
		"word_count":     len(strings.Fields(text)),
		"entity_count":   len(entities),
		"intent_type":    intent.Intent,
		"intent_confidence": intent.Confidence,
	}
	
	return result
}

// identifyDomain 识别领域
func (ca *ContextAnalyzer) identifyDomain(text string) string {
	domainScores := make(map[string]float64)
	
	for domain, keywords := range ca.domainKeywords {
		score := 0.0
		for _, keyword := range keywords {
			if strings.Contains(text, strings.ToLower(keyword)) {
				score += 1.0
			}
		}
		if score > 0 {
			domainScores[domain] = score / float64(len(keywords))
		}
	}
	
	// 找到最高分的领域
	bestDomain := "general"
	bestScore := 0.0
	
	for domain, score := range domainScores {
		if score > bestScore {
			bestDomain = domain
			bestScore = score
		}
	}
	
	// 如果得分太低，返回通用领域
	if bestScore < 0.1 {
		return "general"
	}
	
	return bestDomain
}

// identifyTopic 识别话题
func (ca *ContextAnalyzer) identifyTopic(text string) string {
	topicScores := make(map[string]int)
	
	for topic, patterns := range ca.topicPatterns {
		for _, pattern := range patterns {
			if pattern.MatchString(text) {
				topicScores[topic]++
			}
		}
	}
	
	// 找到最高分的话题
	bestTopic := "general"
	bestScore := 0
	
	for topic, score := range topicScores {
		if score > bestScore {
			bestTopic = topic
			bestScore = score
		}
	}
	
	return bestTopic
}

// calculateComplexity 计算复杂度
func (ca *ContextAnalyzer) calculateComplexity(text string, entities []Entity) float64 {
	complexity := 0.0
	
	// 基于文本长度
	textLength := float64(len(text))
	lengthScore := math.Min(textLength/500.0, 1.0) * 0.3
	complexity += lengthScore
	
	// 基于词汇数量
	wordCount := float64(len(strings.Fields(text)))
	wordScore := math.Min(wordCount/100.0, 1.0) * 0.2
	complexity += wordScore
	
	// 基于实体数量
	entityScore := math.Min(float64(len(entities))/10.0, 1.0) * 0.2
	complexity += entityScore
	
	// 基于句子结构
	sentenceCount := float64(strings.Count(text, ".") + strings.Count(text, "。") + 
		strings.Count(text, "?") + strings.Count(text, "？") + 
		strings.Count(text, "!") + strings.Count(text, "！"))
	if sentenceCount > 0 {
		avgWordsPerSentence := wordCount / sentenceCount
		structureScore := math.Min(avgWordsPerSentence/20.0, 1.0) * 0.2
		complexity += structureScore
	}
	
	// 基于技术术语
	techTerms := []string{"api", "json", "http", "sql", "algorithm", "database", "server"}
	techCount := 0
	textLower := strings.ToLower(text)
	for _, term := range techTerms {
		if strings.Contains(textLower, term) {
			techCount++
		}
	}
	techScore := math.Min(float64(techCount)/5.0, 1.0) * 0.1
	complexity += techScore
	
	return math.Min(complexity, 1.0)
}

// calculateFormality 计算正式程度
func (ca *ContextAnalyzer) calculateFormality(text string) float64 {
	totalScore := 0.0
	wordCount := 0
	
	for word, score := range ca.formalityWords {
		if strings.Contains(text, word) {
			totalScore += score
			wordCount++
		}
	}
	
	if wordCount == 0 {
		// 默认中等正式程度
		return 0.5
	}
	
	avgScore := totalScore / float64(wordCount)
	return math.Max(0.0, math.Min(1.0, avgScore))
}

// calculateUrgency 计算紧急程度
func (ca *ContextAnalyzer) calculateUrgency(text string) float64 {
	maxScore := 0.0
	
	for word, score := range ca.urgencyWords {
		if strings.Contains(text, word) {
			if score > maxScore {
				maxScore = score
			}
		}
	}
	
	// 检查标点符号
	if strings.Contains(text, "!") || strings.Contains(text, "！") {
		maxScore += 0.2
	}
	
	// 检查重复字符（表示强调）
	if strings.Contains(text, "!!") || strings.Contains(text, "？？") {
		maxScore += 0.3
	}
	
	return math.Min(maxScore, 1.0)
}

// extractContextClues 提取上下文线索
func (ca *ContextAnalyzer) extractContextClues(text string, entities []Entity) []string {
	var clues []string
	
	// 从实体中提取线索
	for _, entity := range entities {
		if entity.Type == "PERSON" || entity.Type == "ORGANIZATION" || entity.Type == "LOCATION" {
			clues = append(clues, entity.Text)
		}
	}
	
	// 提取时间线索
	timePatterns := []string{
		`\d{4}年`, `今天`, `明天`, `昨天`, `上周`, `下周`, `本月`, `下月`,
		`最近`, `刚才`, `稍后`, `以后`, `之前`, `现在`, `当前`,
	}
	
	for _, pattern := range timePatterns {
		if regex, err := regexp.Compile(pattern); err == nil {
			matches := regex.FindAllString(text, -1)
			clues = append(clues, matches...)
		}
	}
	
	// 去重
	seen := make(map[string]bool)
	var uniqueClues []string
	for _, clue := range clues {
		if !seen[clue] && len(clue) > 1 {
			seen[clue] = true
			uniqueClues = append(uniqueClues, clue)
		}
	}
	
	return uniqueClues
}

// identifyDependencies 识别依赖关系
func (ca *ContextAnalyzer) identifyDependencies(text string, entities []Entity) []string {
	var dependencies []string
	
	// 识别技术依赖
	techDeps := []string{
		"需要.*安装", "依赖.*版本", "要求.*环境", "基于.*框架",
		"使用.*库", "调用.*API", "连接.*数据库", "部署.*服务器",
	}
	
	for _, pattern := range techDeps {
		if regex, err := regexp.Compile(pattern); err == nil {
			if regex.MatchString(text) {
				dependencies = append(dependencies, regex.FindString(text))
			}
		}
	}
	
	// 识别逻辑依赖
	logicDeps := []string{
		"首先", "然后", "接下来", "最后", "之前", "之后",
		"前提", "条件", "要求", "基础", "准备",
	}
	
	textLower := strings.ToLower(text)
	for _, dep := range logicDeps {
		if strings.Contains(textLower, dep) {
			dependencies = append(dependencies, dep)
		}
	}
	
	return dependencies
}
