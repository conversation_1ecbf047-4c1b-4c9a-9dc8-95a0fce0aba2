package humanize

import "time"

// ConversationalPattern 对话模式
type ConversationalPattern struct {
	Pattern     string   `json:"pattern"`     // 匹配模式
	Category    string   `json:"category"`    // 分类
	Intent      string   `json:"intent"`      // 意图
	Responses   []string `json:"responses"`   // 可能的回应
	Confidence  float64  `json:"confidence"`  // 置信度
	Keywords    []string `json:"keywords"`    // 关键词
	Emotion     string   `json:"emotion"`     // 情感色彩
}

// HumanizedResponse 人性化回应
type HumanizedResponse struct {
	OriginalQuery    string                 `json:"original_query"`    // 原始查询
	DetectedPattern  string                 `json:"detected_pattern"`  // 检测到的模式
	Intent           string                 `json:"intent"`            // 意图
	Emotion          string                 `json:"emotion"`           // 情感
	Response         string                 `json:"response"`          // 回应内容
	ResponseType     string                 `json:"response_type"`     // 回应类型
	Confidence       float64                `json:"confidence"`        // 置信度
	ShouldContinue   bool                   `json:"should_continue"`   // 是否继续处理
	Metadata         map[string]interface{} `json:"metadata"`          // 元数据
	ProcessedAt      time.Time              `json:"processed_at"`      // 处理时间
}

// EmotionalContext 情感上下文
type EmotionalContext struct {
	UserEmotion     string    `json:"user_emotion"`     // 用户情感
	BotPersonality  string    `json:"bot_personality"`  // 机器人性格
	ConversationTone string   `json:"conversation_tone"` // 对话语调
	LastInteraction time.Time `json:"last_interaction"` // 最后交互时间
	InteractionCount int      `json:"interaction_count"` // 交互次数
}

// ResponseStyle 回应风格
type ResponseStyle struct {
	Formal      bool    `json:"formal"`       // 正式
	Casual      bool    `json:"casual"`       // 随意
	Friendly    bool    `json:"friendly"`     // 友好
	Professional bool   `json:"professional"` // 专业
	Humorous    bool    `json:"humorous"`     // 幽默
	Empathetic  bool    `json:"empathetic"`   // 共情
	Confidence  float64 `json:"confidence"`   // 置信度
}

// ConversationalConfig 对话配置
type ConversationalConfig struct {
	EnableHumanization   bool          `json:"enable_humanization"`   // 启用人性化
	DefaultPersonality   string        `json:"default_personality"`   // 默认性格
	ResponseStyle        ResponseStyle `json:"response_style"`        // 回应风格
	MaxResponseLength    int           `json:"max_response_length"`   // 最大回应长度
	EnableEmotionDetection bool        `json:"enable_emotion_detection"` // 启用情感检测
	EnableContextMemory  bool          `json:"enable_context_memory"`  // 启用上下文记忆
}

// DefaultConversationalConfig 默认对话配置
func DefaultConversationalConfig() *ConversationalConfig {
	return &ConversationalConfig{
		EnableHumanization: true,
		DefaultPersonality: "friendly_assistant",
		ResponseStyle: ResponseStyle{
			Formal:      false,
			Casual:      true,
			Friendly:    true,
			Professional: true,
			Humorous:    false,
			Empathetic:  true,
			Confidence:  0.8,
		},
		MaxResponseLength:    200,
		EnableEmotionDetection: true,
		EnableContextMemory:  true,
	}
}
