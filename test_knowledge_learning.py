#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试FAQ系统的知识学习和持久化能力
"""

import requests
import json
import time
import mysql.connector
from datetime import datetime

# 服务器配置
SERVER_URL = "http://localhost:8082"
ASK_ENDPOINT = f"{SERVER_URL}/ask"
FEEDBACK_ENDPOINT = f"{SERVER_URL}/feedback"

# 数据库配置
DB_CONFIG = {
    'host': 'localhost',
    'user': 'root',
    'password': 'root123',
    'database': 'faq_system',
    'charset': 'utf8mb4'
}

def get_db_connection():
    """获取数据库连接"""
    try:
        return mysql.connector.connect(**DB_CONFIG)
    except Exception as e:
        print(f"❌ 数据库连接失败: {e}")
        return None

def check_learned_knowledge():
    """检查learned_knowledge表中的数据"""
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 查询最近学习的知识
        query = """
        SELECT id, question, answer, source, confidence, category, 
               learned_from, status, created_at
        FROM learned_knowledge 
        ORDER BY created_at DESC 
        LIMIT 10
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"\n📚 learned_knowledge表中的最新数据 ({len(results)} 条):")
        print("-" * 80)
        
        for row in results:
            print(f"ID: {row[0]}")
            print(f"问题: {row[1]}")
            print(f"答案: {row[2][:100]}..." if len(row[2]) > 100 else f"答案: {row[2]}")
            print(f"来源: {row[3]} | 置信度: {row[4]} | 分类: {row[5]}")
            print(f"学习来源: {row[6]} | 状态: {row[7]} | 创建时间: {row[8]}")
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ 查询learned_knowledge失败: {e}")
    finally:
        conn.close()

def check_knowledge_vectors():
    """检查knowledge_vectors表中的数据"""
    conn = get_db_connection()
    if not conn:
        return
    
    try:
        cursor = conn.cursor()
        
        # 查询向量数据
        query = """
        SELECT kv.knowledge_id, lk.question, 
               LENGTH(kv.vector_data) as vector_size,
               kv.created_at
        FROM knowledge_vectors kv
        JOIN learned_knowledge lk ON kv.knowledge_id = lk.id
        ORDER BY kv.created_at DESC 
        LIMIT 10
        """
        
        cursor.execute(query)
        results = cursor.fetchall()
        
        print(f"\n🔢 knowledge_vectors表中的最新数据 ({len(results)} 条):")
        print("-" * 80)
        
        for row in results:
            print(f"知识ID: {row[0]}")
            print(f"问题: {row[1][:60]}..." if len(row[1]) > 60 else f"问题: {row[1]}")
            print(f"向量大小: {row[2]} 字节 | 创建时间: {row[3]}")
            print("-" * 40)
            
    except Exception as e:
        print(f"❌ 查询knowledge_vectors失败: {e}")
    finally:
        conn.close()

def simulate_user_interaction(question, expected_answer=None):
    """模拟用户交互"""
    print(f"\n🔍 测试问题: {question}")
    print("-" * 50)
    
    try:
        # 1. 发送问题
        response = requests.post(ASK_ENDPOINT, 
                               json={"question": question},
                               headers={"Content-Type": "application/json"},
                               timeout=10)
        
        if response.status_code == 200:
            data = response.json()
            answer = data.get("answer", "")
            intent = data.get("intent", "")
            confidence = data.get("confidence", 0)
            
            print(f"✅ 系统回答: {answer[:200]}..." if len(answer) > 200 else f"✅ 系统回答: {answer}")
            print(f"📊 意图: {intent} (置信度: {confidence:.2f})")
            
            # 2. 如果有期望答案，提供用户纠错反馈
            if expected_answer and expected_answer.lower() not in answer.lower():
                print(f"🔄 提供纠错反馈: {expected_answer}")
                
                feedback_data = {
                    "question": question,
                    "response": answer,
                    "feedback_type": "correction",
                    "feedback_content": expected_answer,
                    "rating": 2
                }
                
                feedback_response = requests.post(FEEDBACK_ENDPOINT,
                                                json=feedback_data,
                                                headers={"Content-Type": "application/json"},
                                                timeout=5)
                
                if feedback_response.status_code == 200:
                    print("✅ 纠错反馈已提交")
                else:
                    print(f"⚠️ 纠错反馈提交失败: {feedback_response.status_code}")
            
            # 3. 提供正面反馈
            elif confidence > 0.7:
                print("👍 提供正面反馈")
                
                feedback_data = {
                    "question": question,
                    "response": answer,
                    "feedback_type": "positive",
                    "rating": 5
                }
                
                feedback_response = requests.post(FEEDBACK_ENDPOINT,
                                                json=feedback_data,
                                                headers={"Content-Type": "application/json"},
                                                timeout=5)
                
                if feedback_response.status_code == 200:
                    print("✅ 正面反馈已提交")
            
            return True
        else:
            print(f"❌ 请求失败: {response.status_code}")
            return False
            
    except Exception as e:
        print(f"❌ 请求异常: {e}")
        return False

def test_knowledge_learning():
    """测试知识学习功能"""
    print("🧠 开始测试知识学习和持久化功能")
    print("=" * 60)
    
    # 测试用例 - 包含问题和期望的正确答案
    test_cases = [
        {
            "question": "什么是RESTful API？",
            "expected_answer": "RESTful API是一种基于REST架构风格的Web API设计方法，使用HTTP方法（GET、POST、PUT、DELETE）来操作资源，具有无状态、统一接口、可缓存等特点。"
        },
        {
            "question": "如何优化MySQL查询性能？",
            "expected_answer": "优化MySQL查询性能的方法包括：1)创建合适的索引 2)优化查询语句 3)避免SELECT * 4)使用LIMIT限制结果集 5)优化表结构 6)使用查询缓存等。"
        },
        {
            "question": "Python中的装饰器是什么？",
            "expected_answer": "Python装饰器是一种设计模式，允许在不修改原函数代码的情况下，为函数添加额外的功能。装饰器本质上是一个接受函数作为参数并返回新函数的高阶函数。"
        },
        {
            "question": "什么是Docker容器？",
            "expected_answer": "Docker容器是一种轻量级的虚拟化技术，它将应用程序及其依赖项打包到一个可移植的容器中，确保应用在不同环境中的一致性运行。"
        },
        {
            "question": "如何实现负载均衡？",
            "expected_answer": "负载均衡可以通过多种方式实现：1)硬件负载均衡器 2)软件负载均衡器如Nginx、HAProxy 3)云服务负载均衡 4)DNS负载均衡等，主要算法包括轮询、加权轮询、最少连接等。"
        }
    ]
    
    # 执行测试
    success_count = 0
    for i, test_case in enumerate(test_cases, 1):
        print(f"\n📝 测试用例 {i}/{len(test_cases)}")
        if simulate_user_interaction(test_case["question"], test_case["expected_answer"]):
            success_count += 1
        time.sleep(2)  # 避免请求过快
    
    print(f"\n📊 交互测试完成: {success_count}/{len(test_cases)} 成功")
    
    # 等待系统处理学习
    print("\n⏳ 等待系统处理学习数据...")
    time.sleep(5)
    
    # 检查数据库中的学习结果
    print("\n🔍 检查学习结果...")
    check_learned_knowledge()
    check_knowledge_vectors()
    
    # 测试学习效果
    print("\n🎯 测试学习效果...")
    test_learned_knowledge()

def test_learned_knowledge():
    """测试学习到的知识是否能被正确使用"""
    print("\n🧪 测试学习到的知识...")
    
    # 使用稍微不同的问法测试学习效果
    test_questions = [
        "RESTful API的特点是什么？",
        "怎样提高MySQL性能？",
        "Python装饰器的作用？",
        "Docker的优势是什么？",
        "负载均衡有哪些方法？"
    ]
    
    for question in test_questions:
        print(f"\n🔍 测试学习效果: {question}")
        simulate_user_interaction(question)
        time.sleep(1)

def main():
    """主测试函数"""
    print("🚀 FAQ系统知识学习和持久化测试")
    print("=" * 60)
    print(f"⏰ 测试开始时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # 检查初始状态
    print("\n📋 检查初始数据状态...")
    check_learned_knowledge()
    check_knowledge_vectors()
    
    # 执行知识学习测试
    test_knowledge_learning()
    
    print(f"\n⏰ 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎉 知识学习和持久化测试完成！")

if __name__ == "__main__":
    main()
