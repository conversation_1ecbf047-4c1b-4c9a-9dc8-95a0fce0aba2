package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	db, err := sql.Open("mysql", "root:123456@tcp(localhost:3306)/faq_system?charset=utf8mb4&parseTime=True&loc=Local")
	if err != nil {
		log.Fatal("数据库连接失败:", err)
	}
	defer db.Close()

	// 首先检查数据库中的所有知识点标志
	checkAllKnowledgeTopics(db)

	// 然后测试查询相关知识
	testGetRelatedKnowledge(db, "阿萨技术", 123)
}

func checkAllKnowledgeTopics(db *sql.DB) {
	fmt.Printf("📊 检查数据库中的所有知识点标志:\n")

	query := `
		SELECT id, question, knowledge_topic, status
		FROM learned_knowledge
		WHERE knowledge_topic IS NOT NULL AND knowledge_topic != ''
		ORDER BY knowledge_topic, id
	`

	rows, err := db.Query(query)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	count := 0
	currentTopic := ""
	for rows.Next() {
		var id int
		var question, knowledgeTopic, status string

		err := rows.Scan(&id, &question, &knowledgeTopic, &status)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}

		if knowledgeTopic != currentTopic {
			if currentTopic != "" {
				fmt.Printf("\n")
			}
			fmt.Printf("🏷️ 知识点: %s\n", knowledgeTopic)
			currentTopic = knowledgeTopic
		}

		fmt.Printf("   ID:%d - %s (状态:%s)\n", id, question, status)
		count++
	}

	fmt.Printf("\n📊 总共找到 %d 个有知识点标志的知识\n\n", count)
}

func testGetRelatedKnowledge(db *sql.DB, knowledgeTopic string, excludeID int) {
	fmt.Printf("🔍 测试查询相关知识: 知识点='%s', 排除ID=%d\n", knowledgeTopic, excludeID)

	query := `
		SELECT id, question, answer, source, confidence, category, keywords,
		       context, learned_from, status, knowledge_topic, created_at
		FROM learned_knowledge
		WHERE knowledge_topic = ? AND id != ? AND status = 'approved'
		ORDER BY confidence DESC, created_at DESC
		LIMIT 3
	`

	rows, err := db.Query(query, knowledgeTopic, excludeID)
	if err != nil {
		fmt.Printf("❌ 查询失败: %v\n", err)
		return
	}
	defer rows.Close()

	count := 0
	for rows.Next() {
		var id int
		var question, answer, source, category, keywords, context, learnedFrom, status, knowledgeTopicResult string
		var confidence float64
		var createdAt string

		err := rows.Scan(&id, &question, &answer, &source, &confidence, &category, &keywords,
			&context, &learnedFrom, &status, &knowledgeTopicResult, &createdAt)
		if err != nil {
			fmt.Printf("❌ 扫描行失败: %v\n", err)
			continue
		}

		count++
		fmt.Printf("✅ 找到相关知识 %d:\n", count)
		fmt.Printf("   ID: %d\n", id)
		fmt.Printf("   问题: %s\n", question)
		fmt.Printf("   答案: %s\n", answer)
		fmt.Printf("   知识点: %s\n", knowledgeTopicResult)
		fmt.Printf("   置信度: %.2f\n", confidence)
		fmt.Printf("   状态: %s\n", status)
		fmt.Printf("\n")
	}

	if count == 0 {
		fmt.Printf("❌ 没有找到相关知识\n")
	} else {
		fmt.Printf("📊 总共找到 %d 个相关知识\n", count)
	}

	// 测试推荐格式化
	if count > 0 {
		fmt.Printf("\n🔗 推荐格式化测试:\n")
		fmt.Printf("🔗 **您可能还想了解 (%s)：**\n", knowledgeTopic)

		// 重新查询来格式化
		rows2, err := db.Query(query, knowledgeTopic, excludeID)
		if err == nil {
			defer rows2.Close()
			for rows2.Next() {
				var id int
				var question, answer, source, category, keywords, context, learnedFrom, status, knowledgeTopicResult string
				var confidence float64
				var createdAt string

				err := rows2.Scan(&id, &question, &answer, &source, &confidence, &category, &keywords,
					&context, &learnedFrom, &status, &knowledgeTopicResult, &createdAt)
				if err == nil {
					fmt.Printf("• %s\n", question)
				}
			}
		}
	}
}
