package main

import (
	"database/sql"
	"fmt"
	"log"

	_ "github.com/go-sql-driver/mysql"
)

func main() {
	// 连接数据库
	dsn := "root:123456@tcp(localhost:3306)/faq_system?charset=utf8mb4&parseTime=True&loc=Local"
	db, err := sql.Open("mysql", dsn)
	if err != nil {
		log.Fatal("连接数据库失败:", err)
	}
	defer db.Close()

	// 测试连接
	if err := db.Ping(); err != nil {
		log.Fatal("数据库连接测试失败:", err)
	}

	fmt.Println("✅ 数据库连接成功")

	// 执行SQL更新
	sqlStatements := []string{
		// 添加知识点标志字段
		`ALTER TABLE learned_knowledge 
		 ADD COLUMN knowledge_topic VARCHAR(100) DEFAULT NULL COMMENT '知识点标志，用于关联相关问题'`,

		// 添加索引
		`CREATE INDEX idx_knowledge_topic ON learned_knowledge(knowledge_topic)`,

		// 创建知识点管理表
		`CREATE TABLE IF NOT EXISTS knowledge_topics (
			id INT AUTO_INCREMENT PRIMARY KEY,
			topic_name VARCHAR(100) NOT NULL UNIQUE COMMENT '知识点名称',
			topic_description TEXT COMMENT '知识点描述',
			category VARCHAR(50) COMMENT '知识点分类',
			created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
			updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
			INDEX idx_topic_name (topic_name),
			INDEX idx_category (category)
		) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='知识点管理表'`,

		// 插入示例知识点
		`INSERT IGNORE INTO knowledge_topics (topic_name, topic_description, category) VALUES
		('JavaScript基础', 'JavaScript编程语言的基础概念和语法', 'technology'),
		('React框架', 'React前端框架相关知识', 'technology'),
		('Vue.js框架', 'Vue.js前端框架相关知识', 'technology'),
		('Node.js', 'Node.js服务端JavaScript运行环境', 'technology'),
		('数据库设计', '数据库设计原理和最佳实践', 'technology'),
		('机器学习', '机器学习算法和应用', 'science'),
		('深度学习', '深度学习和神经网络', 'science')`,

		// 更新现有知识的知识点标志
		`UPDATE learned_knowledge 
		 SET knowledge_topic = 'React框架' 
		 WHERE (question LIKE '%React%' OR answer LIKE '%React%') AND knowledge_topic IS NULL`,

		`UPDATE learned_knowledge 
		 SET knowledge_topic = 'Vue.js框架' 
		 WHERE (question LIKE '%Vue%' OR answer LIKE '%Vue%') AND knowledge_topic IS NULL`,

		`UPDATE learned_knowledge 
		 SET knowledge_topic = 'Node.js' 
		 WHERE (question LIKE '%Node%' OR answer LIKE '%Node%') AND knowledge_topic IS NULL`,

		`UPDATE learned_knowledge 
		 SET knowledge_topic = 'JavaScript基础' 
		 WHERE (question LIKE '%JavaScript%' OR answer LIKE '%JavaScript%') AND knowledge_topic IS NULL`,
	}

	// 执行每个SQL语句
	for i, sqlStmt := range sqlStatements {
		fmt.Printf("执行SQL %d...\n", i+1)
		
		// 对于ALTER TABLE语句，可能会因为字段已存在而失败，这是正常的
		_, err := db.Exec(sqlStmt)
		if err != nil {
			if i == 0 && (err.Error() == "Error 1060: Duplicate column name 'knowledge_topic'" || 
						  err.Error() == "Error 1061: Duplicate key name 'idx_knowledge_topic'") {
				fmt.Printf("⚠️  字段或索引已存在，跳过: %v\n", err)
				continue
			}
			fmt.Printf("❌ SQL执行失败: %v\n", err)
			fmt.Printf("SQL: %s\n", sqlStmt)
		} else {
			fmt.Printf("✅ SQL执行成功\n")
		}
	}

	// 检查更新结果
	var count int
	err = db.QueryRow("SELECT COUNT(*) FROM learned_knowledge WHERE knowledge_topic IS NOT NULL").Scan(&count)
	if err != nil {
		log.Printf("查询统计失败: %v", err)
	} else {
		fmt.Printf("\n📊 统计结果: %d 条知识已设置知识点标志\n", count)
	}

	// 显示知识点列表
	rows, err := db.Query("SELECT topic_name, topic_description FROM knowledge_topics ORDER BY topic_name")
	if err != nil {
		log.Printf("查询知识点失败: %v", err)
	} else {
		defer rows.Close()
		fmt.Println("\n📚 知识点列表:")
		for rows.Next() {
			var name, desc string
			if err := rows.Scan(&name, &desc); err == nil {
				fmt.Printf("  - %s: %s\n", name, desc)
			}
		}
	}

	fmt.Println("\n🎉 数据库更新完成！")
}
