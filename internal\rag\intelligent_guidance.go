package rag

import (
	"faq-system/internal/logger"
	"faq-system/internal/nlp"
	"fmt"
	"strings"
)

// IntelligentGuidanceSystem 智能提示和引导系统
type IntelligentGuidanceSystem struct {
	nlpProcessor *nlp.IntegratedProcessor

	// 引导模板
	guidanceTemplates map[string][]string

	// 技术领域提示
	techDomainPrompts map[string][]string

	// 常见问题模式
	commonPatterns []GuidancePattern
}

// GuidancePattern 引导模式
type GuidancePattern struct {
	Pattern     string   `json:"pattern"`     // 模式描述
	Keywords    []string `json:"keywords"`    // 关键词
	Suggestions []string `json:"suggestions"` // 建议
	Examples    []string `json:"examples"`    // 示例
}

// GuidanceResult 引导结果
type GuidanceResult struct {
	Success         bool                   `json:"success"`           // 是否成功生成引导
	GuidanceType    string                 `json:"guidance_type"`     // 引导类型
	Message         string                 `json:"message"`           // 引导消息
	Suggestions     []string               `json:"suggestions"`       // 具体建议
	Examples        []string               `json:"examples"`          // 示例
	FollowUpPrompts []string               `json:"follow_up_prompts"` // 后续提示
	Confidence      float64                `json:"confidence"`        // 引导置信度
	Metadata        map[string]interface{} `json:"metadata"`          // 元数据
}

// NewIntelligentGuidanceSystem 创建智能引导系统
func NewIntelligentGuidanceSystem(nlpProcessor *nlp.IntegratedProcessor) *IntelligentGuidanceSystem {
	system := &IntelligentGuidanceSystem{
		nlpProcessor: nlpProcessor,
		guidanceTemplates: map[string][]string{
			"incomplete_input": {
				"您的输入「%s」似乎不够完整。为了更好地帮助您，请提供更多信息：",
				"我注意到您输入了「%s」，这可能是一个缩写或简称。请告诉我更多详细信息：",
				"「%s」这个输入比较简短，为了给您准确的答案，请补充一些背景信息：",
			},
			"technical_abbreviation": {
				"「%s」可能是一个技术术语的缩写。请选择您想了解的具体内容：",
				"我发现「%s」可能与技术相关。为了提供准确答案，请明确您的问题：",
			},
			"context_based": {
				"基于我们之前的对话，我猜测您的「%s」可能与「%s」相关。请确认或提供更多信息：",
				"结合上下文，「%s」可能是关于「%s」的问题。请详细说明您想了解什么：",
			},
			"general_help": {
				"我暂时无法理解「%s」的含义。请尝试用更详细的方式描述您的问题：",
				"为了更好地帮助您，请将「%s」扩展为完整的问题或描述：",
			},
		},
		techDomainPrompts: map[string][]string{
			"前端开发": {
				"• 您是想了解某个前端框架的使用方法？",
				"• 需要解决前端开发中的具体问题？",
				"• 想学习前端技术的最佳实践？",
			},
			"后端开发": {
				"• 您是想了解后端架构设计？",
				"• 需要解决API开发的问题？",
				"• 想了解数据库操作相关内容？",
			},
			"数据库": {
				"• 您是想了解数据库查询优化？",
				"• 需要解决数据库设计问题？",
				"• 想学习数据库管理技巧？",
			},
			"AI技术": {
				"• 您是想了解机器学习算法？",
				"• 需要解决AI模型训练问题？",
				"• 想了解自然语言处理技术？",
			},
		},
	}

	// 初始化常见问题模式
	system.initializeCommonPatterns()

	return system
}

// initializeCommonPatterns 初始化常见问题模式
func (igs *IntelligentGuidanceSystem) initializeCommonPatterns() {
	igs.commonPatterns = []GuidancePattern{
		{
			Pattern:  "version_number",
			Keywords: []string{"版本", "v", "version"},
			Suggestions: []string{
				"请说明您想了解哪个软件或框架的版本信息",
				"您是想了解版本升级、兼容性还是新特性？",
			},
			Examples: []string{
				"React 18版本有什么新特性？",
				"Python 3.9和3.8的区别是什么？",
			},
		},
		{
			Pattern:  "error_code",
			Keywords: []string{"错误", "error", "异常", "bug"},
			Suggestions: []string{
				"请描述具体的错误信息或错误代码",
				"您是在什么环境下遇到的这个问题？",
			},
			Examples: []string{
				"MySQL连接错误2003怎么解决？",
				"JavaScript TypeError如何调试？",
			},
		},
		{
			Pattern:  "configuration",
			Keywords: []string{"配置", "设置", "config", "setup"},
			Suggestions: []string{
				"请说明您想配置什么软件或系统",
				"您是想了解初始配置还是高级配置？",
			},
			Examples: []string{
				"如何配置Docker开发环境？",
				"Nginx反向代理怎么设置？",
			},
		},
		{
			Pattern:  "comparison",
			Keywords: []string{"对比", "比较", "区别", "vs", "versus"},
			Suggestions: []string{
				"请明确您想比较的两个或多个技术/工具",
				"您是想了解性能、功能还是使用场景的区别？",
			},
			Examples: []string{
				"React和Vue的区别是什么？",
				"MySQL和PostgreSQL哪个更适合？",
			},
		},
	}
}

// GenerateGuidance 生成智能引导
func (igs *IntelligentGuidanceSystem) GenerateGuidance(input string, context *Conversation, nlpResult *nlp.IntegratedResult) *GuidanceResult {
	logger.Info("🧭 开始生成智能引导: %s", input)

	result := &GuidanceResult{
		Success:         false,
		Suggestions:     []string{},
		Examples:        []string{},
		FollowUpPrompts: []string{},
		Confidence:      0.0,
		Metadata:        make(map[string]interface{}),
	}

	// 1. 分析输入类型
	inputType := igs.analyzeInputType(input, nlpResult)
	result.GuidanceType = inputType
	result.Metadata["input_type"] = inputType

	// 2. 基于输入类型生成引导
	switch inputType {
	case "incomplete_input":
		igs.generateIncompleteInputGuidance(input, result)
	case "technical_abbreviation":
		igs.generateTechnicalAbbreviationGuidance(input, result)
	case "context_based":
		igs.generateContextBasedGuidance(input, context, result)
	case "pattern_based":
		igs.generatePatternBasedGuidance(input, nlpResult, result)
	default:
		igs.generateGeneralGuidance(input, result)
	}

	// 3. 添加通用的后续提示
	igs.addFollowUpPrompts(result)

	// 4. 计算置信度
	result.Confidence = igs.calculateGuidanceConfidence(result)

	if result.Confidence > 0.3 {
		result.Success = true
		logger.Info("✅ 智能引导生成成功，类型: %s, 置信度: %.2f", result.GuidanceType, result.Confidence)
	} else {
		logger.Info("❌ 智能引导生成失败，置信度过低: %.2f", result.Confidence)
	}

	return result
}

// analyzeInputType 分析输入类型
func (igs *IntelligentGuidanceSystem) analyzeInputType(input string, nlpResult *nlp.IntegratedResult) string {
	input = strings.TrimSpace(input)

	// 1. 检查是否是不完整输入
	if len(input) <= 3 || (len(input) <= 15 && isAlphanumericOnly(input)) {
		return "incomplete_input"
	}

	// 2. 检查是否是技术缩写
	if igs.isTechnicalAbbreviation(input) {
		return "technical_abbreviation"
	}

	// 3. 检查是否匹配常见模式
	for _, pattern := range igs.commonPatterns {
		for _, keyword := range pattern.Keywords {
			if strings.Contains(strings.ToLower(input), keyword) {
				return "pattern_based"
			}
		}
	}

	// 4. 如果有NLP结果且关键词较少，可能需要上下文
	if nlpResult != nil && len(nlpResult.Keywords) <= 2 {
		return "context_based"
	}

	return "general"
}

// generateIncompleteInputGuidance 生成不完整输入的引导
func (igs *IntelligentGuidanceSystem) generateIncompleteInputGuidance(input string, result *GuidanceResult) {
	templates := igs.guidanceTemplates["incomplete_input"]
	template := templates[0] // 使用第一个模板

	result.Message = fmt.Sprintf(template, input)

	result.Suggestions = []string{
		"请提供更多背景信息或上下文",
		"说明您想了解的具体内容",
		"描述您遇到的具体问题或需求",
	}

	result.Examples = []string{
		"如果是技术问题，请说明使用的编程语言或框架",
		"如果是错误信息，请提供完整的错误描述",
		"如果是概念询问，请说明想了解的具体方面",
	}
}

// generateTechnicalAbbreviationGuidance 生成技术缩写的引导
func (igs *IntelligentGuidanceSystem) generateTechnicalAbbreviationGuidance(input string, result *GuidanceResult) {
	templates := igs.guidanceTemplates["technical_abbreviation"]
	template := templates[0]

	result.Message = fmt.Sprintf(template, input)

	// 基于缩写提供可能的解释
	possibleMeanings := igs.getPossibleMeanings(input)
	if len(possibleMeanings) > 0 {
		result.Suggestions = append(result.Suggestions, "可能的含义：")
		result.Suggestions = append(result.Suggestions, possibleMeanings...)
	}

	result.Examples = []string{
		fmt.Sprintf("如果「%s」是编程语言，请说明您想了解什么方面", input),
		fmt.Sprintf("如果「%s」是工具或框架，请描述具体的使用场景", input),
	}
}

// generateContextBasedGuidance 生成基于上下文的引导
func (igs *IntelligentGuidanceSystem) generateContextBasedGuidance(input string, context *Conversation, result *GuidanceResult) {
	if context == nil || len(context.Messages) == 0 {
		igs.generateGeneralGuidance(input, result)
		return
	}

	// 分析上下文中的主要话题
	mainTopic := igs.extractMainTopicFromContext(context)
	if mainTopic == "" {
		igs.generateGeneralGuidance(input, result)
		return
	}

	templates := igs.guidanceTemplates["context_based"]
	template := templates[0]

	result.Message = fmt.Sprintf(template, input, mainTopic)

	result.Suggestions = []string{
		fmt.Sprintf("确认是否与「%s」相关", mainTopic),
		"提供更具体的问题描述",
		"说明您想了解的具体方面",
	}

	result.Examples = []string{
		fmt.Sprintf("关于%s的具体实现方法", mainTopic),
		fmt.Sprintf("%s的最佳实践是什么", mainTopic),
	}
}

// generatePatternBasedGuidance 生成基于模式的引导
func (igs *IntelligentGuidanceSystem) generatePatternBasedGuidance(input string, nlpResult *nlp.IntegratedResult, result *GuidanceResult) {
	inputLower := strings.ToLower(input)

	for _, pattern := range igs.commonPatterns {
		for _, keyword := range pattern.Keywords {
			if strings.Contains(inputLower, keyword) {
				result.Message = fmt.Sprintf("我发现您的问题可能与「%s」相关。", pattern.Pattern)
				result.Suggestions = pattern.Suggestions
				result.Examples = pattern.Examples
				result.Metadata["matched_pattern"] = pattern.Pattern
				return
			}
		}
	}

	igs.generateGeneralGuidance(input, result)
}

// generateGeneralGuidance 生成通用引导
func (igs *IntelligentGuidanceSystem) generateGeneralGuidance(input string, result *GuidanceResult) {
	templates := igs.guidanceTemplates["general_help"]
	template := templates[0]

	result.Message = fmt.Sprintf(template, input)

	result.Suggestions = []string{
		"请用完整的句子描述您的问题",
		"提供相关的技术背景或使用场景",
		"说明您期望得到什么样的帮助",
	}

	result.Examples = []string{
		"如何使用React创建一个组件？",
		"Python中如何处理JSON数据？",
		"MySQL数据库连接超时怎么解决？",
	}
}

// addFollowUpPrompts 添加后续提示
func (igs *IntelligentGuidanceSystem) addFollowUpPrompts(result *GuidanceResult) {
	result.FollowUpPrompts = []string{
		"💡 您也可以尝试：",
		"• 描述具体的使用场景",
		"• 提供相关的错误信息",
		"• 说明您的技术背景",
		"• 询问相关的最佳实践",
	}
}

// calculateGuidanceConfidence 计算引导置信度
func (igs *IntelligentGuidanceSystem) calculateGuidanceConfidence(result *GuidanceResult) float64 {
	confidence := 0.0

	// 基础置信度
	if result.Message != "" {
		confidence += 0.3
	}

	if len(result.Suggestions) > 0 {
		confidence += 0.2
	}

	if len(result.Examples) > 0 {
		confidence += 0.2
	}

	// 基于引导类型调整
	switch result.GuidanceType {
	case "incomplete_input":
		confidence += 0.2
	case "technical_abbreviation":
		confidence += 0.15
	case "context_based":
		confidence += 0.25
	case "pattern_based":
		confidence += 0.2
	default:
		confidence += 0.1
	}

	if confidence > 1.0 {
		confidence = 1.0
	}

	return confidence
}

// isTechnicalAbbreviation 检查是否是技术缩写
func (igs *IntelligentGuidanceSystem) isTechnicalAbbreviation(text string) bool {
	textLower := strings.ToLower(text)

	// 常见技术缩写
	abbreviations := []string{
		"js", "ts", "py", "go", "db", "api", "ui", "css", "html",
		"sql", "orm", "mvc", "spa", "pwa", "cdn", "dns", "http",
		"tcp", "udp", "ssh", "ftp", "git", "svn", "ide", "sdk",
		"aws", "gcp", "k8s", "ci", "cd", "devops", "ai", "ml",
	}

	for _, abbr := range abbreviations {
		if textLower == abbr {
			return true
		}
	}

	// 检查是否是版本号模式 (如 v1.0, 2.0, etc.)
	if len(text) <= 5 && (strings.HasPrefix(textLower, "v") ||
		strings.Contains(text, ".") && isVersionLike(text)) {
		return true
	}

	return false
}

// getPossibleMeanings 获取可能的含义
func (igs *IntelligentGuidanceSystem) getPossibleMeanings(abbreviation string) []string {
	abbr := strings.ToLower(abbreviation)

	meanings := map[string][]string{
		"js":   {"JavaScript编程语言", "前端开发技术"},
		"ts":   {"TypeScript编程语言", "JavaScript的超集"},
		"py":   {"Python编程语言", "数据科学和AI开发"},
		"go":   {"Go编程语言", "Google开发的后端语言"},
		"api":  {"应用程序接口", "系统间通信协议"},
		"ui":   {"用户界面", "前端界面设计"},
		"db":   {"数据库", "数据存储系统"},
		"sql":  {"结构化查询语言", "数据库查询语言"},
		"html": {"超文本标记语言", "网页结构语言"},
		"css":  {"层叠样式表", "网页样式设计"},
		"http": {"超文本传输协议", "网络通信协议"},
		"tcp":  {"传输控制协议", "网络传输协议"},
		"git":  {"版本控制系统", "代码管理工具"},
		"aws":  {"亚马逊云服务", "云计算平台"},
		"ai":   {"人工智能", "机器学习技术"},
		"ml":   {"机器学习", "AI算法技术"},
	}

	if meanings[abbr] != nil {
		return meanings[abbr]
	}

	return []string{}
}

// extractMainTopicFromContext 从上下文中提取主要话题
func (igs *IntelligentGuidanceSystem) extractMainTopicFromContext(context *Conversation) string {
	if context == nil || len(context.Messages) == 0 {
		return ""
	}

	// 分析最近几条消息
	recentMessages := context.Messages
	if len(recentMessages) > 5 {
		recentMessages = recentMessages[len(recentMessages)-5:]
	}

	// 提取关键词
	keywords := make(map[string]int)
	for _, msg := range recentMessages {
		if msg.Role == "user" {
			// 简单的关键词提取
			words := strings.Fields(strings.ToLower(msg.Content))
			for _, word := range words {
				if len(word) > 2 && !isStopWord(word) {
					keywords[word]++
				}
			}
		}
	}

	// 找出出现频率最高的关键词
	maxCount := 0
	mainTopic := ""
	for word, count := range keywords {
		if count > maxCount {
			maxCount = count
			mainTopic = word
		}
	}

	return mainTopic
}

// isVersionLike 检查是否像版本号
func isVersionLike(text string) bool {
	// 简单的版本号模式检查
	parts := strings.Split(text, ".")
	if len(parts) < 2 {
		return false
	}

	for _, part := range parts {
		if part == "" {
			return false
		}
		// 检查是否包含数字
		hasDigit := false
		for _, char := range part {
			if char >= '0' && char <= '9' {
				hasDigit = true
				break
			}
		}
		if !hasDigit {
			return false
		}
	}

	return true
}

// isStopWord 检查是否是停用词
func isStopWord(word string) bool {
	stopWords := []string{
		"的", "是", "在", "有", "和", "与", "或", "但", "如果", "因为",
		"所以", "这", "那", "什么", "怎么", "为什么", "如何", "哪里",
		"when", "where", "what", "how", "why", "the", "a", "an", "and",
		"or", "but", "if", "then", "this", "that", "is", "are", "was",
	}

	for _, stopWord := range stopWords {
		if word == stopWord {
			return true
		}
	}

	return false
}
